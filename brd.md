﻿**Requirement and Solution Design Document**

1. **BUSINESS/INTEGRATION REQUIREMENT** 

This section should cover for Business and Integration functional and non-functional requirements. As the case may be, functional business requirements should be captured in simple clear statement. Since not all projects would require integration requirements, but where and when applicable and as Data sharing and movement is expected between systems, applications or platform in real time or batches this section should cover for all.  

<a name="_heading=h.tj3x0ahmkp6w"></a>[SNBC screenshots - Miro](https://miro.com/app/board/uXjVIwNo90s=/)

1. <a name="_heading=h.o5yqtdwdxpta"></a>**FUNCTIONAL REQUIREMENT**
   1. **MY PORTFOLIOS** 
- To access this section customer must be have an active account on SNBC 
  1. <a name="_heading=h.2uu7sbhglgk7"></a>  **VIEW PORTFOLIOS LIST**
- System displays list with all customer’s “ACTIVE” TADAWUL Portfolios 
- For each portfolio display:
  - <a name="_heading=h.59f43v428ztt"></a>Portfolio Title
    1. **VIEW PORTFOLIO DETAILS** 
- Customer will be able to choose any of the existing portfolios to view its details:
  - Portfolio Title
  - Total Portfolio Value 
  - Market Value 
  - Cost Value
  - Buying Power
  - Total Cash
  - Cash for Fund Out
  - Unsettled Cash
  - P/L of the day
  - Blocked Amount
  - UPL
  - UPL %
  - Ch. Previous Day
  - <a name="_heading=h.jzq5zwpxrtkg"></a>Ch. Previous Day %
    1. **VIEW PORTFOLIO HOLDINGS**
- For each portfolio system displays list of holdings existing in the portfolio, for each holding system displays:
  - Stock Code
  - Stock Name
  - Quantity 
  - Last Trade Price
  - <a name="_heading=h.nbj25nhj1928"></a>Realized Profit/Loss of the day
    1. <a name="_heading=h.dgni5i2w00wy"></a>**CASH TRANSACTIONS**
       1. **BETWEEN PORTFOLIOS**
- Customer chooses to transfer funds between any 2 of his portfolios
  - ` `“From Portfolio”
    - System displays a list of all active customer’s Tadawul portfolios 
    - With each portfolio system displays the “Available Balance” of the portfolio
    - Customer chooses one of these portfolios to transfer Cash From
    - Customer will not be able to choose portfolio with Zero Balance 
  - “To Portfolio”
    - System displays a list of all active customer’s Tadawul portfolios 
    - With each portfolio system displays the “Available Balance” of the portfolio
    - Customer chooses one of these portfolios to transfer Cash To
    - From and To Portfolios cannot be the same 
  - Amount (SAR)
    - Customer defines amount he want to transfer 
    - Minimum amount is 1 SAR
- Confirmation
  - When the customer confirms, the system displays a confirmation screen with the information below.
    - From Portfolio
    - To Portfolio
    - Amount
    - Fees (if exist)
    - VAT (if exist)
    - Net Amount (amount (-) fees (-) VAT
  - Customer chooses either to confirm or cancel 
- Validations 
  - System validates that there is enough balance in the source portfolio 
    - If there is no enough balance, system displays message to customer acknowledging him that there is no enough balance in source portfolio
  - <a name="_heading=h.40gxks6ejntl"></a>If transfer is done successfully, system displays message to customer acknowledging him that transfer is done successfully
    1. **FROM ACCOUNT TO PORTFOLIO**
- Customer chooses to transfer cash from any of his NEO SAR Accounts to any of his existing active Tadawul portfolios 
  - From Account
    - System displays list of all customer’s Active NEO SAR Accounts
    - For each account system displays “Account Number” 
  - To Portfolio
    - System displays a list of all active customer’s Tadawul portfolios 
    - With each portfolio system displays the “Available Balance” of the portfolio
    - Customer chooses one of these portfolios to transfer Cash To
  - Amount (SAR)
    - Minimum amount = 1 SAR
  - Confirmation 
    - The system displays confirmation screen with below information
      - From account
      - To Portfolio
      - Amount
      - Fees (if exist)
      - VAT (if exist)
      - Net Amount: Amount (minus) fees (minus) VAT
  - Validations 
    - System validates that there is enough balance in the source portfolio 
      - If there is no enough balance, system displays message to customer acknowledging him that there is no enough balance in source portfolio
    - If transfer is done successfully, system displays message to customer acknowledging him that transfer is done successfully

1. <a name="_heading=h.rto9ybnt8hos"></a>**FROM PORTFOLIO TO ACCOUNT**

- Customer chooses to transfer funds from any of his “Active” portfolios to any of his Neo SAR bank accounts 
  - From Portfolio 
    - System displays a list of all active customer’s Tadawul portfolios 
    - With each portfolio system displays the “Available Balance” of the portfolio
    - Customer chooses one of these portfolios to transfer Cash From
    - Customer will not be able to choose portfolio with Zero Balance 
  - To Account 
    - <a name="_heading=h.ownouyfpyu24"></a>System displays list of all customer’s Active NEO SAR Accounts
      1. **FOR EACH ACCOUNT SYSTEM DISPLAYS “ACCOUNT NUMBER”** 
  - Amount (SAR)
    - Minimum amount = 1 SAR
  - Confirmation 
    - The system displays confirmation screen with below information
      - From Portfolio
      - To Account
      - Amount
      - Fees (if exist)
      - VAT (if exist)
      - Net Amount: Amount (minus) fees (minus) VAT
  - Validations 
    - The system validates that there is enough balance in the source account 
      - If there is no enough balance, the system displays message to customer acknowledging him that there is no enough balance in source account
    - If transfer is done successfully, system displays message to customer acknowledging him that transfer is done successfully

1. <a name="_heading=h.nqyba7y3oait"></a><a name="_heading=h.fx8ykycs0b0k"></a>**IPO**
   1. **IPO LISTINGS AND DETAILS**
- List of current and upcoming IPOs.
- IPO prospectus access (PDF viewer or download).
- Display key IPO details:
  - Company name
  - Sector
  - Price per share
  - Subscription period
  - Allocation date
  - <a name="_heading=h.8dv24ysv0t7t"></a>Minimum & maximum subscription limits
    1. **CUSTOMER ELIGIBILITY CHECK**
- Verify if the customer is eligible to subscribe.
- KYC and AML checks.

1. <a name="_heading=h.2mhkwu7x64a3"></a>**SUBSCRIPTION PROCESS**
- Real-time subscription form.
- Select number of shares.
- Auto-calculation of total amount.
- Selection of payment method:
  - SNB account
  - <a name="_heading=h.kqxobwvwkvux"></a>IPO funds transfer
    1. **REAL-TIME INTEGRATION WITH TADAWUL / DEPOSITORY**
- Integration to submit IPO orders directly to the market.
- <a name="_heading=h.71iwmo9rgkvf"></a>Receive confirmation and updates on subscription status.
  1. **STATUS TRACKING & NOTIFICATIONS**
- View subscription status:
  - Pending
  - Successful
  - Allocated shares
  - Refund issued (if applicable)
- SMS / Email alerts for:
  - Subscription success
  - Allocation confirmation
  - <a name="_heading=h.h4ktxihcuvv0"></a>Refund transfer
    1. **PAYMENT & REFUND MANAGEMENT**
- Link to SNB Capital wallet or SNB bank account.
- Real-time debiting of funds.
- Refund of excess or unallocated funds post allocation.

1. <a name="_heading=h.u0c9ili5mt2i"></a>**HISTORICAL IPO SUBSCRIPTIONS**
- Past IPO participation records.
- Downloadable or exportable reports (PDF/Excel).
- Allocation and profit/loss tracking (if shares still held).



