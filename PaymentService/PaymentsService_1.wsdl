<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions name="PaymentsService" targetNamespace="http://corp.alahli.com/middlewareservices/payment/1.0/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://corp.alahli.com/middlewareservices/payment/1.0/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:header="http://corp.alahli.com/middlewareservices/header/1.0/">
  <wsdl:types>
    <xsd:schema>
      <xsd:import namespace="http://corp.alahli.com/middlewareservices/payment/1.0/" schemaLocation="PaymentsService.xsd"/>
      <xsd:import namespace="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" schemaLocation="securityheader.xsd"/>
      <xsd:import namespace="http://corp.alahli.com/middlewareservices/header/1.0/" schemaLocation="header.xsd"/>
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="FundTransferRequest">
    <wsdl:part element="tns:FundTransferRequest" name="body"/>
  </wsdl:message>
  <wsdl:message name="FundTransferResponse">
    <wsdl:part element="tns:FundTransferResponse" name="body"/>
  </wsdl:message>
  <wsdl:message name="PayToBeneficiaryRequest">
    <wsdl:part element="tns:PayToBeneficiaryRequest" name="body"/>
  </wsdl:message>
  <wsdl:message name="PayToBeneficiaryResponse">
    <wsdl:part element="tns:PayToBeneficiaryResponse" name="body"/>
  </wsdl:message>
  <wsdl:message name="PayToBeneficiaryWithFXDealRequest">
    <wsdl:part element="tns:PayToBeneficiaryWithFXDealRequest" name="body"/>
  </wsdl:message>
  <wsdl:message name="PayToBeneficiaryWithFXDealResponse">
    <wsdl:part element="tns:PayToBeneficiaryWithFXDealResponse" name="body"/>
  </wsdl:message>
  <wsdl:message name="BranchPaymentRequest">
    <wsdl:part element="tns:BranchPaymentRequest" name="body"/>
  </wsdl:message>
  <wsdl:message name="BranchPaymentResponse">
    <wsdl:part element="tns:BranchPaymentResponse" name="body"/>
  </wsdl:message>
  <wsdl:message name="HoldAndReleaseRequest">
    <wsdl:part element="tns:HoldAndReleaseRequest" name="body"/>
  </wsdl:message>
  <wsdl:message name="HoldAndReleaseResponse">
    <wsdl:part element="tns:HoldAndReleaseResponse" name="body"/>
  </wsdl:message>
  <wsdl:message name="AccountTransferRequest">
    <wsdl:part element="tns:AccountTransferRequest" name="body"/>
  </wsdl:message>
  <wsdl:message name="AccountTransferResponse">
    <wsdl:part element="tns:AccountTransferResponse" name="body"/>
  </wsdl:message>
  <!--<wsdl:message name="AccountingEntryFromIBASRequest">
        <wsdl:part element="tns:AccountingEntryFromIBASRequest"
        	name="body" />
    </wsdl:message>
    <wsdl:message name="AccountingEntryFromIBASResponse">
        <wsdl:part element="tns:AccountingEntryFromIBASResponse"
        	name="body" />
    </wsdl:message>-->
  <wsdl:message name="SendPaymentRequest">
    <wsdl:part element="tns:SendPaymentRequest" name="body"/>
  </wsdl:message>
  <wsdl:message name="SendPaymentResponse">
    <wsdl:part element="tns:SendPaymentResponse" name="body"/>
  </wsdl:message>
  <wsdl:message name="GLToGLTransferRequest">
    <wsdl:part element="tns:GLToGLTransferRequest" name="body"/>
  </wsdl:message>
  <wsdl:message name="GLToGLTransferResponse">
    <wsdl:part element="tns:GLToGLTransferResponse" name="body"/>
  </wsdl:message>
  <wsdl:message name="GLToDepositTransferRequest">
    <wsdl:part element="tns:GLToDepositTransferRequest" name="body"/>
  </wsdl:message>
  <wsdl:message name="GLToDepositTransferResponse">
    <wsdl:part element="tns:GLToDepositTransferResponse" name="body"/>
  </wsdl:message>
  <wsdl:message name="DepositToGLTransferRequest">
    <wsdl:part element="tns:DepositToGLTransferRequest" name="body"/>
  </wsdl:message>
  <wsdl:message name="DepositToGLTransferResponse">
    <wsdl:part element="tns:DepositToGLTransferResponse" name="body"/>
  </wsdl:message>
  <wsdl:message name="OutgoingSWIFTTransferRequest">
    <wsdl:part element="tns:OutgoingSWIFTTransferRequest" name="body"/>
  </wsdl:message>
  <wsdl:message name="OutgoingSWIFTTransferResponse">
    <wsdl:part element="tns:OutgoingSWIFTTransferResponse" name="body"/>
  </wsdl:message>
  <wsdl:message name="InSWIFTOutgoingSARIETransferRequest">
    <wsdl:part element="tns:InSWIFTOutgoingSARIETransferRequest" name="body"/>
  </wsdl:message>
  <wsdl:message name="InSWIFTOutgoingSARIETransferResponse">
    <wsdl:part element="tns:InSWIFTOutgoingSARIETransferResponse" name="body"/>
  </wsdl:message>
  <wsdl:message name="ServiceHeader">
    <wsdl:part name="header" element="header:ServiceHeader"/>
  </wsdl:message>
  <wsdl:message name="Security" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
    <wsdl:part name="header" element="wsse:Security"/>
  </wsdl:message>
  <wsdl:message name="FeesRequest">
    <wsdl:part name="body" element="tns:FeesRequest"/>
  </wsdl:message>
  <wsdl:message name="FeesResponse">
    <wsdl:part name="body" element="tns:FeesResponse"/>
  </wsdl:message>
  <wsdl:message name="IntraTransferRequest">
    <wsdl:part name="body" element="tns:IntraTransferRequest"/>
  </wsdl:message>
  <wsdl:message name="IntraTransferResponse">
    <wsdl:part name="body" element="tns:IntraTransferResponse"/>
  </wsdl:message>
  <wsdl:message name="InterTransferRequest">
    <wsdl:part name="body" element="tns:InterTransferRequest"/>
  </wsdl:message>
  <wsdl:message name="InterTransferResponse">
    <wsdl:part name="body" element="tns:InterTransferResponse"/>
  </wsdl:message>
  <wsdl:message name="FundInTransferRequest">
    <wsdl:part name="parameters" element="tns:FundInTransferRequest"/>
  </wsdl:message>
  <wsdl:message name="FundInTransferResponse">
    <wsdl:part name="parameters" element="tns:FundInTransferResponse"/>
  </wsdl:message>
  <wsdl:message name="ScreenTransferRequest">
    <wsdl:part name="body" element="tns:ScreenTransferRequest"/>
  </wsdl:message>
  <wsdl:message name="ScreenTransferResponse">
    <wsdl:part name="body" element="tns:ScreenTransferResponse"/>
  </wsdl:message>
  <wsdl:message name="MonitorForScreeningFeedbackRequest">
    <wsdl:part name="body" element="tns:MonitorForScreeningRequest"/>
  </wsdl:message>
  <wsdl:message name="MonitorForScreeningFeedbackResponse">
    <wsdl:part name="body" element="tns:MonitorForScreeningResponse"/>
  </wsdl:message>
  <wsdl:message name="FundOutRequestForwarderRequest">
    <wsdl:part name="body" element="tns:FundOutRequestForwarderRequest"/>
  </wsdl:message>
  <wsdl:message name="FundOutRequestForwarderResponse">
    <wsdl:part name="body" element="tns:FundOutRequestForwarderResponse"/>
  </wsdl:message>
  <wsdl:message name="IntraBeneficiaryTransferRequest">
    <wsdl:part name="body" element="tns:IntraBeneficiaryTransferRequest"/>
  </wsdl:message>
  <wsdl:message name="IntraBeneficiaryTransferResponse">
    <wsdl:part name="body" element="tns:IntraBeneficiaryTransferResponse"/>
  </wsdl:message>
  <wsdl:message name="DepositAccountCreditRequest">
    <wsdl:part name="body" element="tns:DepositAccountCreditRequest"/>
  </wsdl:message>
  <wsdl:message name="DepositAccountCreditResponse">
    <wsdl:part name="body" element="tns:DepositAccountCreditResponse"/>
  </wsdl:message>
  <wsdl:message name="DepositAccountDebitRequest">
    <wsdl:part name="body" element="tns:DepositAccountDebitRequest"/>
  </wsdl:message>
  <wsdl:message name="DepositAccountDebitResponse">
    <wsdl:part name="body" element="tns:DepositAccountDebitResponse"/>
  </wsdl:message>
  <wsdl:message name="GLAccountCreditRequest">
    <wsdl:part name="body" element="tns:GLAccountCreditRequest"/>
  </wsdl:message>
  <wsdl:message name="GLAccountCreditResponse">
    <wsdl:part name="body" element="tns:GLAccountCreditResponse"/>
  </wsdl:message>
  <wsdl:message name="GLAccountDebitRequest">
    <wsdl:part name="body" element="tns:GLAccountDebitRequest"/>
  </wsdl:message>
  <wsdl:message name="GLAccountDebitResponse">
    <wsdl:part name="body" element="tns:GLAccountDebitResponse"/>
  </wsdl:message>
  <wsdl:message name="GoldWithdrawlRequest">
    <wsdl:part name="body" element="tns:GoldWithdrawlRequest"/>
  </wsdl:message>
  <wsdl:message name="GoldWithdrawlResponse">
    <wsdl:part name="body" element="tns:GoldWithdrawlResponse"/>
  </wsdl:message>
  <wsdl:message name="TransferFundToBeneficiaryRequest">
    <wsdl:part name="body" element="tns:TransferFundToBeneficiaryRequest"/>
  </wsdl:message>
  <wsdl:message name="TransferFundToBeneficiaryResponse">
    <wsdl:part name="body" element="tns:TransferFundToBeneficiaryResponse"/>
  </wsdl:message>
  <wsdl:message name="InterBankTransactionInquiryRequest">
    <wsdl:part name="body" element="tns:InterBankTransactionInquiryRequest"/>
  </wsdl:message>
  <wsdl:message name="InterBankTransactionInquiryResponse">
    <wsdl:part name="body" element="tns:InterBankTransactionInquiryResponse"/>
  </wsdl:message>
  <wsdl:message name="InterBankCancelledTransactionsInquiryRequest">
    <wsdl:part name="body" element="tns:InterBankCancelledTransactionsInquiryRequest"/>
  </wsdl:message>
  <wsdl:message name="InterBankCancelledTransactionsInquiryResponse">
    <wsdl:part name="body" element="tns:InterBankCancelledTransactionsInquiryResponse"/>
  </wsdl:message>
  <wsdl:message name="InterBankTransactionCancellationRequest">
    <wsdl:part name="body" element="tns:InterBankTransactionCancellationRequest"/>
  </wsdl:message>
  <wsdl:message name="InterBankTransactionCancellationResponse">
    <wsdl:part name="body" element="tns:InterBankTransactionCancellationResponse"/>
  </wsdl:message>
  <wsdl:message name="ValueDateAndCutOffTimeRequest">
    <wsdl:part name="body" element="tns:ValueDateAndCutOffTimeRequest"/>
  </wsdl:message>
  <wsdl:message name="ValueDateAndCutOffTimeResponse">
    <wsdl:part name="body" element="tns:ValueDateAndCutOffTimeResponse"/>
  </wsdl:message>
  <wsdl:message name="TrackSwiftGPIPaymentRequest">
    <wsdl:part name="body" element="tns:TrackSwiftGPIPaymentRequest"/>
  </wsdl:message>
  <wsdl:message name="TrackSwiftGPIPaymentResponse">
    <wsdl:part name="body" element="tns:TrackSwiftGPIPaymentResponse"/>
  </wsdl:message>
  <wsdl:message name="MT103MessageRequest">
    <wsdl:part name="body" element="tns:MT103MessageRequest"/>
  </wsdl:message>
  <wsdl:message name="MT103MessageResponse">
    <wsdl:part name="body" element="tns:MT103MessageResponse"/>
  </wsdl:message>
  <wsdl:message name="ManageRTPaymentsRequest">
    <wsdl:part name="body" element="tns:ManageRTPaymentsRequest"/>
  </wsdl:message>
  <wsdl:message name="ManageRTPaymentsResponse">
    <wsdl:part name="body" element="tns:ManageRTPaymentsResponse"/>
  </wsdl:message>
  <wsdl:message name="GetRTPaymentTransactionsRequest">
    <wsdl:part name="body" element="tns:GetRTPaymentTransactionsRequest"/>
  </wsdl:message>
  <wsdl:message name="GetRTPaymentTransactionsResponse">
    <wsdl:part name="body" element="tns:GetRTPaymentTransactionsResponse"/>
  </wsdl:message>
  <wsdl:message name="InquireProxyDetailRequest">
    <wsdl:part name="body" element="tns:InquireProxyDetailRequest"/>
  </wsdl:message>
  <wsdl:message name="InquireProxyDetailResponse">
    <wsdl:part name="body" element="tns:InquireProxyDetailResponse"/>
  </wsdl:message>
  <wsdl:message name="FetchRTParticipantBanksRequest">
    <wsdl:part name="body" element="tns:FetchRTParticipantBanksRequest"/>
  </wsdl:message>
  <wsdl:message name="FetchRTParticipantBanksResponse">
    <wsdl:part name="body" element="tns:FetchRTParticipantBanksResponse"/>
  </wsdl:message>
  <wsdl:message name="ManageProxyRequest">
    <wsdl:part name="body" element="tns:ManageProxyRequest"/>
  </wsdl:message>
  <wsdl:message name="ManageProxyResponse">
    <wsdl:part name="body" element="tns:ManageProxyResponse"/>
  </wsdl:message>
  <wsdl:message name="ProxyLookupRequest">
    <wsdl:part name="body" element="tns:ProxyLookupRequest"/>
  </wsdl:message>
  <wsdl:message name="ProxyLookupResponse">
    <wsdl:part name="body" element="tns:ProxyLookupResponse"/>
  </wsdl:message>
  <wsdl:message name="GetRTPPaymentTypeRequest">
    <wsdl:part name="body" element="tns:GetRTPPaymentTypeRequest"/>
  </wsdl:message>
  <wsdl:message name="GetRTPPaymentTypeResponse">
    <wsdl:part name="body" element="tns:GetRTPPaymentTypeResponse"/>
  </wsdl:message>
  <wsdl:message name="FetchRTPLimitRequest">
    <wsdl:part name="body" element="tns:FetchRTPLimitRequest"/>
  </wsdl:message>
  <wsdl:message name="FetchRTPLimitResponse">
    <wsdl:part name="body" element="tns:FetchRTPLimitResponse"/>
  </wsdl:message>
  <wsdl:message name="MaintainQTLLimitRequest">
    <wsdl:part name="body" element="tns:MaintainQTLLimitRequest"/>
  </wsdl:message>
  <wsdl:message name="MaintainQTLLimitResponse">
    <wsdl:part name="body" element="tns:MaintainQTLLimitResponse"/>
  </wsdl:message>
  <wsdl:message name="MultiDebitMultiCreditRequest">
    <wsdl:part name="body" element="tns:MultiDebitMultiCreditRequest"/>
  </wsdl:message>
  <wsdl:message name="MultiDebitMultiCreditResponse">
    <wsdl:part name="body" element="tns:MultiDebitMultiCreditResponse"/>
  </wsdl:message>
  <wsdl:message name="ValidateBenAccountRTPRequest">
    <wsdl:part name="body" element="tns:ValidateBenAccountRTPRequest"/>
  </wsdl:message>
  <wsdl:message name="ValidateBenAccountRTPResponse">
    <wsdl:part name="body" element="tns:ValidateBenAccountRTPResponse"/>
  </wsdl:message>
  <wsdl:message name="ActOnRequestToPayRTPRequest">
    <wsdl:part name="body" element="tns:ActOnRequestToPayRTPRequest"/>
  </wsdl:message>
  <wsdl:message name="ActOnRequestToPayRTPResponse">
    <wsdl:part name="body" element="tns:ActOnRequestToPayRTPResponse"/>
  </wsdl:message>
  <wsdl:message name="InquireRequestToPayRTPRequest">
    <wsdl:part name="body" element="tns:InquireRequestToPayRTPRequest"/>
  </wsdl:message>
  <wsdl:message name="InquireRequestToPayRTPResponse">
    <wsdl:part name="body" element="tns:InquireRequestToPayRTPResponse"/>
  </wsdl:message>
  <wsdl:message name="InquireRTPProductTypesRequest">
    <wsdl:part name="body" element="tns:InquireRTPProductTypesRequest"/>
  </wsdl:message>
  <wsdl:message name="InquireRTPProductTypesResponse">
    <wsdl:part name="body" element="tns:InquireRTPProductTypesResponse"/>
  </wsdl:message>
  <wsdl:message name="RequestToPayRTPRequest">
    <wsdl:part name="body" element="tns:RequestToPayRTPRequest"/>
  </wsdl:message>
  <wsdl:message name="RequestToPayRTPResponse">
    <wsdl:part name="body" element="tns:RequestToPayRTPResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateRTPStatusRequest">
    <wsdl:part name="body" element="tns:UpdateRTPStatusRequest"/>
  </wsdl:message>
  <wsdl:message name="UpdateRTPStatusResponse">
    <wsdl:part name="body" element="tns:UpdateRTPStatusResponse"/>
  </wsdl:message>
  <wsdl:message name="CreditContingentAccountRequest">
    <wsdl:part name="body" element="tns:CreditContingentAccountRequest"/>
  </wsdl:message>
  <wsdl:message name="CreditContingentAccountResponse">
    <wsdl:part name="body" element="tns:CreditContingentAccountResponse"/>
  </wsdl:message>
  <wsdl:message name="DebitContingentAccountRequest">
    <wsdl:part name="body" element="tns:DebitContingentAccountRequest"/>
  </wsdl:message>
  <wsdl:message name="DebitContingentAccountResponse">
    <wsdl:part name="body" element="tns:DebitContingentAccountResponse"/>
  </wsdl:message>
  <wsdl:message name="TransferFundToCharityRequest">
    <wsdl:part name="body" element="tns:TransferFundToCharityRequest"/>
  </wsdl:message>
  <wsdl:message name="TransferFundToCharityResponse">
    <wsdl:part name="body" element="tns:TransferFundToCharityResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAvailableRitualsRequest">
    <wsdl:part name="body" element="tns:GetAvailableRitualsRequest"/>
  </wsdl:message>
  <wsdl:message name="GetAvailableRitualsResponse">
    <wsdl:part name="body" element="tns:GetAvailableRitualsResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAvailableProductRequest">
    <wsdl:part name="body" element="tns:GetAvailableProductRequest"/>
  </wsdl:message>
  <wsdl:message name="GetAvailableProductResponse">
    <wsdl:part name="body" element="tns:GetAvailableProductResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAdahiCountriesRequest">
    <wsdl:part name="body" element="tns:GetAdahiCountriesRequest"/>
  </wsdl:message>
  <wsdl:message name="GetAdahiCountriesResponse">
    <wsdl:part name="body" element="tns:GetAdahiCountriesResponse"/>
  </wsdl:message>
  <wsdl:message name="SubmitAdahiOrderRequest">
    <wsdl:part name="body" element="tns:SubmitAdahiOrderRequest"/>
  </wsdl:message>
  <wsdl:message name="SubmitAdahiOrderResponse">
    <wsdl:part name="body" element="tns:SubmitAdahiOrderResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateCouponStatusRequest">
    <wsdl:part name="body" element="tns:UpdateCouponStatusRequest"/>
  </wsdl:message>
  <wsdl:message name="UpdateCouponStatusResponse">
    <wsdl:part name="body" element="tns:UpdateCouponStatusResponse"/>
  </wsdl:message>
  <wsdl:portType name="PaymentsService">
    <wsdl:operation name="TransferFund">
      <wsdl:documentation>This operation transfers fund between own or third party intra bank accounts for single currency and cross currency.</wsdl:documentation>
      <wsdl:input message="tns:FundTransferRequest"/>
      <wsdl:output message="tns:FundTransferResponse"/>
    </wsdl:operation>
    <!--<wsdl:operation name="PayToBeneficiary">
			<wsdl:documentation>
				This operation do domestic (SARIE) or International (SWIFT) payment for single currency.
			</wsdl:documentation>			
			<wsdl:input message="tns:PayToBeneficiaryRequest" />
			<wsdl:output message="tns:PayToBeneficiaryResponse" />
		</wsdl:operation>-->
    <wsdl:operation name="PayAtBranch">
      <wsdl:documentation>This operation do payment by user from branch which will invoke 21016 BaNCS transaction.</wsdl:documentation>
      <wsdl:input message="tns:BranchPaymentRequest"/>
      <wsdl:output message="tns:BranchPaymentResponse"/>
    </wsdl:operation>
    <wsdl:operation name="HoldAndRelease">
      <wsdl:documentation>This operation will consolidate amount available in payroll file.</wsdl:documentation>
      <wsdl:input message="tns:HoldAndReleaseRequest"/>
      <wsdl:output message="tns:HoldAndReleaseResponse"/>
    </wsdl:operation>
    <wsdl:operation name="AccountTransfer">
      <wsdl:documentation>This operation Transfer money from one account to another except IBAS channel.</wsdl:documentation>
      <wsdl:input message="tns:AccountTransferRequest"/>
      <wsdl:output message="tns:AccountTransferResponse"/>
    </wsdl:operation>
    <!-->wsdl:operation name="AccountingEntryFromIBAS">
			<wsdl:documentation>
				This operation Transfer money from one account to another for IBAS channel.
			</wsdl:documentation>			
			<wsdl:input message="tns:AccountingEntryFromIBASRequest" />
			<wsdl:output message="tns:AccountingEntryFromIBASResponse" />
		</wsdl:operation>
		<wsdl:operation name="SendPayment">
			<wsdl:documentation>
				This operation will initiate payment for outgoing swift transfer through payment engine.
			</wsdl:documentation>			
			<wsdl:input message="tns:SendPaymentRequest" />
			<wsdl:output message="tns:SendPaymentResponse" />
		</wsdl:operation-->
    <wsdl:operation name="TransferFundGLToGL">
      <wsdl:documentation>This operation will transfer amount from GL account to another GL account.</wsdl:documentation>
      <wsdl:input message="tns:GLToGLTransferRequest"/>
      <wsdl:output message="tns:GLToGLTransferResponse"/>
    </wsdl:operation>
    <wsdl:operation name="TransferFundGLToDeposit">
      <wsdl:documentation>This operation will transfer amount from GL account to deposit account.</wsdl:documentation>
      <wsdl:input message="tns:GLToDepositTransferRequest"/>
      <wsdl:output message="tns:GLToDepositTransferResponse"/>
    </wsdl:operation>
    <wsdl:operation name="TransferFundDepositToGL">
      <wsdl:documentation>This operation will transfer amount from deposit account to GL account.</wsdl:documentation>
      <wsdl:input message="tns:DepositToGLTransferRequest"/>
      <wsdl:output message="tns:DepositToGLTransferResponse"/>
    </wsdl:operation>
    <wsdl:operation name="TransferFundSWIFT">
      <wsdl:documentation>This operation will do out SWIFT transfer.</wsdl:documentation>
      <wsdl:input message="tns:OutgoingSWIFTTransferRequest"/>
      <wsdl:output message="tns:OutgoingSWIFTTransferResponse"/>
    </wsdl:operation>
    <wsdl:operation name="TransferFundSARIE">
      <wsdl:documentation>This operation will do In SWIFT and Out SARIE transfer</wsdl:documentation>
      <wsdl:input message="tns:InSWIFTOutgoingSARIETransferRequest"/>
      <wsdl:output message="tns:InSWIFTOutgoingSARIETransferResponse"/>
    </wsdl:operation>
    <!--<wsdl:operation name="GetFees">
			<wsdl:input message="tns:FeesRequest"></wsdl:input>
			<wsdl:output message="tns:FeesResponse"></wsdl:output>
		</wsdl:operation>-->
    <wsdl:operation name="IntraBankFundTransfer">
      <wsdl:input message="tns:IntraTransferRequest"/>
      <wsdl:output message="tns:IntraTransferResponse"/>
    </wsdl:operation>
    <wsdl:operation name="InterBankFundTransfer">
      <wsdl:input message="tns:InterTransferRequest"/>
      <wsdl:output message="tns:InterTransferResponse"/>
    </wsdl:operation>
    <wsdl:operation name="FundInTransfer">
      <wsdl:input message="tns:FundInTransferRequest"/>
      <wsdl:output message="tns:FundInTransferResponse"/>
    </wsdl:operation>
    <wsdl:operation name="ScreenTransfer">
      <wsdl:input message="tns:ScreenTransferRequest"/>
      <wsdl:output message="tns:ScreenTransferResponse"/>
    </wsdl:operation>
    <wsdl:operation name="MonitorForScreeningFeedback">
      <wsdl:input message="tns:MonitorForScreeningFeedbackRequest"/>
      <wsdl:output message="tns:MonitorForScreeningFeedbackResponse"/>
    </wsdl:operation>
    <wsdl:operation name="FundOutRequestForwarder">
      <wsdl:input message="tns:FundOutRequestForwarderRequest"/>
      <wsdl:output message="tns:FundOutRequestForwarderResponse"/>
    </wsdl:operation>
    <wsdl:operation name="IntraBeneficiaryTransfer">
      <wsdl:input message="tns:IntraBeneficiaryTransferRequest"/>
      <wsdl:output message="tns:IntraBeneficiaryTransferResponse"/>
    </wsdl:operation>
    <wsdl:operation name="CreditDepositAccount">
      <wsdl:input message="tns:DepositAccountCreditRequest"/>
      <wsdl:output message="tns:DepositAccountCreditResponse"/>
    </wsdl:operation>
    <wsdl:operation name="DebitDepositAccount">
      <wsdl:input message="tns:DepositAccountDebitRequest"/>
      <wsdl:output message="tns:DepositAccountDebitResponse"/>
    </wsdl:operation>
    <wsdl:operation name="CreditGLAccount">
      <wsdl:input message="tns:GLAccountCreditRequest"/>
      <wsdl:output message="tns:GLAccountCreditResponse"/>
    </wsdl:operation>
    <wsdl:operation name="DebitGLAccount">
      <wsdl:input message="tns:GLAccountDebitRequest"/>
      <wsdl:output message="tns:GLAccountDebitResponse"/>
    </wsdl:operation>
    <wsdl:operation name="WithdrawGold">
      <wsdl:input message="tns:GoldWithdrawlRequest"/>
      <wsdl:output message="tns:GoldWithdrawlResponse"/>
    </wsdl:operation>
    <wsdl:operation name="TransferFundToBeneficiary">
      <wsdl:input message="tns:TransferFundToBeneficiaryRequest"/>
      <wsdl:output message="tns:TransferFundToBeneficiaryResponse"/>
    </wsdl:operation>
    <wsdl:operation name="InquireInterBankTransaction">
      <wsdl:documentation>To fetch outward SARIE/SWIFT transaction details initiated from digital channels.</wsdl:documentation>
      <wsdl:input message="tns:InterBankTransactionInquiryRequest"/>
      <wsdl:output message="tns:InterBankTransactionInquiryResponse"/>
    </wsdl:operation>
    <wsdl:operation name="InquireInterBankCancelledTransactions">
      <wsdl:documentation>To fetch the list of payment transactions on which cancellation has been initiated over a period.</wsdl:documentation>
      <wsdl:input message="tns:InterBankCancelledTransactionsInquiryRequest"/>
      <wsdl:output message="tns:InterBankCancelledTransactionsInquiryResponse"/>
    </wsdl:operation>
    <wsdl:operation name="CancelInterBankTransaction">
      <wsdl:documentation>To cancel outward future dated SARIE transfer initiated from digital channels.</wsdl:documentation>
      <wsdl:input message="tns:InterBankTransactionCancellationRequest"/>
      <wsdl:output message="tns:InterBankTransactionCancellationResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetValueDateAndCutOffTime">
      <wsdl:input message="tns:ValueDateAndCutOffTimeRequest"/>
      <wsdl:output message="tns:ValueDateAndCutOffTimeResponse"/>
    </wsdl:operation>
    <wsdl:operation name="TrackSwiftGPIPayment">
      <wsdl:documentation>Track Swift payments by SWIFT gpi - Global Payment Initiative.
SWIFT gpi refelcts below,
When is the payment carried out.
How much does it cost to make the payment?
Where is the payment? 
Payment remains unchanged and homogeneous during the process?</wsdl:documentation>
      <wsdl:input message="tns:TrackSwiftGPIPaymentRequest"/>
      <wsdl:output message="tns:TrackSwiftGPIPaymentResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetMT103Message">
      <wsdl:input message="tns:MT103MessageRequest"/>
      <wsdl:output message="tns:MT103MessageResponse"/>
    </wsdl:operation>
    <wsdl:operation name="ManageRTPayments">
      <wsdl:documentation>RTP - Real Time Payments.
				         Initiate and Validate  RTP Service .</wsdl:documentation>
      <wsdl:input message="tns:ManageRTPaymentsRequest"/>
      <wsdl:output message="tns:ManageRTPaymentsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetRTPaymentTransactions">
      <wsdl:documentation>RTP - Real Time Payments.
				         Payment Enquiry Service.</wsdl:documentation>
      <wsdl:input message="tns:GetRTPaymentTransactionsRequest"/>
      <wsdl:output message="tns:GetRTPaymentTransactionsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="InquireProxyDetail">
      <wsdl:documentation>RTP - Real Time Payments.
				         Fetch Proxy Detail service.</wsdl:documentation>
      <wsdl:input message="tns:InquireProxyDetailRequest"/>
      <wsdl:output message="tns:InquireProxyDetailResponse"/>
    </wsdl:operation>
    <wsdl:operation name="FetchRTParticipantBanks">
      <wsdl:documentation>RTP - Real Time Payments.
				         Fetch IPS Participant bank list.</wsdl:documentation>
      <wsdl:input message="tns:FetchRTParticipantBanksRequest"/>
      <wsdl:output message="tns:FetchRTParticipantBanksResponse"/>
    </wsdl:operation>
    <wsdl:operation name="ManageProxy">
      <wsdl:documentation>Proxy management Service</wsdl:documentation>
      <wsdl:input message="tns:ManageProxyRequest"/>
      <wsdl:output message="tns:ManageProxyResponse"/>
    </wsdl:operation>
    <wsdl:operation name="ProxyLookup">
      <wsdl:documentation>Proxy Lookup Service</wsdl:documentation>
      <wsdl:input message="tns:ProxyLookupRequest"/>
      <wsdl:output message="tns:ProxyLookupResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetRTPPaymentType">
      <wsdl:documentation>RTP - Real Time Payments.
				         Determine Payment Type</wsdl:documentation>
      <wsdl:input message="tns:GetRTPPaymentTypeRequest"/>
      <wsdl:output message="tns:GetRTPPaymentTypeResponse"/>
    </wsdl:operation>
    <wsdl:operation name="FetchRTPLimit">
      <wsdl:documentation>RTP - Real Time Payments.
				         Fetch IPS Limits</wsdl:documentation>
      <wsdl:input message="tns:FetchRTPLimitRequest"/>
      <wsdl:output message="tns:FetchRTPLimitResponse"/>
    </wsdl:operation>
    <wsdl:operation name="MaintainQTLLimit">
      <wsdl:input message="tns:MaintainQTLLimitRequest"/>
      <wsdl:output message="tns:MaintainQTLLimitResponse"/>
    </wsdl:operation>
    <wsdl:operation name="MultiDebitMultiCredit">
      <wsdl:input message="tns:MultiDebitMultiCreditRequest"/>
      <wsdl:output message="tns:MultiDebitMultiCreditResponse"/>
    </wsdl:operation>
    <wsdl:operation name="ValidateBenAccountRTP">
      <wsdl:documentation>Functionality : Account Verification .
				Description : The service provides true or false response on validity and status of beneficiary account details sent in request .</wsdl:documentation>
      <wsdl:input message="tns:ValidateBenAccountRTPRequest"/>
      <wsdl:output message="tns:ValidateBenAccountRTPResponse"/>
    </wsdl:operation>
    <wsdl:operation name="ActOnRequestToPayRTP">
      <wsdl:documentation>Functionality : Request To Pay .
				Description : This service allows participant to update and act on rtp request, whether incoming or outgoing .</wsdl:documentation>
      <wsdl:input message="tns:ActOnRequestToPayRTPRequest"/>
      <wsdl:output message="tns:ActOnRequestToPayRTPResponse"/>
    </wsdl:operation>
    <wsdl:operation name="InquireRequestToPayRTP">
      <wsdl:documentation>Functionality : Request To Pay .
				Description : It provides a way to retrieve incoming and outgoing request to pay requests by providing their reference, status, amount and debtor and creditor information among other details .</wsdl:documentation>
      <wsdl:input message="tns:InquireRequestToPayRTPRequest"/>
      <wsdl:output message="tns:InquireRequestToPayRTPResponse"/>
    </wsdl:operation>
    <wsdl:operation name="InquireRTPProductTypes">
      <wsdl:documentation>Functionality : Request To Pay .
				Description : This service provides a list of predefined product types to be used for Request to Pay exchanges.</wsdl:documentation>
      <wsdl:input message="tns:InquireRTPProductTypesRequest"/>
      <wsdl:output message="tns:InquireRTPProductTypesResponse"/>
    </wsdl:operation>
    <wsdl:operation name="RequestToPayRTP">
      <wsdl:documentation>Functionality : Request To Pay .
				Description : It allows to create and send RequestToPay request to a local bank via IPS.</wsdl:documentation>
      <wsdl:input message="tns:RequestToPayRTPRequest"/>
      <wsdl:output message="tns:RequestToPayRTPResponse"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateRTPStatus">
      <wsdl:documentation>Functionality : Request To Pay .
				Description : It provides ability to call back and manually update RTP status.</wsdl:documentation>
      <wsdl:input message="tns:UpdateRTPStatusRequest"/>
      <wsdl:output message="tns:UpdateRTPStatusResponse"/>
    </wsdl:operation>
    <wsdl:operation name="CreditContingentAccount">
      <wsdl:documentation>Functionality : Credit .
				Description : Debit from OGL and credit to Contingent Account.</wsdl:documentation>
      <wsdl:input message="tns:CreditContingentAccountRequest"/>
      <wsdl:output message="tns:CreditContingentAccountResponse"/>
    </wsdl:operation>
    <wsdl:operation name="DebitContingentAccount">
      <wsdl:documentation>Functionality : Debit .
				Description : Debit from Contingent Account and Credit to OGL.</wsdl:documentation>
      <wsdl:input message="tns:DebitContingentAccountRequest"/>
      <wsdl:output message="tns:DebitContingentAccountResponse"/>
    </wsdl:operation>
    <wsdl:operation name="TransferFundToCharity">
      <wsdl:documentation>-- to initiate the Ehsan charity fund transfer.
		    --to debit the transferred amount from customer account or Credit Card and credit it in BGL account related to charity type.</wsdl:documentation>
      <wsdl:input message="tns:TransferFundToCharityRequest"/>
      <wsdl:output message="tns:TransferFundToCharityResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetAvailableRituals">
      <wsdl:documentation>The Service will be consumed by Resellers to return the list of available ritual in Adahi System  .</wsdl:documentation>
      <wsdl:input message="tns:GetAvailableRitualsRequest"/>
      <wsdl:output message="tns:GetAvailableRitualsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetAvailableProduct">
      <wsdl:documentation>The Service will be consumed by Resellers to return the list of product allowed based on Reseller Contract with Adahi  .</wsdl:documentation>
      <wsdl:input message="tns:GetAvailableProductRequest"/>
      <wsdl:output message="tns:GetAvailableProductResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetAdahiCountries">
      <wsdl:input message="tns:GetAdahiCountriesRequest"/>
      <wsdl:output message="tns:GetAdahiCountriesResponse"/>
    </wsdl:operation>
    <wsdl:operation name="SubmitAdahiOrder">
      <wsdl:documentation>The Service will be consumed by Reseller to submit new Adahi order to IDB (Islamic Development Bank)   .</wsdl:documentation>
      <wsdl:input message="tns:SubmitAdahiOrderRequest"/>
      <wsdl:output message="tns:SubmitAdahiOrderResponse"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateCouponStatus">
      <wsdl:documentation>The Service will be Implemented and hosted in Resellers Servers, consumed by IDB Adahi System to update the coupon status upon Execution and distribution  .</wsdl:documentation>
      <wsdl:input message="tns:UpdateCouponStatusRequest"/>
      <wsdl:output message="tns:UpdateCouponStatusResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="PaymentsService" type="tns:PaymentsService">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="TransferFund">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/TransferFund"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!--<wsdl:operation name="PayToBeneficiary">
			<soap:operation
			soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/PayToBeneficiary" />
			<wsdl:input>
			<soap:body use="literal" />
			<soap:header message="tns:Security" part="header"
			use="literal" />
			<soap:header message="tns:ServiceHeader" part="header"
			use="literal" />
			</wsdl:input>
			<wsdl:output>
			<soap:body use="literal" />
			</wsdl:output>
			</wsdl:operation>-->
    <wsdl:operation name="PayAtBranch">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/PayAtBranch"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="HoldAndRelease">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/HoldAndRelease"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AccountTransfer">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/AccountTranfer"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!--wsdl:operation name="AccountingEntryFromIBAS">
			<soap:operation
			soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/AccountingEntryFromIBAS" />
			<wsdl:input>
			<soap:body use="literal" />
			<soap:header message="tns:Security"	part="header" use="literal"	/>
			<soap:header message="tns:ServiceHeader" part="header"
			use="literal" />
			</wsdl:input>
			<wsdl:output>
			<soap:body use="literal" />
			</wsdl:output>
			</wsdl:operation>
			<wsdl:operation name="SendPayment">
			<soap:operation
			soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/SendPayment" />
			<wsdl:input>
			<soap:body use="literal" />
			<soap:header message="tns:Security"	part="header" use="literal"	/>
			<soap:header message="tns:ServiceHeader" part="header"
			use="literal" />
			</wsdl:input>
			<wsdl:output>
			<soap:body use="literal" />
			</wsdl:output>
			</wsdl:operation-->
    <wsdl:operation name="TransferFundGLToGL">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/TransferFundGLToGL"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TransferFundGLToDeposit">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/TransferFundGLToDeposit"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TransferFundDepositToGL">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/TransferFundDepositToGL"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TransferFundSWIFT">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/TransferFundSWIFT"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TransferFundSARIE">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/TransferFundSARIE"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!--<wsdl:operation name="GetFees">
			<soap:operation
			soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/GetFees" />
			<wsdl:input>
			<soap:body use="literal" />
			<soap:header message="tns:Security" part="header"
			use="literal" />
			<soap:header message="tns:ServiceHeader" part="header"
			use="literal" />
			</wsdl:input>
			<wsdl:output>
			<soap:body use="literal" />
			</wsdl:output>
			</wsdl:operation>-->
    <wsdl:operation name="IntraBankFundTransfer">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/IntraBankFundtransfer"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InterBankFundTransfer">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/InterBankFundtransfer"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FundInTransfer">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/InterBankFundtransfer"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ScreenTransfer">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/ScreenTransfer"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MonitorForScreeningFeedback">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/MonitorForScreeningFeedback"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FundOutRequestForwarder">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/FundOutRequestForwarder"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IntraBeneficiaryTransfer">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/IntraBeneficiaryTransfer"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DebitDepositAccount">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/DebitDepositAccount"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditDepositAccount">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/account/1.0/CreditDepositAccount"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditGLAccount">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/AccountCredit"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DebitGLAccount">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/DebitGLAccount"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="WithdrawGold">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/WithdrawGold"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TransferFundToBeneficiary">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/TransferFundToBeneficiary"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InquireInterBankTransaction">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/InquireInterBankTransaction"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InquireInterBankCancelledTransactions">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/InquireInterBankCancelledTransactions"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelInterBankTransaction">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/CancelInterBankTransaction"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetValueDateAndCutOffTime">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/GetValueDateAndCutOffTime"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TrackSwiftGPIPayment">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/TrackSwiftGPIPayment"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMT103Message">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/GetMT103Message"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ManageRTPayments">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/ManageRTPayments"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetRTPaymentTransactions">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/GetRTPaymentTransactions"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InquireProxyDetail">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/InquireProxyDetail"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FetchRTParticipantBanks">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/FetchRTParticipantBanks"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ProxyLookup">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/ProxyLookup"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ManageProxy">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/ManageProxy"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetRTPPaymentType">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/GetRTPPaymentType"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FetchRTPLimit">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/FetchRTPLimit"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MaintainQTLLimit">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/MaintainQTLLimit"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MultiDebitMultiCredit">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/MultiDebitMultiCredit"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ValidateBenAccountRTP">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/ValidateBenAccountRTP"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ActOnRequestToPayRTP">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/ActOnRequestToPayRTP"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InquireRequestToPayRTP">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/InquireRequestToPayRTP"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InquireRTPProductTypes">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/InquireRTPProductTypes"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RequestToPayRTP">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/RequestToPayRTP"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateRTPStatus">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/UpdateRTPStatus"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditContingentAccount">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/CreditContingentAccount"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DebitContingentAccount">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/DebitContingentAccount"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TransferFundToCharity">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/TransferFundToCharity"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAvailableRituals">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/GetAvailableRituals"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAvailableProduct">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/GetAvailableProduct"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAdahiCountries">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/GetAdahiCountries"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SubmitAdahiOrder">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/SubmitAdahiOrder"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateCouponStatus">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/payment/1.0/UpdateCouponStatus"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="PaymentsService">
    <wsdl:port binding="tns:PaymentsService" name="PaymentsService">
      <soap:address location="http://**********:8080/cordys/com.alahli.gateway.NCBGateway.wcp?organization=o=FoundationServices,cn=cordys,cn=defaultInst,o=alahli.com"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>