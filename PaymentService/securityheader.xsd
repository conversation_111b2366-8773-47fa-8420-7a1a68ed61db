<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema elementFormDefault="qualified" targetNamespace="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:tns="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <element name="Security">
    <complexType>
      <sequence>
        <element name="UsernameToken">
          <complexType>
            <sequence>
              <element name="Username" type="xsd:string"/>
              <element name="Password" type="xsd:string"/>
            </sequence>
          </complexType>
        </element>
      </sequence>
    </complexType>
  </element>
</xsd:schema>