<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema targetNamespace="http://corp.alahli.com/middlewareservices/payment/1.0/" elementFormDefault="qualified" xmlns:tns="http://corp.alahli.com/middlewareservices/payment/1.0/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:fault="http://corp.alahli.com/middlewareservices/fault/1.0/" xmlns:Q1="http://corp.alahli.com/schemas/middleware/temporaltypes/2012/10/29/">
  <xsd:import namespace="http://corp.alahli.com/middlewareservices/fault/1.0/" schemaLocation="fault.xsd"/>
  <xsd:import namespace="http://corp.alahli.com/schemas/middleware/temporaltypes/2012/10/29/" schemaLocation="schemas.xsd"/>
  <xsd:element name="TransferFundRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="fromAccountId" type="Q1:AccountId"/>
        <xsd:element name="toAccountId" type="Q1:AccountId"/>
        <xsd:element name="fromAmount" type="Q1:Money"/>
        <xsd:element name="toAmount" type="Q1:Money"/>
        <xsd:element name="description" type="xsd:string"/>
        <xsd:element name="description2" type="xsd:string"/>
        <xsd:element name="override">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="Y"/>
              <xsd:enumeration value="N"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="postDate" type="Q1:Date"/>
        <xsd:element name="journalId" type="Q1:JournalId"/>
        <xsd:element name="promoCode" type="xsd:string"/>
        <xsd:element name="shortCIF" type="Q1:ShortCIF"/>
        <xsd:element name="baseCurrencyAmount" type="Q1:Money"/>
        <xsd:element name="commissionAmount" type="Q1:Money"/>
        <xsd:element name="chargeAmount" type="Q1:Money"/>
        <xsd:element name="treasuryRate">
          <xsd:simpleType>
            <xsd:restriction base="xsd:decimal">
              <xsd:totalDigits value="14"/>
              <xsd:fractionDigits value="9"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="atmTime" type="Q1:TimeZone"/>
        <xsd:element name="atmDate" type="Q1:Date"/>
        <xsd:element name="atmReference" type="xsd:string"/>
        <xsd:element name="atmTerminal" type="xsd:string"/>
        <xsd:element name="atmCardId" type="xsd:string"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="TransferFundResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:TransferFundResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <!--<xsd:element name="FundTransferRequest">
  <xsd:complexType>
    <xsd:sequence>
      <xsd:element maxOccurs="1" minOccurs="1" name="fromAccountId" type="Q1:AccountId"/>
      <xsd:element maxOccurs="1" minOccurs="1" name="toAccountId" type="Q1:AccountId"/>
      <xsd:element maxOccurs="1" minOccurs="1" name="fromAmount" type="Q1:Money"/>
      <xsd:element maxOccurs="1" minOccurs="1" name="toAmount" type="Q1:Money"/>
      <xsd:element maxOccurs="1" minOccurs="0" name="description" type="xsd:string"/>
      <xsd:element maxOccurs="1" minOccurs="0" name="description2" type="xsd:string"/>
      <xsd:element default="Y" maxOccurs="1" minOccurs="1" name="override">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="Y"/>
            <xsd:enumeration value="N"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      
  
      <xsd:element maxOccurs="1" minOccurs="0" name="treasuryRate">
        <xsd:simpleType>
          <xsd:restriction base="xsd:decimal">
            <xsd:totalDigits value="14"/>
            <xsd:fractionDigits value="9"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element maxOccurs="1" minOccurs="0" name="postDate" type="Q1:Date"/>
      <xsd:element maxOccurs="1" minOccurs="0" name="journalId" type="Q1:JournalId"/>
      <xsd:element maxOccurs="1" minOccurs="0" name="shortCIF" type="Q1:ShortCIF"/>
      <xsd:element maxOccurs="1" minOccurs="0" name="promoCode" type="xsd:string"/>
      <xsd:element maxOccurs="1" minOccurs="0" name="baseCurrencyAmount" type="Q1:Money"/>
      <xsd:element maxOccurs="1" minOccurs="0" name="commissionAmount" type="Q1:Money"/>
      <xsd:element maxOccurs="1" minOccurs="0" name="chargeAmount" type="Q1:Money"/>
		      <xsd:element maxOccurs="1" minOccurs="0" name="isFundInATM" type="xsd:boolean"/>
		      <xsd:element maxOccurs="1" minOccurs="0" name="posRetailer" type="xsd:string"/>
      <xsd:element maxOccurs="1" minOccurs="0" name="atmTime" type="Q1:TimeZone"/>
      <xsd:element maxOccurs="1" minOccurs="0" name="atmDate" type="Q1:Date"/>
      <xsd:element maxOccurs="1" minOccurs="0" name="atmReference" type="xsd:string"/>
      <xsd:element maxOccurs="1" minOccurs="0" name="atmTerminal" type="xsd:string"/>
      <xsd:element maxOccurs="1" minOccurs="0" name="atmCardId" type="xsd:string"/>
      <xsd:element maxOccurs="1" minOccurs="0" name="fxDeal" type="tns:FXDealType"/>
    </xsd:sequence>
  </xsd:complexType>
</xsd:element>-->
  <xsd:element name="FundTransferRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="1" name="fromAccountId" type="Q1:AccountId"/>
        <xsd:element maxOccurs="1" minOccurs="1" name="toAccountId" type="Q1:AccountId"/>
        <xsd:element maxOccurs="1" minOccurs="1" name="fromAmount" type="Q1:Money"/>
        <xsd:element maxOccurs="1" minOccurs="1" name="toAmount" type="Q1:Money"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="description" type="xsd:string"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="description2" type="xsd:string"/>
        <xsd:element default="Y" maxOccurs="1" minOccurs="1" name="override">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="Y"/>
              <xsd:enumeration value="N"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="treasuryRate">
          <xsd:simpleType>
            <xsd:restriction base="xsd:decimal">
              <xsd:totalDigits value="14"/>
              <xsd:fractionDigits value="9"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="postDate" type="Q1:Date"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="journalId" type="Q1:JournalId"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="shortCIF" type="Q1:ShortCIF"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="promoCode" type="xsd:string"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="baseCurrencyAmount" type="Q1:Money"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="commissionAmount" type="Q1:Money"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="chargeAmount" type="Q1:Money"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="atmTime" type="Q1:TimeZone"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="atmDate" type="Q1:Date"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="atmReference" type="xsd:string"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="atmTerminal" type="xsd:string"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="atmCardId" type="Q1:EncryptedText"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="fxDeal" type="tns:FXDealType"/>
        <xsd:element name="AMLPurposeCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="categoryOfTransaction" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="secondLevelPurpose" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="relationships" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="virtualAccountId" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Virtual Account Id for ICustody Project .</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="FundTransferResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:TransferFundResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="FXDealType">
    <xsd:sequence>
      <xsd:element name="cif" type="Q1:CIF" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="dealDate" type="Q1:MWDate" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="buyCurrency" type="Q1:ISOCurrencyCode" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="buyAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="sellCurrency" type="Q1:ISOCurrencyCode" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="contractBuyDate" type="Q1:MWDate" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="contractSellDate" type="Q1:MWDate" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="paymentComments" type="xsd:string" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="contractRate" minOccurs="1" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:decimal">
            <xsd:minInclusive value="0"/>
            <xsd:fractionDigits value="4"/>
            <xsd:totalDigits value="9"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="treasuryRate" minOccurs="1" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:decimal">
            <xsd:totalDigits value="14"/>
            <xsd:fractionDigits value="9"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="channelReference" type="Q1:Max35Text" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="description" type="xsd:string" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="communicationFinprod" type="xsd:string" default="SPOT" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="communicationMode" type="xsd:string" default="POSSQJED" minOccurs="1" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TransferFundResponseType">
    <xsd:sequence>
      <xsd:element name="fromAccountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="fromBalance" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="toBalance" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="journalId" type="Q1:JournalId" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="fxDealReference" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="PayToBeneficiaryRequest" type="tns:PayToBeneficiaryRequestType"/>
  <xsd:complexType name="PayToBeneficiaryRequestType">
    <xsd:sequence>
      <xsd:element name="accountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="uti" type="Q1:Max16Text" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="debitAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="debitCurrency" type="Q1:ISOCurrencyCode" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="transferAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="transferCurrency" type="Q1:ISOCurrencyCode" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="commission" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="beneficiaryName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1" nillable="true"/>
      <xsd:element name="beneficiaryAccountId" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="beneficiaryBranch" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="beneficiaryAddress1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="beneficiaryAddress2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="beneficiaryAddress3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="swiftBIC" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankRouteCode" minOccurs="0" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="20"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="bankName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankAddress1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankAddress2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankAddress3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankIdBIC" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="instructions1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="instructions2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="instructions3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="instructions4" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterAddress1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterAddress2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterAddress3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterPhone" type="Q1:PhoneNumber" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="baseCurrencyAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="charges" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="chargeType" minOccurs="0" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="PAY"/>
            <xsd:enumeration value="URG"/>
            <xsd:enumeration value="WORLD_REG"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="bankCharge" minOccurs="0" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="SHA"/>
            <xsd:enumeration value="BEN"/>
            <xsd:enumeration value="REM"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="accountWithOpt" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="correspondingBankCharges" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="reasonCode" minOccurs="0" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="10"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="reasonDetail" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="isSariePayment" default="Y" minOccurs="1" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="Y"/>
            <xsd:enumeration value="N"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="paymentDate" type="Q1:MWDate" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="treasuryRate" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:decimal">
            <xsd:totalDigits value="14"/>
            <xsd:fractionDigits value="9"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="fxDeal" type="tns:FXDealType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AMLPurposeCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="instructions5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="instructions6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="PayToBeneficiaryResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:PayToBeneficiaryResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="PayToBeneficiaryWithFXDealRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accID" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="UTI" type="Q1:Max16Text" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="templateId" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="12"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="debitAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="debitCurrency" type="Q1:ISOCurrencyCode" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="transferAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="transferCurrency" type="Q1:ISOCurrencyCode" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="commission" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="shortCIF" type="Q1:ShortCIF" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="beneficiaryName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1" nillable="true"/>
        <xsd:element name="beneficiaryAccID" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="IBANAuthorisation" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="beneficiaryBranch" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="beneficiaryAddress1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="beneficiaryAddress2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="beneficiaryAddress3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="bankID_BIC" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="acctWithOpt" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="swiftBic" type="Q1:BIC" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="bankRouteCode" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="20"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="bankName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="bankAddress1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="bankAddress2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="bankAddress3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="beneLocIdNo" type="Q1:ISOCountryCode" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="beneBnkLocIdNo" type="Q1:ISOCountryCode" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="instructions1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="instructions2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="instructions3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="instructions4" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="remitterName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="remitterAddress1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="remitterAddress2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="remitterAddress3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="remitterPhone" type="Q1:PhoneNumber" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="baseCurrencyAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="charges" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="chargeType" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="PAY"/>
              <xsd:enumeration value="URG"/>
              <xsd:enumeration value="WORLD_REG"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="correspondantBankCharges" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="tFxRate" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="rateType" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:totalDigits value="4"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="reasonCode" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="10"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="reasonDetail" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="isSariePayment" default="Y" minOccurs="1" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="Y"/>
              <xsd:enumeration value="N"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="paymentDate" type="Q1:Date" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="override" type="Q1:Max2Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="value" type="Q1:Max2Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="segmentLimit" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="journal" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:totalDigits value="9"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="branch" type="Q1:Branch" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="fxDeal" minOccurs="0" maxOccurs="1" type="tns:FXDealType"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="PayToBeneficiaryWithFXDealResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:PayToBeneficiaryResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="PayToBeneficiaryResponseType">
    <xsd:sequence>
      <xsd:element name="journalNumber" type="xsd:string" minOccurs="1"/>
      <xsd:element name="transcationDate" type="Q1:Date" minOccurs="1"/>
      <xsd:element name="uti" type="Q1:Max16Text" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="fxDealReference" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterLanguage" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="BranchPaymentRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="fromAccountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="fromAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="carrierType" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="1"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="promoCode" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="2"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="journal" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:totalDigits value="9"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="beneficiaryName" type="Q1:Max35Text" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="generateSwiftMessage" default="Y" minOccurs="1" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="Y"/>
              <xsd:enumeration value="N"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="toAccountId" type="Q1:AccountId" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="toAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="baseCurrencyAmount" type="Q1:Amount" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="commision" type="Q1:Amount" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="feeAmount" type="Q1:Amount" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="rateType" default="04" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:totalDigits value="2"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="currencyVersion" default="ZZZZ" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="4"/>
              <xsd:minLength value="4"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="swiftCharges" type="Q1:Amount" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="RelationShip" type="Q1:Max35Text" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="beneficiaryAddress2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="beneficiaryAddress3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="OrderDetails1" type="Q1:Max35Text" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="OrderDetails2" type="Q1:Max35Text" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="OrderDetails3" type="Q1:Max35Text" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="OrderDetails4" type="Q1:Max35Text" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="deferredIntDaysCredit" type="Q1:Max2Digits" default="00" minOccurs="0"/>
        <xsd:element name="deferredIntDaysDebit" type="Q1:Max2Digits" default="00" minOccurs="0"/>
        <xsd:element name="valueDate" type="Q1:MWDate" minOccurs="0"/>
        <xsd:element name="applicationId" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:totalDigits value="2"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="beneficiaryAccountId" type="Q1:Max35Text" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="correspodentBankId" type="Q1:Max16Text" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="orderCustomerAccountNumber" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="orderCustomerdetailsOpt" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="1"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="accountName" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="60"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="bookingNumber" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="7"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="idType" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="4"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="idNumber" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="24"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="contactNumber" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="12"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="expectedCommision" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="18"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="commisionFlag" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="1"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="previousTransReferanceNumber" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="22"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="transactionReferance" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="5"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="externalDetails" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="1"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="swiftReferanceNumber" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="10"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="BeneficiaryDetailsOpt" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="1"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="treasuryRate" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:decimal">
              <xsd:totalDigits value="14"/>
              <xsd:fractionDigits value="9"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="localReferanceNumber" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:totalDigits value="12"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="BranchPaymentResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:BranchPaymentResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="BranchPaymentResponseType">
    <xsd:sequence>
      <xsd:element name="journalId" type="Q1:JournalId" minOccurs="1"/>
      <xsd:element name="uti" type="Q1:Max16Text" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="balance" type="Q1:Amount"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="HoldAndReleaseRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountID" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="amount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="holdFlag" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="T"/>
              <xsd:enumeration value="F"/>
              <xsd:maxLength value="1"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="reasonCode" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="14"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="referenceNumber" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="20"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="retry" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="1"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="override" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="1"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="description" type="Q1:Max70Text" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="HoldAndReleaseResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:HoldAndReleaseResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="HoldAndReleaseResponseType">
    <xsd:sequence>
      <xsd:element name="accountId" type="Q1:AccountId" minOccurs="1"/>
      <xsd:element name="journalId" type="Q1:JournalId" minOccurs="1"/>
      <xsd:element name="referenceNumber" minOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="20"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="AccountTransferRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="fromAccountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="fromAmount" type="Q1:Money" minOccurs="1"/>
        <xsd:element name="toAccountId" type="xsd:string" minOccurs="1"/>
        <xsd:element name="toAmount" type="Q1:Money" minOccurs="1"/>
        <xsd:element name="postingDate" type="Q1:Date" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="promoCode" type="Q1:Max2Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="commissionAmount" type="Q1:Money" nillable="true" minOccurs="1"/>
        <xsd:element name="transactionFees" type="Q1:Money" minOccurs="1"/>
        <xsd:element name="otherCharges" type="Q1:Money" minOccurs="0"/>
        <xsd:element name="baseCurrencyAmount" type="Q1:Money" minOccurs="1"/>
        <xsd:element name="rateType" default="03" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:totalDigits value="4"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="currencyVersion" default="ZZZZ" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="4"/>
              <xsd:minLength value="4"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="description1" type="Q1:Max35Text" minOccurs="0"/>
        <xsd:element name="description2" type="Q1:Max35Text" minOccurs="0"/>
        <xsd:element name="journalNumber" type="Q1:JournalId" default="0" minOccurs="0"/>
        <xsd:element name="bookingNumber" type="Q1:Max10Text" minOccurs="0"/>
        <xsd:element name="treasuryRate" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:decimal">
              <xsd:totalDigits value="14"/>
              <xsd:fractionDigits value="9"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="requestDate" type="Q1:Date" minOccurs="0"/>
        <xsd:element name="mnemonicCode" type="Q1:Max2Text" default="EF" minOccurs="0"/>
        <xsd:element name="deferredIntDaysCredit" type="Q1:Max2Digits" default="00" minOccurs="0"/>
        <xsd:element name="deferredIntDaysDebit" type="Q1:Max2Digits" default="00" minOccurs="0"/>
        <xsd:element name="valueDate" type="Q1:Date" minOccurs="0"/>
        <xsd:element name="referenceNo" type="Q1:Max35Text" minOccurs="0"/>
        <xsd:element name="moneyReceiptNo" type="Q1:Max35Text" minOccurs="0"/>
        <xsd:element name="voucherNo" type="Q1:Max35Text" minOccurs="0"/>
        <xsd:element name="arabicName" type="Q1:Max35Text" minOccurs="0"/>
        <xsd:element name="englishName" type="Q1:Max35Text" minOccurs="0"/>
        <xsd:element name="purpose1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="purpose2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="override" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="chargeAmount" type="Q1:Money" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="shortCIF" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="segmentLimit" type="xsd:string" maxOccurs="1" minOccurs="0" default="0"/>
        <xsd:element name="narrative" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="option" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="subscriberId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="deleteIPORecord" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="AMLPurposeCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="Limit" type="tns:LimitType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="subscriberIdType" type="Q1:NINType" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>ID TYPE</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="amendReason" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="AccountTransferResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:TransferResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="PaymentTransferRequest" type="tns:PayToBeneficiaryRequestType"/>
  <xsd:element name="PaymentTransferResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:TransferResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="TransferResponseType">
    <xsd:all>
      <xsd:element name="transactionId" type="Q1:TransactionId" minOccurs="0"/>
      <xsd:element name="transactionStatus" type="Q1:TransactionStatus" minOccurs="0"/>
      <xsd:element name="transactionStatusCode" type="Q1:StatusCode" minOccurs="0"/>
      <xsd:element name="journalNumber" type="Q1:JournalId" minOccurs="0"/>
      <xsd:element name="transactionMessage" type="xsd:string" minOccurs="0"/>
      <xsd:element name="transactionDate" type="Q1:MWDateOptional" minOccurs="0"/>
      <xsd:element name="fromAccountNumber" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="toAccountNumber" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="reference" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="balance" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="TransferResponseType1">
    <xsd:all>
      <xsd:element name="journalNumber" type="Q1:JournalId" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <!--<xsd:element name="AccountingEntryFromIBASRequest"  type="envl:UpdtBckListSrvcEnvlType" />
			<xsd:element name="AccountingEntryFromIBASResponse">
               <xsd:complexType>
					<xsd:sequence>
							<xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:TransferResponseType"></xsd:element>
							<xsd:element  maxOccurs="1" minOccurs="0" ref="fault:fault"></xsd:element>
				</xsd:sequence>	
				</xsd:complexType>
           </xsd:element>-->
  <xsd:element name="SendPaymentRequest" type="tns:PayToBeneficiaryRequestType"/>
  <xsd:element name="SendPaymentResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:SendPaymentResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="SendPaymentResponseType">
    <xsd:all>
      <xsd:element name="journalNumber" type="Q1:JournalId" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="UTI" type="Q1:Max16Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterNameLanguage" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="balance" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="valueDate" type="Q1:Date" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FXDealImportRefNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:element name="GLToGLTransferRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="fromAccountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="fromAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="transactionDesc" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="promoCode" type="Q1:Max2Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="toAccountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="purposeOfTransfer1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="purposeOfTransfer2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="toAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="baseCurrencyAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="commissionAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="chargeAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="rateType" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:totalDigits value="4"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="currencyVersion" default="ZZZZ" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="4"/>
              <xsd:minLength value="4"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="deferredIntDay" type="Q1:Max2Digits" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="debtDeferredIntDays" type="Q1:Max2Digits" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="depNonValDays" type="Q1:Max2Digits" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="statementNarrative" type="Q1:Max70Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="bookingNum" type="Q1:Max10Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="MRN" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:totalDigits value="20"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="MRN2" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:totalDigits value="20"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="treasuryRate" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:decimal">
              <xsd:totalDigits value="14"/>
              <xsd:fractionDigits value="9"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="AMLPurposeCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="dealId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="pnlAmount" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="action" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="creditCardDetails" type="tns:creditCardDetailsType" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="creditCardDetailsType">
    <xsd:all>
      <xsd:element name="cardId" type="Q1:EncryptedText" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="paymentReferenceCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="source" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="purpose" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="relationship" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:element name="GLToGLTransferResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:TransferResponseType1"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="DepositToGLTransferRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="fromAccountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="dueAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="promoCode" type="Q1:Max2Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="toAccountID" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="purposeOfTransfer1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="purposeOfTransfer2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="amount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="baseCurrencyAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="commissionAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="chargeAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="rateType" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:totalDigits value="4"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="statementNarrative" type="Q1:Max70Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="bookingNumber" type="Q1:Max10Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="treasuryRate" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:decimal">
              <xsd:totalDigits value="14"/>
              <xsd:fractionDigits value="9"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="journalNumber" type="Q1:JournalId" default="0" minOccurs="0"/>
        <xsd:element name="override" default="N" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="Y"/>
              <xsd:enumeration value="N"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="referenceNo" type="xsd:int" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="isNCBC" type="Q1:YorNTypeOptional" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="ncbcPromoCode" type="Q1:YorNTypeOptional" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="postingDate" type="Q1:MWDateOptional" minOccurs="0" maxOccurs="1"/>
        <!--<xsd:element name="currencyVersion" default="ZZZZ" minOccurs="0">
					<xsd:simpleType>
					<xsd:restriction base="xsd:string">
					<xsd:maxLength value="4" />
					<xsd:minLength value="4" />
					</xsd:restriction>
					</xsd:simpleType>
					</xsd:element>
					<xsd:element name="mnemonicCode" type="Q1:Max2Text" minOccurs="0" maxOccurs="1" />
					<xsd:element name="subscriberNumber" type="Q1:Max16Text" minOccurs="0" maxOccurs="1" />
					<xsd:element name="deferredIntDay" type="Q1:Max2Digits" minOccurs="0" maxOccurs="1" />
					<xsd:element name="debtDeferredIntDays" type="Q1:Max2Digits" minOccurs="0" maxOccurs="1" />
					<xsd:element name="depNonValDays" type="Q1:Max2Digits" minOccurs="0" maxOccurs="1" />
					<xsd:element name="MRN" minOccurs="0" maxOccurs="1">
					<xsd:simpleType>
					<xsd:restriction base="xsd:int">
					<xsd:totalDigits value="20" />
					</xsd:restriction>
					</xsd:simpleType>
					</xsd:element>
					<xsd:element name="voucherNumber" type="xsd:int" minOccurs="0" maxOccurs="1" />
					<xsd:element name="AMLPurposeCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>-->
        <xsd:element name="fxDeal" type="tns:FXDealType_11" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="toAmount" type="Q1:MoneyOptional" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="mtNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element type="xsd:string" name="channelIndicator" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Channel Indicator</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element type="xsd:string" name="sourceOfPayment" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Source Of Payment</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="DepositToGLTransferResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:DepositToGLTransferResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="DepositToGLTransferResponseType">
    <xsd:all>
      <xsd:element name="journalNumber" type="Q1:JournalId" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="journalReversalNumber" type="Q1:JournalId" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="referenceNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="balanceAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
    </xsd:all>
  </xsd:complexType>
  <!--Full Implementation XSD
            <xsd:element  name="GLToDepositTransferRequest">
           		 <xsd:complexType>
					<xsd:all>
						<xsd:element name="fromAccountId" type="xsd:string" minOccurs="1" maxOccurs="1"/>
						<xsd:element name="toAccountId" type="xsd:string" minOccurs="1" maxOccurs="1"/>
												
						
						<xsd:element name="promoCode" type="Q1:Max2Text" minOccurs="0" maxOccurs="1" />
						<xsd:element name="referenceNo" type="xsd:int" minOccurs="0" maxOccurs="1" />
						
						<xsd:element name="purposeOfTransfer1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="purposeOfTransfer2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
						
						<xsd:element name="baseCurrencyAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="commissionAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="chargeAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="rateType"  minOccurs="0" maxOccurs="1">
						 	<xsd:simpleType>
								<xsd:restriction base="xsd:int">
									<xsd:totalDigits value="4"/>
								</xsd:restriction>
							</xsd:simpleType>
						</xsd:element>				
						<xsd:element name="currencyVersion"  default="ZZZZ" minOccurs="0" maxOccurs="1">
						<xsd:simpleType>
							<xsd:restriction base="xsd:string">
								<xsd:maxLength value="4"/>
								<xsd:minLength value="4"/>
							</xsd:restriction>
						</xsd:simpleType>
						</xsd:element>
						<xsd:element name="mnemonicCode" type="Q1:Max2Text" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="subscriberNo" type="Q1:Max16Text" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="deferredIntDay" type="Q1:Max2Digits" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="debtDeferredIntDays" type="Q1:Max2Digits" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="depNonValDays" type="Q1:Max2Digits" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="statementNarrative" type="Q1:Max70Text" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="bookingNum" type="Q1:Max10Text" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="MRN"  minOccurs="0" maxOccurs="1">
						 <xsd:simpleType>
								<xsd:restriction base="xsd:int">
									<xsd:totalDigits value="20"/>
								</xsd:restriction>
   							 </xsd:simpleType>
						</xsd:element>
						<xsd:element name="voucherNumer" type="xsd:int" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="subscriberIdType" type="Q1:Max16Text" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="REDFContract" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="contractDate" type="Q1:Date" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="arabicName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="englishName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="bankCode" type="Q1:BankCode" minOccurs="0" maxOccurs="1"/>						
  					
  					    <xsd:element name="isOmniAccount" default="false" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
  						<xsd:element name="treasuryRate" minOccurs="0" maxOccurs="1" >
							<xsd:simpleType>
								<xsd:restriction base="xsd:decimal">
									<xsd:totalDigits value="14"/>
									<xsd:fractionDigits value="9"/>
								</xsd:restriction>
							</xsd:simpleType>
						</xsd:element>								
						<xsd:element name="transCurrencyCode" type="Q1:ISOCurrencyCode" maxOccurs="1" minOccurs="0" />
						<xsd:element name="fill" type="xsd:string" maxOccurs="1" minOccurs="0" />
						<xsd:element name="deleteIPORecord" type="xsd:string" maxOccurs="1" minOccurs="0" />
						<xsd:element name="amount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
						<xsd:element name="transactionAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
						<xsd:element name="changeAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="postingDate" type="Q1:Date" minOccurs="0" maxOccurs="1"/>
						<xsd:element name="journalNumber" type="Q1:JournalId" default="0" minOccurs="0"/>	
						<xsd:element name="override" default="N" minOccurs="0" maxOccurs="1">
						<xsd:simpleType>
							<xsd:restriction base="xsd:string" >
								<xsd:enumeration value="Y"></xsd:enumeration>
								<xsd:enumeration value="N"></xsd:enumeration>
							</xsd:restriction>
						</xsd:simpleType>
						</xsd:element>					
						
					</xsd:all>
				</xsd:complexType>
			</xsd:element>-->
  <xsd:element name="GLToDepositTransferRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="fromAccountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="fromAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="promoCode" type="Q1:Max2Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="referenceNo" type="xsd:int" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="toAccountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="purposeOfTransfer1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="purposeOfTransfer2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="toAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="baseCurrencyAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="commissionAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="chargeAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="rateType" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:totalDigits value="4"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="currencyVersion" default="ZZZZ" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="4"/>
              <xsd:minLength value="4"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="subscriberNo" type="Q1:Max16Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="deferredIntDay" type="Q1:Max2Digits" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="debtDeferredIntDays" type="Q1:Max2Digits" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="depNonValDays" type="Q1:Max2Digits" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="statementNarrative" type="Q1:Max70Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="bookingNum" type="Q1:Max10Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="MRN" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:totalDigits value="20"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="voucherNumer" type="xsd:int" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="subscriberIdType" type="Q1:Max16Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="REDFContract" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="contractDate" type="Q1:Date" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="arabicName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="englishName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="bankCode" type="Q1:BankCode" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="isOmniAccount" default="false" type="xsd:boolean" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="treasuryRate" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:decimal">
              <xsd:totalDigits value="14"/>
              <xsd:fractionDigits value="9"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="transCurrencyCode" type="Q1:ISOCurrencyCode" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="fill" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="deleteIPORecord" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="changeAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="journalNumber" type="Q1:JournalId" default="0" minOccurs="0"/>
        <xsd:element name="override" default="N" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="Y"/>
              <xsd:enumeration value="N"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="fxDeal" type="tns:FXDealType_11" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="amendReason" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GLToDepositTransferResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:GLToDepositTransferResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GLToDepositTransferResponseType">
    <xsd:all>
      <xsd:element name="journalNumber" type="Q1:JournalId" minOccurs="0"/>
      <xsd:element name="journalReversalNumber" type="Q1:JournalId" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:element name="OutgoingSWIFTTransferRequest" type="tns:PayToBeneficiaryRequestType"/>
  <xsd:element name="OutgoingSWIFTTransferResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:TransferResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InSWIFTOutgoingSARIETransferRequest" type="tns:PayToBeneficiaryRequestType"/>
  <xsd:element name="InSWIFTOutgoingSARIETransferResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:TransferResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="FeesRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="accountId" type="Q1:AccountId" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="type" maxOccurs="1" minOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="20"/>
              <xsd:enumeration value="SWIFT"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="countryCode" type="Q1:ISOCountryCode" maxOccurs="1" minOccurs="1"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="FeesResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="sucess" type="tns:FeeResponseType"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="FeeResponseType">
    <xsd:sequence>
      <xsd:element name="fee" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="valueDate" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="status" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="IntraTransferRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="fromAccountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="toAccountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="fromAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="toAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="description" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="description2" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="override" minOccurs="1" maxOccurs="1" default="Y">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="Y"/>
              <xsd:enumeration value="N"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="treasuryRate" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:decimal">
              <xsd:totalDigits value="14"/>
              <xsd:fractionDigits value="9"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="postDate" type="Q1:Date" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="journalId" type="Q1:JournalId" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="shortCIF" type="Q1:ShortCIF" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="promoCode" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="baseCurrencyAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="serviceFee" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="atmTime" type="Q1:TimeZone" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="atmDate" type="Q1:Date" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="atmReference" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="atmTerminal" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="atmCardId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="fxDeal" minOccurs="0" maxOccurs="1" type="tns:FXDealType1"/>
        <xsd:element name="AMLPurposeCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="overrideFlag" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="categoryOfTransaction" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="secondLevelPurpose" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="relationships" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="isBenRegistered" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Value should be Y - for Registered
							beneficiaries and N - Unregistered
							beneficiaries</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="virtualAccountId" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Virtual Account Id for ICustody Project .</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="IntraTransferResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:IntraTransferResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="IntraTransferResponseType">
    <xsd:sequence>
      <xsd:element name="fromAccountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="fromBalance" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="toBalance" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="journalId" type="Q1:JournalId" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="fxDealReference" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="IntraBeneficiaryTransferResponseType">
    <xsd:sequence>
      <xsd:element name="benficiaryId" type="Q1:BeneficiaryId" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="fromAccountId" type="Q1:AccountId" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="fromBalance" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="toBalance" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="journalId" type="Q1:JournalId" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="fxDealReference" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="customerId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="customerIdType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="customerName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="customerName2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="record" type="tns:BeneficiaryType" maxOccurs="unbounded" minOccurs="0"/>
      <xsd:element name="totalBeneficiaryRecords" type="xsd:int" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="BeneficiaryType">
    <xsd:all>
      <xsd:element name="cif" type="Q1:CIFOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryAccountCurrency" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryAccountNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryBankCountry" type="Q1:ISOCountryCodeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryBankCity" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryBankId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryCity" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryId" type="xsd:int" maxOccurs="1" minOccurs="0" default="0"/>
      <xsd:element name="beneficiaryName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryNickName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryRef" type="xsd:int" maxOccurs="1" minOccurs="0" default="0"/>
      <xsd:element name="branchSortCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryBankName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="corpId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="swiftbic" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="status" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="reasonCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="postalAddress" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryPhone" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="remitterName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="remitterCity" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="remitterCountry" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="routeCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="remitterAddress" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryZip" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryState" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryCountry" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="iBanApprove" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryFax" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="beneficiaryBankBranchId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:element name="InterTransferRequest" type="tns:InterTransferRequestType"/>
  <xsd:complexType name="InterTransferRequestType">
    <xsd:sequence>
      <xsd:element name="accountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="uti" type="Q1:Max16Text" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="debitAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="debitCurrency" type="Q1:ISOCurrencyCode" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="transferAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="transferCurrency" type="Q1:ISOCurrencyCode" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="commission" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="beneficiaryName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="beneficiaryAccountId" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="beneficiaryBranch" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="beneficiaryAddress1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="beneficiaryAddress2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="beneficiaryAddress3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="swiftBIC" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankRouteCode" minOccurs="0" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="20"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="bankName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankAddress1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankAddress2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankAddress3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankIdBIC" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="instructions1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="instructions2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="instructions3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="instructions4" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterAddress1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterAddress2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterAddress3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterPhone" type="Q1:PhoneNumber" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="baseCurrencyAmount" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="serviceFee" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="chargeType" minOccurs="0" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="PAY"/>
            <xsd:enumeration value="URG"/>
            <xsd:enumeration value="WORLD_REG"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="bankCharge" minOccurs="0" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="SHA"/>
            <xsd:enumeration value="BEN"/>
            <xsd:enumeration value="REM"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="accountWithOpt" type="xsd:string" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>Beneficiary bank ref type, default value 5 being
						used for MOF / GOVT payment.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="correspondingBankCharges" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="reasonCode" minOccurs="0" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="10"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="reasonDetail" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="isSariePayment" default="Y" minOccurs="1" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="Y"/>
            <xsd:enumeration value="N"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="paymentDate" type="Q1:Date" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="treasuryRate" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:decimal">
            <xsd:totalDigits value="14"/>
            <xsd:fractionDigits value="9"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="fxDeal" type="tns:FXDealType1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AMLPurposeCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="overrideFlag" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="feeInRemAcctCurrency" type="Q1:Amount" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>fee in remitter account currency</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="paymentType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>identifies if MOF/GOVT payment or private,
						default value 100 being used for MOF/GOVT
						payment.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="depositorAccountId" type="Q1:AccountId" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>When MOF is received, TCS Payment sends
						depositor number in outgoing SWIFT rather than
						debtor account . Depositor Account Number. This
						will be populated in tag50 of MOF payment
						message</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="instructions5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="instructions6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="instructions7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="InterTransferResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:InterTransferResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="InterTransferResponseType">
    <xsd:sequence>
      <xsd:element name="journalId" type="xsd:string" minOccurs="1"/>
      <xsd:element name="transcationDate" type="Q1:Date" minOccurs="1"/>
      <xsd:element name="uti" type="Q1:Max16Text" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="fxDealReference" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterLanguage" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="FXDealType1">
    <xsd:sequence>
      <xsd:element name="dealId" type="xsd:string"/>
      <xsd:element name="provider" type="xsd:string"/>
      <xsd:element name="bidRateAmount" type="Q1:Amount"/>
      <xsd:element name="tradingEngineUser" type="xsd:string"/>
      <xsd:element name="quoteTimeOut" type="xsd:string"/>
      <xsd:element name="soapProcessorDn" type="xsd:string"/>
      <xsd:element name="pnlAmount" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="clientRate" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="FundInTransferRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="1" name="fromAccountId" type="Q1:AccountId"/>
        <xsd:element maxOccurs="1" minOccurs="1" name="toAccountId" type="Q1:AccountId"/>
        <xsd:element maxOccurs="1" minOccurs="1" name="fromAmount" type="Q1:Money"/>
        <xsd:element maxOccurs="1" minOccurs="1" name="toAmount" type="Q1:Money"/>
        <xsd:element maxOccurs="1" minOccurs="1" name="description" type="xsd:string"/>
        <xsd:element maxOccurs="1" minOccurs="1" name="shortCIF" type="Q1:ShortCIF"/>
        <xsd:element maxOccurs="1" minOccurs="1" name="transCurrencyCode" type="Q1:ISOCurrencyCode"/>
        <xsd:element name="atmInfo" type="tns:atmInfoType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="1" name="transactionAmount" type="Q1:Money"/>
        <xsd:element maxOccurs="1" minOccurs="1" name="transferPurpose1" type="xsd:string"/>
        <xsd:element name="AMLPurposeCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="type" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Account type (BGL)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="FundInTransferResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="success" type="tns:FundInTransferResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="FundInTransferResponseType">
    <xsd:sequence>
      <xsd:element name="fromAccountId" type="Q1:AccountId" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="fromBalance" type="Q1:MoneyOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="toBalance" type="Q1:MoneyOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="journalId" type="Q1:JournalId" minOccurs="1" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ScreenTransferRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="transactionId" minOccurs="1" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="64"/>
              <xsd:minLength value="1"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="paymentType" minOccurs="1" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="SINGLE"/>
              <xsd:enumeration value="MULTIPLE"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="paymentSubType" minOccurs="1" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="SARIE"/>
              <xsd:enumeration value="SWIFT"/>
              <xsd:enumeration value="KONDOR"/>
              <xsd:enumeration value="BANKTRADE"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="finData" type="xsd:string" minOccurs="1" maxOccurs="1"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="ScreenTransferResponse" type="tns:ScreenTransferResponseType"/>
  <xsd:complexType name="ScreenTransferResponseType">
    <xsd:sequence>
      <xsd:element name="screenTransfer" maxOccurs="1" minOccurs="0">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="transactionId" minOccurs="1" maxOccurs="1">
              <xsd:simpleType>
                <xsd:restriction base="xsd:string">
                  <xsd:maxLength value="64"/>
                  <xsd:minLength value="1"/>
                </xsd:restriction>
              </xsd:simpleType>
            </xsd:element>
            <xsd:element name="result" minOccurs="1" maxOccurs="1">
              <xsd:simpleType>
                <xsd:restriction base="xsd:string">
                  <xsd:enumeration value="HIT"/>
                  <xsd:enumeration value="NOHIT"/>
                </xsd:restriction>
              </xsd:simpleType>
            </xsd:element>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="FundOutRequestForwarderResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:FundOutRequestForwarderResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="FundOutRequestForwarderRequest" type="tns:FundOutRequestForwarderType"/>
  <xsd:complexType name="FundOutRequestForwarderType">
    <xsd:sequence>
      <xsd:element name="investmentAccountNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="toAccountNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fromAmount" type="Q1:MoneyOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="toAmount" type="Q1:MoneyOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="transactionTypeCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="toIBAN" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="text1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="text2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="text3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="FundOutRequestForwarderResponseType">
    <xsd:sequence>
      <xsd:element name="transactionStatus" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="transactionJournalNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <!--New Implementation WSDl for FundInTransferRequest
			<xsd:element name="FundInTransferRequest">
			  <xsd:complexType>
			    <xsd:sequence>
			      <xsd:element maxOccurs="1" minOccurs="1" name="fromAccountId" type="Q1:AccountId"/>
			      <xsd:element maxOccurs="1" minOccurs="1" name="toAccountId" type="Q1:AccountId"/>
			      <xsd:element maxOccurs="1" minOccurs="1" name="fromAmount" type="Q1:Money"/>
			      <xsd:element maxOccurs="1" minOccurs="1" name="toAmount" type="Q1:Money"/>
			      <xsd:element maxOccurs="1" minOccurs="0" name="description" type="xsd:string"/>
			      <xsd:element maxOccurs="1" minOccurs="0" name="shortCIF" type="Q1:ShortCIF"/>
			      <xsd:element maxOccurs="1" minOccurs="0" name="transCurrencyCode" type="Q1:ISOCurrencyCode"/>
			       <xsd:element default="Y" maxOccurs="1" minOccurs="1" name="override">
			        <xsd:simpleType>
			          <xsd:restriction base="xsd:string">
			            <xsd:enumeration value="Y"/>
			            <xsd:enumeration value="N"/>
			          </xsd:restriction>
			        </xsd:simpleType>
			      </xsd:element>		      
			      
			       <xsd:element name="tFxRate" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
			      <xsd:element maxOccurs="1" minOccurs="0" name="baseCurrencyAmount" type="Q1:Money"/>
			      <xsd:element maxOccurs="1" minOccurs="0" name="commissionAmount" type="Q1:Money"/>
			      <xsd:element maxOccurs="1" minOccurs="0" name="chargeAmount" type="Q1:Money"/>
				  <xsd:element maxOccurs="1" minOccurs="0" name="journalId" type="Q1:JournalId"/>
			      <xsd:element minOccurs="0" maxOccurs="1" name="segmentLimit" type="xsd:string" />
			      <xsd:element maxOccurs="1" minOccurs="0" name="postDate" type="Q1:Date"/>
        <xsd:element name="atmInformation" minOccurs="0"  maxOccurs="1" >
          <xsd:complexType>
            <xsd:sequence>            
			    <xsd:element maxOccurs="1" minOccurs="0" name="atmTime" type="Q1:TimeZone"/>
			    <xsd:element maxOccurs="1" minOccurs="0" name="atmDate" type="Q1:Date"/>
			    <xsd:element maxOccurs="1" minOccurs="0" name="atmReference" type="xsd:string"/>
			    <xsd:element maxOccurs="1" minOccurs="0" name="atmTerminal" type="xsd:string"/>
			    <xsd:element maxOccurs="1" minOccurs="0" name="atmCardId" type="xsd:string"/>	
			      		</xsd:sequence>
          </xsd:complexType>
        </xsd:element>
			      <xsd:element maxOccurs="1" minOccurs="0" name="transactionAmount" type="Q1:Money"/>			      			      
			      <xsd:element maxOccurs="1" minOccurs="0" name="transferPurpose1" type="xsd:string"/>
			       <xsd:element minOccurs="0" maxOccurs="1" name="statementNarrative" type="xsd:string" />
			      <xsd:element maxOccurs="1" minOccurs="0" name="promoCode" type="xsd:string"/>
			    </xsd:sequence>
			  </xsd:complexType>
			</xsd:element>
			
			
			
			<xsd:element name="FundInTransferResponse">
			  <xsd:complexType>
			    <xsd:sequence>
			      <xsd:element maxOccurs="1" minOccurs="0" name="success" type="tns:FundInTransferResponseType"/>
			      <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
			    </xsd:sequence>
			  </xsd:complexType>
			</xsd:element>

			<xsd:complexType name= "FundInTransferResponseType">
				    
			    <xsd:sequence>
					<xsd:element name="fromAccountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
					<xsd:element name="toAccountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
					<xsd:element name="fromBalance" type="Q1:Money" minOccurs="0" maxOccurs="1"></xsd:element>
					<xsd:element name="toBalance" type="Q1:Money" minOccurs="0" maxOccurs="1"></xsd:element>
					<xsd:element name="journalId" type="Q1:JournalId" minOccurs="1" maxOccurs="1"/>					
			      <xsd:element name="amountSR" type="Q1:Money" minOccurs="0" maxOccurs="1" />
			      <xsd:element name="aailyTotal" type="xsd:string" minOccurs="0" maxOccurs="1" />
			      <xsd:element name="aaily3rdParty" type="xsd:string" minOccurs="0" maxOccurs="1" />
      
				</xsd:sequence>
				
			  </xsd:complexType>-->
  <xsd:complexType name="LimitType">
    <xsd:sequence>
      <xsd:element name="isApplied" type="xsd:boolean" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="type" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="MonitorForScreeningRequest" type="tns:Message"/>
  <xsd:element name="MonitorForScreeningResponse" type="tns:response"/>
  <xsd:complexType name="Message">
    <xsd:sequence>
      <xsd:element name="correlationID" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="messageID" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="queue" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="message" type="xsd:string" maxOccurs="1" minOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="response">
    <xsd:sequence>
      <xsd:element name="OK" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="IntraBeneficiaryTransferRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="isBeneficiaryExist" type="Q1:Boolean"/>
        <xsd:element name="type" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>It allows List,Details and Transfer</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="beneficiaryType" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>It allows EFT or SWF</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fromAccount" type="Q1:AccountId"/>
        <xsd:element name="toAccount" type="Q1:AccountId"/>
        <xsd:element name="fromAmount" type="Q1:Money"/>
        <xsd:element name="toAmount" type="Q1:Money">
          <xsd:annotation>
            <xsd:documentation>if Type is Transfer then it is Mandatory.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="atmTime" type="Q1:TimeZoneOptional" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>if Type is Transfer then it is Mandatory.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="atmDate" type="Q1:DateOptional" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>if Type is Transfer then it is Mandatory.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="atmReference" type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>if Type is Transfer then it is Mandatory.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="atmTerminal" type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>if Type is Transfer then it is Mandatory.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="quantity" type="xsd:int" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="nextRecordId" type="xsd:int" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="amlPurposeCode" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>if Type is Transfer then it is Mandatory.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="IntraBeneficiaryTransferResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:IntraBeneficiaryTransferResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="DepositAccountCreditRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="amount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="tranDate" minOccurs="0" maxOccurs="1" type="Q1:Date"/>
        <xsd:element name="promoCode" type="Q1:Max2Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="deferredIntDays" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:minInclusive value="0"/>
              <xsd:maxInclusive value="2"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <!--<xsd:element name="fromCurrencyCode"
    				type="Q1:ISOCurrencyCodeOptional" minOccurs="0" maxOccurs="1">
    			</xsd:element>
    			<xsd:element name="toAmount"
    				type="Q1:Money" minOccurs="0" maxOccurs="1">
    			</xsd:element>
    			<xsd:element name="toCurrencyCode"
    				type="Q1:ISOCurrencyCodeOptional" minOccurs="0" maxOccurs="1">
    			</xsd:element>-->
        <xsd:element name="baseCurrencyAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="commissionAmount" type="Q1:MoneyOptional" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="chargeAmount" type="Q1:MoneyOptional" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="rateType" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:minInclusive value="0"/>
              <xsd:maxInclusive value="2"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="currencyVersion" default="ZZZZ" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="4"/>
              <xsd:minLength value="4"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <!--<xsd:element name="journal"
    				type="Q1:JournalId" minOccurs="0" maxOccurs="1">
    			</xsd:element>-->
        <xsd:element name="description" type="Q1:Max30Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="narrative" type="Q1:Max50Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="voucherNumber" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="12"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="bookingNumber" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="7"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="treasuryRate" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:decimal">
              <xsd:totalDigits value="14"/>
              <xsd:fractionDigits value="9"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="batchPostingReferenceNumber" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="8"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="journalId" type="Q1:JournalId"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="DepositAccountCreditResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:CreditDepositAccountType" minOccurs="0" maxOccurs="1"/>
        <xsd:element ref="fault:fault" minOccurs="0" maxOccurs="1"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="CreditDepositAccountType">
    <xsd:sequence>
      <xsd:element name="accountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="journal" type="Q1:JournalId" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="accountName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="newBalance" type="Q1:Money" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="DepositAccountDebitRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountId" type="Q1:AccountId" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="amount" type="Q1:Money" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="tranDate" type="xsd:string"/>
        <xsd:element name="promoCode" type="xsd:string"/>
        <xsd:element name="baseCurrencyAmount" type="Q1:Money"/>
        <xsd:element name="commissionAmount" type="Q1:Money"/>
        <xsd:element name="chargeAmount" type="Q1:Money"/>
        <xsd:element name="rateType" type="xsd:string"/>
        <xsd:element name="currencyVersion" type="xsd:string"/>
        <xsd:element name="journalId" type="Q1:JournalId"/>
        <xsd:element name="description" type="xsd:string"/>
        <xsd:element name="deferredIntDay" type="xsd:string"/>
        <xsd:element name="narrative" type="xsd:string"/>
        <xsd:element name="voucherNo" type="xsd:string"/>
        <xsd:element name="bookingNum" type="xsd:string"/>
        <xsd:element name="batchId" type="xsd:string"/>
        <xsd:element name="treasuryRate">
          <xsd:simpleType>
            <xsd:restriction base="xsd:decimal">
              <xsd:totalDigits value="14"/>
              <xsd:fractionDigits value="9"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="batchPostingRefNo" type="xsd:string"/>
        <xsd:element name="benificiaryName" type="xsd:string"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="DepositAccountDebitResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:DepositAccountDebitType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="DepositAccountDebitType">
    <xsd:sequence>
      <xsd:element name="journalId" type="Q1:JournalId" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="fromBalance" type="Q1:Money" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="fromAccountId" type="Q1:AccountId" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="accountName" type="xsd:string" maxOccurs="1" minOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GLAccountCreditRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="fromAccountId" type="Q1:AccountId">
          <xsd:annotation>
            <xsd:documentation>Account Identification Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="amount" type="Q1:Money">
          <xsd:annotation>
            <xsd:documentation>Amount to be credit</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="description" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>Description of the transaction</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="voucherNo" type="Q1:Max35Text"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="baseCurrencyAmount" type="Q1:Money"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="rateType">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:totalDigits value="4"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="journalId" type="Q1:JournalId"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="retryFlag">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="Y"/>
              <xsd:enumeration value="N"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="override" default="N">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="Y"/>
              <xsd:enumeration value="N"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="batchPostingRefNo" type="xsd:string"/>
        <xsd:element name="promoCode" type="Q1:Max2Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="typeOfIncome" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Type of income</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GLAccountCreditResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="success" type="tns:AccountDebitType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="AccountDebitType">
    <xsd:sequence>
      <xsd:element name="accountId" type="Q1:AccountId" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="journal" type="Q1:JournalId" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="newBalance" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GLAccountDebitRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="fromAccountId" type="Q1:AccountId">
          <xsd:annotation>
            <xsd:documentation>Account Identification Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="amount" type="Q1:Money">
          <xsd:annotation>
            <xsd:documentation>Amount to be debit</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="description" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>Description of the transaction</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="voucherNo" type="Q1:Max35Text"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="baseCurrencyAmount" type="Q1:Money"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="rateType">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:totalDigits value="4"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="journalId" type="Q1:JournalId"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="retryFlag">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="Y"/>
              <xsd:enumeration value="N"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="override" default="N">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="Y"/>
              <xsd:enumeration value="N"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="batchPostingRefNo" type="xsd:string"/>
        <xsd:element name="promoCode" type="Q1:Max2Text" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="typeOfIncome" type="xsd:string" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Type of Income</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="vatAmount" type="Q1:AmountOptional" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Vat Amount</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GLAccountDebitResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="success" type="tns:AccountDebitType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GoldWithdrawlRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountNumber" type="Q1:AccountId" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Gold Account number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="goldQuantity" type="Q1:MoneyOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Gold quantity</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="totalFee" type="Q1:MoneyOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Total fee Amount applied(It will be fetched by GetFee Operation.Bancs14 will calculate the total fee amount)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="requestDate" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Gold request date</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="deliveryBranch" type="Q1:Branch" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Branch selected for delivery(It will be dynamic dropdown and fetched by operation RetrieveGoldDeliveryBranches)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="feeBearingAccount" type="Q1:AccountId" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Fee bearing account (It will be fetched by operation GetFee)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="taxRate" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Tax Rate  (It will be fetched by operation GetFee response)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="vat" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>vat (It will be fetched by operation GetFee response)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GoldWithdrawlResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:GoldWithdrawlResponseSuccessType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="ValueDateAndCutOffTimeRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountId" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Account Id</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="transactionType" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Clearing Type
								8 - SWIFT
								1 - SARIE
								3 - BULK File</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="1"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="beneficiaryCountry" type="Q1:ISOCountryCodeOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Beneficiary Country</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="paymentCurrency" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Payment Currency</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="transactionAmount" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Transaction Amount of payment in SAR
   							currency, as the charges are maintained
   							in local currency.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="bankCharge" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Charge option 1 - BEN, 2 - OUR, 3 -SHA</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="1"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="ValueDateAndCutOffTimeResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="success" type="tns:ValueDateAndCutOffTimeResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="ValueDateAndCutOffTimeResponseType">
    <xsd:sequence>
      <xsd:element name="dateTimeCutOffList" type="tns:dateCutOffArrayType" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="dateCutOffArrayType">
    <xsd:sequence>
      <xsd:element name="dateTimeCutOff" type="tns:dateCutOffType" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="dateCutOffType">
    <xsd:sequence>
      <xsd:element name="valueDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="1">
        <xsd:annotation>
          <xsd:documentation>Value Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="swiftFlag" maxOccurs="1" minOccurs="1">
        <xsd:annotation>
          <xsd:documentation>1 - SWIFT allowed for the given value
   							date 2 - SWIFT NOT allowed for the given
   							value date</xsd:documentation>
        </xsd:annotation>
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:length value="1"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="sarieFlag" maxOccurs="1" minOccurs="1">
        <xsd:annotation>
          <xsd:documentation>1 - SARIE Allowed for the given value
   							date 2 - SARIE NOT allowed for the given
   							value date</xsd:documentation>
        </xsd:annotation>
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:length value="1"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="cutOffTime" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Cut off time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cutOffTimeWithinBank" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>This field will be populated for Within Bank Records</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="GoldWithdrawlResponseSuccessType">
    <xsd:sequence>
      <xsd:element name="referenceNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Reference number for customer</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="deliveryDate" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Delivery Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="deliveryExpiryDate" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Delivery Expiry Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="atmInfoType">
    <xsd:sequence>
      <xsd:element name="time" type="Q1:MWTimeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="date" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="receipt" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="terminal" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cardnumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="TransferFundToBeneficiaryRequest" type="tns:TransferFundToBeneficiaryRequestType"/>
  <xsd:element name="TransferFundToBeneficiaryResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:TransferFundToBeneficiaryResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="TransferFundToBeneficiaryResponseType">
    <xsd:sequence>
      <xsd:element name="journalId" type="xsd:string" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="transcationDate" type="Q1:DateOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="uti" type="Q1:Max16Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="fxDealReference" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="fromAccountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="fromBalance" type="Q1:MoneyOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="toBalance" type="Q1:MoneyOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="treasuryRate" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="debitAmount" type="Q1:MoneyOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="clientRate" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="feeInRemAcctCurrency" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="feeClientRate" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="feeTreasuryRate" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TransferFundToBeneficiaryRequestType">
    <xsd:sequence>
      <xsd:element name="accountId" type="Q1:AccountId" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="debitCurrency" type="Q1:ISOCurrencyCode" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="transferAmount" type="Q1:Money" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="beneficiaryAccountId" type="Q1:Max35Text" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="beneficiaryName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="beneficiaryAddress1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="beneficiaryAddress2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="beneficiaryAddress3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankRouteCode" minOccurs="0" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="20"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="bankName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankAddress1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankAddress2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankAddress3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>countryCode</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bankIdBIC" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterName" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterAddress1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterAddress2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="remitterAddress3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="serviceFee" type="Q1:Money" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankCharge" minOccurs="0" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="SHA"/>
            <xsd:enumeration value="BEN"/>
            <xsd:enumeration value="REM"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="promoCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="paymentType" minOccurs="1" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="SARIE"/>
            <xsd:enumeration value="SWIFT"/>
            <xsd:enumeration value="FUNDTRAN"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="reasonCode" minOccurs="0" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="10"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="reasonDetail" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="paymentDate" type="Q1:Date" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>value date or credit currency date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="instructions1" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="instructions2" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="instructions3" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="instructions4" type="Q1:Max35Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="description " type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AMLPurposeCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="overrideFlag" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>It's override duplicate transaction notification and process transfer.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="treasuryTakerMemo" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Presently ecorp/b2bcorp prepares this string and sends in the InitiateFxDeal service input FXDealInitiateRequest/FXDeal/channel
					ex. ecorp format - NCB Branch @CustomerRegion@@ref
					b2bcorp - remitter name@/ref
					We're expecting this field here because this service will internally call InitiateFxDeal 
					service to convert fee in remitter account currency and calculate debit amount,baseCurrencyAmount in case of cross currency transaction.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="customerSegment" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Presently ecorp/b2bcorp maintains segment locally ex, ECORP_SME , B2B_DEFAULT etc..</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="categoryOfTransaction" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="secondLevelPurpose" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="relationships" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="InterBankTransactionInquiryRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountId" type="Q1:AccountId" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Customer account number(Payment
        							ordering customer)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="postingDate" type="Q1:MWDate" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Transaction posting date in core
        							banking system</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="journalId" type="Q1:JournalId" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>BaNCS Journal number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InterBankTransactionInquiryResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:InterBankTransactionInquirySuccessType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InterBankCancelledTransactionsInquiryRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountId" type="Q1:AccountId" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Customer account number(Payment
        							ordering customer)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="cif" type="Q1:CIFOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Customer Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="clearingType" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>CLEARING TYPE : 1:- SARIE
        							                8:- SWIFT</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fromDate" type="Q1:MWDate" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Transaction posting date in core
        							banking system</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="toDate" type="Q1:MWDate" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Transaction posting date in core
        							banking system</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="paymentStatusFlag" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Status of trasaction being enquired</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="pageSize" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Number of records per page</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="pageNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Page Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InterBankCancelledTransactionsInquiryResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:InterBankCancelledTransactionsInquirySuccessType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InterBankTransactionCancellationRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="requestTypeIndicator" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Field to signify type of
        							Cancellation</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="BANCSREFERENCE"/>
              <xsd:enumeration value="UTI"/>
              <xsd:enumeration value="JOURNALID"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="uniqueReference" type="xsd:string" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Unique Reference Number for the
        							request</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="reasonCode" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Contains reason code for cancellation</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="remarks" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Cancellation related remarks submitted by user</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InterBankTransactionCancellationResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0">
          <xsd:complexType/>
        </xsd:element>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="InterBankTransactionInquirySuccessType">
    <xsd:sequence>
      <xsd:element name="operationalUnitIdentifier" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Operational Unit Identifier</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accoundID" type="Q1:AccountId" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Customer account number(Payment ordering customer)</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="postingDate" type="Q1:MWDate" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Operational Unit Identifier</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="journalId" type="Q1:JournalId" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BaNCS Journal number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="paymentCancellationFlag" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Transaction(SWIFT/SARIE) being enquired may be cancelled or not.Would be populated only if transaction is already cancelled</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="paymenentStatus" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Current status of transaction being enquired  in BaNCS Payments</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="paymenentEventStatus" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Current event status of transaction being enquired  in BaNCS Payments</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cancellationDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Transaction cancellation date; if transaction being enquired already cancelled</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="orderDate" type="Q1:MWDate" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Order date for transaction being enquired</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="valueDate" type="Q1:MWDate" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Value date for transaction being enquired</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="channelId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Initiating channel for transaction being enquired</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cancellationFeeAmount" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Transaction cancellation Fee amount in SAR</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="uti" type="Q1:Max16Text" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>UTI for transaction being enquired</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="sourceReferenceNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Source Reference Number (as received in the payment initiation request). It will be mapped from Message Reference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="internalReferenceNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Unique Transaction Reference as generated by payments</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Type of transaction SWIFT or SARIE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="InterBankCancelledTransactionsInquirySuccessType">
    <xsd:sequence>
      <xsd:element name="transactions" type="tns:InterBankCancelledTransactionsInquiryTransactionType" maxOccurs="unbounded" minOccurs="0"/>
      <xsd:element name="totalNumberOfRecords" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="InterBankCancelledTransactionsInquiryTransactionType">
    <xsd:sequence>
      <xsd:element name="operationalUnitIdentifier" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Operational Unit Identifier</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accoundID" type="Q1:AccountId" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Customer account number(Payment ordering
        						customer)</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="paymentStatusFlag" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Status of transaction being enquired</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="paymenentStatus" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Current status of transaction being
        						enquired in BaNCS Payments</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="paymenentEventStatus" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Current event status of transaction
        						being enquired in BaNCS Payments</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cancellationDate" type="Q1:MWDate" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Transaction cancellation date; if
        						transaction being enquired already
        						cancelled</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="orderDate" type="Q1:MWDate" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Order date for transaction being
        						enquired</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="valueDate" type="Q1:MWDate" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Value date for transaction being
        						enquired</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="channelId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Initiating channel for transaction being
        						enquired</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="uti" type="Q1:Max16Text" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>UTI for transaction being enquired</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="sourceReferenceNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Source Reference Number (as received in
        						the payment initiation request). It will
        						be mapped from Message Reference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="internalReferenceNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Unique Transaction Reference as
        						generated by payments</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="currency" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Transfer currency of transaction</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionAmount" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Transfer amount of transaction</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Type of transaction SWIFT or SARIE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bankName" type="Q1:Max35Text" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Beneficiary bank name of transaction</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bankCountry" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Beneficiary bank country for transaction</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bankIdBIC" type="Q1:Max35Text" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Beneficiary Bank Reference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bankRouteCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Routing code for SWIFT clearing</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryAccountId" type="Q1:Max35Text" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Beneficiary account number of
        						transaction</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Beneficiary Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debitAccountCurrency" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>DEBIT Account Currency</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="FXDealType_11">
    <xsd:sequence>
      <xsd:element name="dealId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Deal ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="pnlAmount" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>profit and loss amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="TrackSwiftGPIPaymentRequest">
    <xsd:annotation>
      <xsd:documentation>SWIFT GPI Uniqueue end to end reference</xsd:documentation>
    </xsd:annotation>
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="GPIReference" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>SWIFT GPI Uniqueue end to end reference</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="transUTIReference" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Transaction UTI reference</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="accountId" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Customer account number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="transInitDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Transaction initiation Date</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="TrackSwiftGPIPaymentResponse">
    <xsd:complexType>
      <xsd:annotation>
        <xsd:documentation>Associated Charges deducted during the transaction cycle</xsd:documentation>
      </xsd:annotation>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0">
          <xsd:complexType>
            <xsd:sequence>
              <xsd:element name="transUTIReference" type="xsd:string" maxOccurs="1" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Transaction UTI
        										reference</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="paymentStatus" type="xsd:string" maxOccurs="1" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Payment Status</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="GPIStatus" type="xsd:string" maxOccurs="1" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>SWIFT GPI Status</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="accountId" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Customer account number</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="channel" type="xsd:string" maxOccurs="1" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Channel from which
        										payment was initiated</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="channelDescEN" type="xsd:string" maxOccurs="1" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Channel Description in English</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="channelDescAR" type="xsd:string" maxOccurs="1" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Channel Description in Arabic</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="transactionAmount" type="Q1:Money" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="exchangeRate" type="xsd:string" maxOccurs="1" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>exchange rate applied by NCB</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="chargeAmount" type="xsd:string" maxOccurs="1" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>NCB deducts</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="creditValueDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Credit Value Date of
        										SWIFT payment</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="orderDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Order Date (date when
        										payment was initiated)</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="beneficiaryAccountId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="beneficiaryName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="beneficiaryAddress" type="xsd:string" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="beneficiaryBankBIC" type="xsd:string" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="beneficiaryBankName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="paymentPendBankBIC" type="xsd:string" maxOccurs="1" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Payment Pending With
        										Bank</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="clearingType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="correspondBankBIC" type="xsd:string" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="correspondBankName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="lastActionDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="lastActionTime" type="xsd:string" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="associatedCharges" type="Q1:Amount" maxOccurs="1" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Associated Charges
        										deducted during the
        										transaction cycle</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="paymentStatusDescEN" type="xsd:string" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="paymentStatusDescAR" type="xsd:string" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="partyTracker" type="tns:trackerDetailsListType" maxOccurs="1" minOccurs="0"/>
            </xsd:sequence>
          </xsd:complexType>
        </xsd:element>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="trackerDetailsListType">
    <xsd:sequence>
      <xsd:element name="partyDetails" type="tns:trackerDetailsType" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="trackerDetailsType">
    <xsd:sequence>
      <xsd:element name="bankCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>GPI Sender Bank Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="senderReference" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="relatedReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Related Reference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="senderDeduction" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Deduction from other senders.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="exchangeRate" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>exchange rate applied by other parties</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="sendDateTime" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>tracker update Send Date time by sender</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GPIStatus" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>SWIFT GPI Status</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="paymentStatusDescEN" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>SWIFT GPI Status description - English</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="paymentStatusDescAR" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>SWIFT GPI Status description - Arabic</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="settlementAmount" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>GPI Settlement Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="MT103MessageRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="senderBankBIC" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Beneficairy Bank BIC</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="11"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="transUTIReferenceNumber" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Outgoing SWIFT Payment UTI reference</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="16"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="valueDate" maxOccurs="1" minOccurs="0" type="Q1:MWDateOptional">
          <xsd:annotation>
            <xsd:documentation>Current System Date</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="MT103MessageResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:MT103MessageResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="MT103MessageResponseType">
    <xsd:sequence>
      <xsd:element name="foundFlag" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>foundFlag can have below values
        					  Values      :  Description

        					  "1"         : Found in Backend Sytem(Payment Engine)
        					  "2"         : Not found in Backend System(Payment Engine)</xsd:documentation>
        </xsd:annotation>
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="1"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="transactionNumber" maxOccurs="1" minOccurs="0" type="Q1:Max16Text">
        <xsd:annotation>
          <xsd:documentation>Payments Engine internal transaction
        						reference number.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="orderDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Date on which this transaction message was received in TCS Payment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="valueDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Value date currently marked in TCS PE for this transaction</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transUTIReferenceNumber" maxOccurs="1" minOccurs="0" type="Q1:Max16Text">
        <xsd:annotation>
          <xsd:documentation>UTI reference number generated by NCB	bank.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transUTIReferenceNumber2" maxOccurs="1" minOccurs="0" type="Q1:Max16Text">
        <xsd:annotation>
          <xsd:documentation>Reference number generated by NCB bank	and only applicable when NCB generate	Cover message</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="correspondentBankCode" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>For out SWIFT--> it will be	correspondent bank to which message this	is further forwarded.

        						For out SARIE--> it will be receiver	local bank.</xsd:documentation>
        </xsd:annotation>
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="11"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="transactionCurrency" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Transaction Currency</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionAmount" maxOccurs="1" minOccurs="0" type="Q1:AmountOptional">
        <xsd:annotation>
          <xsd:documentation>Transaction Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryAccountNumber" maxOccurs="1" minOccurs="0" type="Q1:Max35Text">
        <xsd:annotation>
          <xsd:documentation>Beneficiary Account Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryName" maxOccurs="1" minOccurs="0" type="Q1:Max70Text">
        <xsd:annotation>
          <xsd:documentation>Beneficiary Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryAddress" maxOccurs="1" minOccurs="0" type="Q1:Max70Text">
        <xsd:annotation>
          <xsd:documentation>Beneficiary Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryBankCode" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>For outward payments, this will be	populated as beneficiary bank code.</xsd:documentation>
        </xsd:annotation>
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="15"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="beneficiaryBankName" maxOccurs="1" minOccurs="0" type="Q1:Max70Text">
        <xsd:annotation>
          <xsd:documentation>Bank Name corresponding to the Bank Code (for Beneficiary Bank Code)</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="remitterAccountNumber" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Remitter Account Number</xsd:documentation>
        </xsd:annotation>
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="40"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="remitterName" maxOccurs="1" minOccurs="0" type="Q1:Max70Text">
        <xsd:annotation>
          <xsd:documentation>Remitter Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="remitterCIF" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>CIF of debtor account. Not applicable for Inward.</xsd:documentation>
        </xsd:annotation>
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="20"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="originalTransactionalMessage" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Will contain complete original SWIFTmessage send to correspondent bank.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="counterAmount" maxOccurs="1" minOccurs="0" type="Q1:AmountOptional">
        <xsd:annotation>
          <xsd:documentation>Counter Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="exchangeRate" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Exchange Rate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="remitterAddress" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Remitter Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="chargeOption" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Charge Option</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ManageRTPaymentsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="messageHeader" type="tns:MessageHeaderType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="actionFlag" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>action Flag</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="remitterDetails" type="tns:RemitterDetailsType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="paymentDetails" type="tns:PaymentDetailsType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionChargeDetails" type="tns:TransactionChargeDetailsType" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="MessageHeaderType">
    <xsd:sequence>
      <xsd:element name="messageType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Message Type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="initiatingBranchId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Initiating Branch Id</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="schemeId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Scheme Id</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="requestType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Request Type   (  1 – Validate , 3 – Payment initiate )</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="serviceName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Service Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="location" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Location</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="acceptOrReject" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Accept Or Reject</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TransactionChargeDetailsType">
    <xsd:sequence>
      <xsd:element name="transactionChargeDetail" type="xsd:string" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ManageRTPaymentsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="success" type="tns:ManageRTPaymentsSuccessType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="RemitterDetailsType">
    <xsd:sequence>
      <xsd:element name="orderingCustomerAccount" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ordering Customer Account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="orderingCustomerName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ordering Customer Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="orderingCustomerAddressLine1" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ordering Customer Address Line1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="orderingCustomerAddressLine2" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ordering Customer Address Line2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="orderingCustomerAddressLine3" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ordering Customer Address Line3</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="customerIdentificationId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>customer Identification Id</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="executionDate" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="productType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PaymentDetailsType">
    <xsd:sequence>
      <xsd:element name="transactionReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>transaction Reference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryAccount" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiary Account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiary Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiary Reference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryAddressLine1" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiary Address Line1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryAddressLine2" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiary Address Line2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryAddressLine3" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiary Address Line3</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryAddressLine4" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiary Address Line4</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionAmount" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Transaction Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionCurrency" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Transaction Currency</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Transaction Type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryBankReferenceType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiary Bank Reference Type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryBankReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiary Bank Reference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="settlementRemarksLine1" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>settlement Remarks Line1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="settlementRemarksLine2" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>settlement Remarks Line2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryReferenceType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiary Reference Type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="endToEndReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>End To End Reference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="chargeCurrency" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Charge Currency</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="chargeAmount" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Charge Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="requestToPayReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Request To Pay Reference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ManageRTPaymentsSuccessType">
    <xsd:sequence>
      <xsd:element name="data" type="tns:DataType" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="DataType">
    <xsd:sequence>
      <xsd:element name="messageResponse" type="tns:MessageResponseType" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Message Response</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="MessageResponseType">
    <xsd:sequence>
      <xsd:element name="transactionPosType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>transaction Pos Type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionPosNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>transaction Pos Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="endToEndReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>end To End Reference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="successFlag" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>success Flag</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="instructionId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>instrid</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="messageId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>msgid</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="balance" type="Q1:Amount" maxOccurs="1" minOccurs="0" default="0">
        <xsd:annotation>
          <xsd:documentation>Balance</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetRTPaymentTransactionsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="arg0" type="tns:arg0Type" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="arg0Type">
    <xsd:sequence>
      <xsd:element name="schemaId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>schemaId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ouId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ouId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="sequenceNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>sequenceNumber</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionPOSNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>transactionPOSNumber</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionPOSType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>transactionPOSType</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="prtnGrp" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>prtn_Grp</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="prtnDate" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>prtn_Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="channelId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>channelId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="instrumentId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>instrumentId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="clrType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>clrType</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="stat" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>stat</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="eventStat" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>eventStat</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="inpQueueType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>inp_Queue_Typ</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionDetail" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>transactionDtl</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="orderDate" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orderDate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="orderTime" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>orderTime</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="valueDate" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>valueDate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="executionDate" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>executionDate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="messageId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>msg_Id</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="e2EReferenceId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>e2E_Ref_Id</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="instructionId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>instr_Id</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="uniqueSequenceNumber" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>unq_Seq_Num</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="referenceNumber" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ref_Num</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="currency" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>crncy</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="amount" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>amt</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="exchangeRate" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>exchng_Rt</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cntrCurrency" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>cntr_Crncy</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cntrAmount" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>cntr_Amt</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="preAgrdFlg" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>pre_Agrd_Flg</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="chrgOption" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>chrg_Optn</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="wavChrg" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>wav_Chrg</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="paymentType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>pymnt_Typ</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="mktCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>mkt_Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>txn_Typ</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="instructionType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>instr_Type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="customerType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>cust_Type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="initMUId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>INIT_MU_ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalAgentType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Agnt_Typ</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalAgentReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Agnt_Ref</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalAgentName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Agnt_Nm</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalAgentAddressLine1" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Agnt_Adrs_LN1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalAgentAddressLine2" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Agnt_Adrs_LN2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalAgentAddressLine3" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Agnt_Adrs_LN3</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Prty_Nm</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyCIFId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Prty_Cif_Id</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyCustomerReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Prty_Cust_Ref</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyAccountMUId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ORIG_PRTY_ACNT_MU_ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyAccountCurrency" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Prty_Acnt_Crncy</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyAccountPOSId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Prty_Acnt_Pos_Id</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyAccountPOSType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Prty_Acnt_Pos_Typ</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyIntReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Prty_Int_Ref</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyExtReferenceType1" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Prty_Ext_Ref_Typ_1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyExtReference1" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Prty_Ext_Ref_1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyExtReferenceType2" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Prty_Ext_Ref_Typ_2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyExtReferenc2" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Prty_Ext_Ref_2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyExtReferenceType3" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Prty_Ext_Ref_Typ_3</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyExtReference3" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Prty_Ext_Ref_3</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyAddressLine1" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Prty_Adrs_LN1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyAddressLine2" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Prty_Adrs_LN2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyAddressLine3" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>orig_Prty_Adrs_LN3</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="originalPartyAdditionalDetails" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>originalPartyAdditionaDetails</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryAgentType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiaryAgentType</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryAgentReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiaryAgentReference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryAgentName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiaryAgentName</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryAgentAddressLine1" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ben_Agnt_Adrs_LN1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryAgentAddressLine2" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ben_Agnt_Adrs_LN2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryAgentAddressLine3" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ben_Agnt_Adrs_LN3</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiaryPartyName</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyCIFId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiaryPartyCIFId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyCustomerRef" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiaryPartyCustomerRef</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyAccountMUId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BEN_PRTY_ACNT_MU_ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyAccountCurrency" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiaryPartyAccountCurrency</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyAccountPOSId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiaryPartyAccountPOSId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyAccountPOSType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiaryPartyAccountPOSType</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyIntReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiaryPartyIntReference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyExtReferenceType1" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ben_Prty_Ext_Ref_Typ_1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyExtReference1" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ben_Prty_Ext_Ref_1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyExtReferenceType2" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ben_Prty_Ext_Ref_Typ_2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyExtReference2" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ben_Prty_Ext_Ref_2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyExtReferenceType3" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ben_Prty_Ext_Ref_Typ_3</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyExtReference3" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ben_Prty_Ext_Ref_3</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyAddressLine1" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ben_Prty_Adrs_LN1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyAddressLine2" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ben_Prty_Adrs_LN2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyAddressLine3" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ben_Prty_Adrs_LN3</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryPartyAdditionalDetails" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>beneficiaryPartyAdditionalDetails</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="intermediaryAgentType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>intermediaryAgentType</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="intermediaryAgentReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>intermediaryAgentReference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="intermediaryAgentName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>intermediaryAgentName</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="intermediaryAgentAddressLine1" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>INTRMDRY_AGNT_ADRS_LN1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="intermediaryAgentAddressLine2" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>INTRMDRY_AGNT_ADRS_LN2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="intermediaryAgentAddressLine3" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>INTRMDRY_AGNT_ADRS_LN3</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ordrngInstnType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ordrng_InstnType</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ordngInstnReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ordng_InstnReference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ordngInstnName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ordng_InstnName</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ordngInstnAddressLine1" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ordng_InstnAddressLine1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ordngInstnAddressLine2" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ordng_InstnAddressLine2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ordngInstnAddressLine3" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ordng_InstnAddressLine3</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="mirrAccountCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>mirrAccountCode</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cntrPartyIntAccountTyp" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>cntr_PartyIntAccountTyp</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cntrPartyIntAccountRef" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>cntr_PartyIntAccountRef</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lastPrcsdSequenceId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>lastPrcsd_SeqId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lastPrcsdChannelId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>lastPrcsdChannelId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lastPrcsdMessageId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>lastPrcsd_MsgId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cashBlckReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>cash_BlckReference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="purposeCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>purposeCode</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="remitterInformation" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>remitterInformation</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="rtrnReferenceNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>rtrnReferenceNumber</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="rltdRemitterEventStat" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>rltdRemitterEventStat</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="rltdRemitterExecutionDate" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>rltdRemitterExecutionDate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="rltdRemitterPOSNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>rltdRemitterPOSNumber</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="rltdRemitterStat" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>rltdRemitterStat</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="alias" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>alias</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>aliasId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasCategory" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>aliasCategory</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="messageOriginalParty" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>messageOriginalParty</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="messageType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>messageType</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="returnFlg" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>returnFlg</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="netAmountAvailable" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>netAmountAvailable</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="refundedAmount" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>refundedAmount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="refundAmount" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>refundAmount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cnsldtnBkng" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>CNSLDTN_BKNG</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lotReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>lot_Ref</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bkngReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>bkngReference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="settlementCycleId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>settlementCycleId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="mbrSequenceNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>mbrseqno</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="mnlIntervention" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>MNL_INTRVNTN</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="chrgType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>chrgType</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="chrgEventType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>CHRG_EVNT_TYP</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="chrgAmount" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>CHRG_AMT</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="dupHash" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>dup_Hash</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="remarks" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>rmrks</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="rtpPayOrderVer" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>rtp_pay_order_ver</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="reasonCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>rsn_Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="reasonDescription" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>rsn_Dscrptn</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetRTPaymentTransactionsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="success" type="tns:GetRTPaymentTransactionsSuccessType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetRTPaymentTransactionsSuccessType">
    <xsd:sequence>
      <xsd:element name="data" type="tns:arg0Type" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="InquireProxyDetailRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountReference" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>accountReference</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="aliasReference1" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>aliasReference1</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InquireProxyDetailResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="success" type="tns:InquireProxyDetailSuccessType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="InquireProxyDetailSuccessType">
    <xsd:sequence>
      <xsd:element name="response" maxOccurs="unbounded" minOccurs="0" type="tns:ResponseType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ResponseType">
    <xsd:sequence>
      <xsd:element name="creationDate" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>creationDate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="activationDate" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>activationDate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasId" type="xsd:int" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>aliasId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasType1" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>aliasType1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasReference1" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>aliasReference1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="displayName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>displayName</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasType2" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>aliasType2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasReference2" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>aliasReference2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountType" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>accountType</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountReference" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>accountReference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>accountName</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountCurrency" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>accountCurrency</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="status" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>status</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="eventStatus" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>eventStatus</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="registrationId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>registrationId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="isBlocked" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>isBlocked</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="firstName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>firstName</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="secondName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>secondName</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lastName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>lastName</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="businessName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>businessName</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="FetchRTParticipantBanksRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="moInpPpLocalDirectory" type="tns:InpPPLocalDirectoryType" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>MO_INP_PP_LCLDir</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="ppLocalDirectory" type="tns:PPLocalDirectoryType" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>PP_LCL_DIRECTORY</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="pageSize" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Page_Size</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="pageNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Page_Num</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="pagesToExport" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Pages_To_Export</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="userId" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>User_ID</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="exceptionArray" type="tns:ExceptionArrayType" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>EXCEPTION_ARRAY</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="InpPPLocalDirectoryType">
    <xsd:sequence>
      <xsd:element name="bcNumber" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>BC_NUMBER</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="codeType" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>CODE_TYP</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="shortName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>SHORT_NAME</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="attribute1" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ATTR1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="attribute2" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ATTR2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="branchCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>BRANCH_CODE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repMain" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REP_MAIN</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repMainAddressType" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REP_MAIN_ADDR_TYP</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repBankUnit" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REP_BNK_UNIT</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repBankUnitBranch" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REP_BNK_UNIT_BRANCH</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repBankUnitCountry" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REP_BNK_UNIT_CNTRY</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repBranch" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REP_BRANCH</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repBankAddressType" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REP_BNK_ADDRTYP</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="countryCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>CNTRY_CODE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bankCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>BANK_CODE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="landCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>LAND_CODE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bicCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>BIC_CODE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="prtcptnFlag" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>PRTCPTN_FLG</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="location" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>LOCATION</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="stat" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>STAT</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="flag" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>FLAG</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repMainCountry" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REP_MAIN_CNTRY</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="state" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>State</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="city" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>City</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="altBCNumber" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Alt_BCNumber></xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="isBlocked" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>IS_BLOCKED</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bcNumberRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>BC_NUMBER_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="branchCodeRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>BRANCH_CODE_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="codeTypeRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>CODE_TYP_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="shortNameRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>SHORT_NAME_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bankCodeRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>BANK_CODE_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="statRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>STAT_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="isBlockedRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>IS_BLOCKED_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="altBCNumberRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>Alt_BCNumber_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cityRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>City_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="stateRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>State_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="locationRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>LOCATION_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bicCodeRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>BIC_CODE_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="countryCodeRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>CNTRY_CODE_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="landCodeRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>LAND_CODE_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repBankUnitRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>REP_BNK_UNIT_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="prtcptnFlagRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>PRTCPTN_FLG_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repBranchRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>REP_BRANCH_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repBankAddrTypeRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>REP_BNK_ADDRTYP_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repBankUnitBranchRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>REP_BNK_UNIT_BRANCH_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repBankUnitCountryRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>REP_BNK_UNIT_CNTRY_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repMainRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>REP_MAIN_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repMainCountryRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>REP_MAIN_CNTRY_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repMainAddrTypeRangeValue" maxOccurs="1" minOccurs="0" type="tns:RangeValueType">
        <xsd:annotation>
          <xsd:documentation>REP_MAIN_ADDR_TYP_RNGVAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="RangeValueType">
    <xsd:sequence>
      <xsd:element name="dFromValue" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>d_FromValue</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="dToValue" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>d_ToValue</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="dRangeOpr" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>d_RangeOpr</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="dRangeFlag" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>d_RangeFlag</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PPLocalDirectoryType">
    <xsd:sequence>
      <xsd:element name="bankCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>BANK_CODE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bcNumber" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>BC_NUMBER</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bicCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>BIC_CODE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="codeType" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>CODE_TYP</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="instAddressLine1" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>INST_ADRS_LN1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="instAddressLine2" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>INST_ADRS_LN2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="instAddressLine3" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>INST_ADRS_LN3</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="landCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>LAND_CODE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="location" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>LOCATION</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="shortName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>SHORT_NAME</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="codeTypeCd" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>CODE_TYP_CD</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="branchCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>BRANCH_CODE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repBankUnit" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REP_BNK_UNIT</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repBranch" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REP_BRANCH</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repBankAddressType" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REP_BNK_ADDRTYP</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="locationCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>LCTN_CODE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="origin" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ORIGIN</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="updateDate" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>UPDATE_DT</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="activationDate" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ACTVTN_DT</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="deactivationDate" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>DACTVTN_DT</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="prtcptnFlag" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>PRTCPTN_FLG</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="attribute1" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ATTR_1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="attribute2" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ATTR_2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="attribute3" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ATTR_3</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="attribute4" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ATTR_4</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repBankUnitBranch" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REP_BNK_UNIT_BRANCH</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repBankUnitCountry" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REP_BNK_UNIT_CNTRY</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repMain" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REP_MAIN</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repMainCountry" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REP_MAIN_CNTRY</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="repMainAddressType" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REP_MAIN_ADDR_TYP</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="countryCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>CNTRY_CODE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="physicalAddress" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>PHYSICAL_ADRS</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="physicalAdrsAddrType" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>PHYSICAL_ADRS_ADDR_TYP</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="newBICKey" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>NEW_BIC_KEY</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="localBranchCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>LCL_BRNCH_CODE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="localClrngCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>LCL_CLRNG_CODE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ppLocalSequenceId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>PP_LCL_SEQ_ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="stat" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>STAT</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionDetail" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>TXN_DTL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="isBlocked" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>IS_BLOCKED</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="contact1" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Contact1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="contact2" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Contact2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="contact3" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Contact3</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="branchNumber" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>BRNCH_NM</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="city" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>City</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="area" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Area</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionType" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>TxnTyp</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="clrType" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ClrTyp</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="altBCNumber" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Alt_BCNumber</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="clrCntrName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>CLR_CNTR_NM</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="state" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>State</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="altBCNumber2" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ALT_BC_NUMBER</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="issId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ISS_ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cityCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>CITY_CODE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="stateCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>STATE_CODE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="altBCReference" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ALT_BC_REF</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ppLocalDirectoryVer" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Pp_lcl_directory_ver</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ExceptionArrayType">
    <xsd:sequence>
      <xsd:element name="exception" maxOccurs="unbounded" minOccurs="0" type="tns:ExceptionType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ExceptionType">
    <xsd:sequence>
      <xsd:element name="boId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>BOId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="boNumber" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>BONum</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="attributeId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>AttrId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="instanceId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>InstanceId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="dAttributeValue" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>DAttrVal</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="mid" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Mid</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="FetchRTParticipantBanksResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="success" type="tns:FetchRTParticipantBanksSuccessType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="FetchRTParticipantBanksSuccessType">
    <xsd:sequence>
      <xsd:element name="ppLocalDirectoryArray" type="tns:PPLocalDirectoryArrayType" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>PP_LCL_DIRECTORY</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PPLocalDirectoryArrayType">
    <xsd:sequence>
      <xsd:element name="ppLocalDirectory" maxOccurs="unbounded" minOccurs="0" type="tns:PPLocalDirectoryType">
        <xsd:annotation>
          <xsd:documentation>ppLCLDirectory</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ManageProxyRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="alias" type="tns:AliasType" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="AliasType">
    <xsd:sequence>
      <xsd:element name="schemeId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>schemeId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creationDate" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>creationDate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Alias Id</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasType1" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>aliasType1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasReference1" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>aliasReference1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="displayName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>displayName ( ALIAS_SHRT_NM_1 )</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasType2" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>aliasType2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasReference2" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>aliasReference2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountType" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>accountType</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountReference" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>accountReference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>accountName</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountCurrency" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>accountCurrency</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="status" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>status</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="channelId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>channelId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="registrationId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>registrationId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="firstName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>firstName ( RSRV_STR_1 )</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="secondName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>secondName ( RSRV_STR_2 )</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lastName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>lastName ( RSRV_STR_3 )</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="businessName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>businessName ( CUST_NM )</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="actionFlag" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>actionFlag</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="reasonCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="reasonDescription" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ManageProxyResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="success" type="tns:ManageProxySuccessType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="ManageProxySuccessType">
    <xsd:sequence>
      <xsd:element name="response" maxOccurs="1" minOccurs="0" type="tns:ManageProxyResponseType">
        <xsd:annotation>
          <xsd:documentation>response</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ManageProxyResponseType">
    <xsd:sequence>
      <xsd:element name="creationDate" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>creationDate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="activationDate" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>activationDate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>aliasId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasType1" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>aliasType1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasReference1" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>aliasReference1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasType2" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>aliasType2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="aliasReference2" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>aliasReference2</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountReference" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>accountReference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountCurrency" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>accountCurrency</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="status" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>status</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="eventStatus" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>eventStatus</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="registrationId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>registrationId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="isBlocked" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>isBlocked</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ProxyLookupRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="payload" type="tns:PayloadType" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="PayloadType">
    <xsd:sequence>
      <xsd:element name="channelId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>SCHM_ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="proxyType" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ALIAS_REF_TYP_1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="proxyValue" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ALIAS_REF_NUM_1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ProxyLookupResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="success" type="tns:ProxyLookupSuccessType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="ProxyLookupSuccessType">
    <xsd:sequence>
      <xsd:element name="registrations" maxOccurs="unbounded" minOccurs="0" type="tns:RegistrationsType">
        <xsd:annotation>
          <xsd:documentation>registrations</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="RegistrationsType">
    <xsd:sequence>
      <xsd:element name="registrationId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>REG_ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="participantCode" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>RSRV_STR_1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="displayName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ALIAS_SHRT_NM_1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="proxy" maxOccurs="1" minOccurs="0" type="tns:ProxyType">
        <xsd:annotation>
          <xsd:documentation>proxy</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="account" maxOccurs="1" minOccurs="0" type="tns:AccountType">
        <xsd:annotation>
          <xsd:documentation>account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountUsg" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>acnt_Usg</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="requestId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>requestId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ProxyType">
    <xsd:sequence>
      <xsd:element name="type" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ALIAS_REF_TYP_1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="value" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ALIAS_REF_NUM_1</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AccountType">
    <xsd:sequence>
      <xsd:element name="type" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ACNT_TYP</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="value" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ACNT_REF</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="name" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ACNT_NM</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetRTPPaymentTypeRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="amount" maxOccurs="1" minOccurs="0" type="Q1:AmountOptional">
          <xsd:annotation>
            <xsd:documentation>Transfer amount</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="channelId" maxOccurs="1" minOccurs="0" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>Channel id</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="entity" maxOccurs="1" minOccurs="0" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>Entity Name</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="accountReference" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Account Reference</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetRTPPaymentTypeResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="success" type="tns:GetRTPPaymentTypeSuccessType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetRTPPaymentTypeSuccessType">
    <xsd:sequence>
      <xsd:element name="response" maxOccurs="1" minOccurs="0" type="tns:RTPPaymentTypeResponseType">
        <xsd:annotation>
          <xsd:documentation>response</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="RTPPaymentTypeResponseType">
    <xsd:sequence>
      <xsd:element name="paymentTypeRules" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>This is Payment type rules evaluated based
								 on amount and limit available(QL,PTL,STL)</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="FetchRTPLimitRequest"/>
  <xsd:element name="FetchRTPLimitResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="success" type="tns:FetchRTPLimitSuccessType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="FetchRTPLimitSuccessType">
    <xsd:sequence>
      <xsd:element name="data" maxOccurs="1" minOccurs="0" type="tns:FetchRTPLimitDataType">
        <xsd:annotation>
          <xsd:documentation>data</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="FetchRTPLimitDataType">
    <xsd:sequence>
      <xsd:element name="quickTransferLimit" maxOccurs="1" minOccurs="0" type="Q1:AmountOptional">
        <xsd:annotation>
          <xsd:documentation>AMT_THRSHLD   ( Amount Threshold )</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="systemTransferLimit" maxOccurs="1" minOccurs="0" type="Q1:AmountOptional">
        <xsd:annotation>
          <xsd:documentation>AVAIL_AMT  (Available Amount)</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="proxyTransferLimit" maxOccurs="1" minOccurs="0" type="Q1:AmountOptional">
        <xsd:annotation>
          <xsd:documentation>TXN_LMT_AMT   ( Transaction Limit Amount )</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="MaintainQTLLimitRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="function" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Function value will be "E". E:Enquiry
        							Function value will be "U". U:Update
        							Function value will be "C". C:Set</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="1"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="customerNumber" type="Q1:CIFOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Customer Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customerIPSQTLLimit" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Customer IPS QTL Limit</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="dailyQTLLimit" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Daily QTL Limit</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="MaintainQTLLimitResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="success" type="tns:MaintainQTLLimitSuccessType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="MaintainQTLLimitSuccessType">
    <xsd:sequence>
      <xsd:element name="customerNumber" maxOccurs="1" minOccurs="0" type="Q1:CIFOptional">
        <xsd:annotation>
          <xsd:documentation>Customer Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="customerIPSQTLLimit" maxOccurs="1" minOccurs="0" type="Q1:AmountOptional">
        <xsd:annotation>
          <xsd:documentation>Customer IPS QTL Limit</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="dailyQTLLimit" maxOccurs="1" minOccurs="0" type="Q1:AmountOptional">
        <xsd:annotation>
          <xsd:documentation>Daily QTL Limit</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lastUpdateDate" maxOccurs="1" minOccurs="0" type="Q1:MWDateOptional1">
        <xsd:annotation>
          <xsd:documentation>Last Update Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="MultiDebitMultiCreditRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="batchAmount" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="accountCurrencyCode" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="promoNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="statementNarrative" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="paymentReference" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="traceNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="paymentInformation" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="drCr1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="accountNumber1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="amount1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="valueDate1" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="iban1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionCurrency1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="exRate1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionAmount1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="baseAmount1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="paymentReference1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="senderToReceiver1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="statementNarrative1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="name1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="rebateAmount1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="drCr2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="accountNumber2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="amount2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="valueDate2" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="iban2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionCurrency2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="exRate2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionAmount2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="baseAmount2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="paymentReference2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="senderToReceiver2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="statementNarrative2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="name2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="rebateAmount2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="drCr3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="accountNumber3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="amount3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="valueDate3" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="iban3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionCurrency3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="exRate3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionAmount3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="baseAmount3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="paymentReference3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="senderToReceiver3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="statementNarrative3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="name3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="rebateAmount3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="drCr4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="accountNumber4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="amount4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="valueDate4" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="iban4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionCurrency4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="exRate4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionAmount4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="baseAmount4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="paymentReference4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="senderToReceiver4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="statementNarrative4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="name4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="rebateAmount4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="drCr5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="accountNumber5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="amount5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="valueDate5" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="iban5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionCurrency5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="exRate5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionAmount5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="baseAmount5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="paymentReference5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="senderToReceiver5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="statementNarrative5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="name5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="rebateAmount5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="drCr6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="accountNumber6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="amount6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="valueDate6" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="iban6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionCurrency6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="exRate6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionAmount6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="baseAmount6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="paymentReference6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="senderToReceiver6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="statementNarrative6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="name6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="rebateAmount6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="drCr7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="accountNumber7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="amount7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="valueDate7" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="iban7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionCurrency7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="exRate7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionAmount7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="baseAmount7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="paymentReference7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="senderToReceiver7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="statementNarrative7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="name7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="rebateAmount7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="drCr8" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="accountNumber8" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="amount8" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="valueDate8" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="iban8" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionCurrency8" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="exRate8" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionAmount8" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="baseAmount8" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="paymentReference8" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="senderToReceiver8" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="statementNarrative8" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="name8" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="rebateAmount8" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="drCr9" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="accountNumber9" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="amount9" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="valueDate9" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="iban9" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionCurrency9" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="exRate9" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionAmount9" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="baseAmount9" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="paymentReference9" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="senderToReceiver9" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="statementNarrative9" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="name9" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="rebateAmount9" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="drCr10" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="accountNumber10" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="amount10" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="valueDate10" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="iban10" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionCurrency10" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="exRate10" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transactionAmount10" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="baseAmount10" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="paymentReference10" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="senderToReceiver10" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="statementNarrative10" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="name10" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="rebateAmount10" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeFee1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeReb1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeFee2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeReb2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeFee3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeReb3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeFee4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeReb4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeFee5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeReb5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeFee6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeReb6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeFee7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeReb7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeFee8" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeReb8" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeFee9" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeReb9" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeFee10" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="typeOfIncomeReb10" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="MultiDebitMultiCreditResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="success" type="tns:MultiDebitMultiCreditSuccessType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="MultiDebitMultiCreditSuccessType">
    <xsd:sequence>
      <xsd:element name="journalNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ValidateBenAccountRTPRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="requestHeader" type="tns:RequestHeaderType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="beneficiaryBank" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Beneficiary Bank</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="beneficiaryIban" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Beneficiary IBAN</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="currency" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Currency</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="proxyType" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>eneficiary Proxy Type</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="proxyValue" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>eneficiary Proxy</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customerIdentification" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>identification of customer.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customerIdentificationType" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>this is used for identify the type of identification being used.
							                     Note that Iqama is populated in the Proprietary data element.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="schemeProprietaryDetails" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Name of the identification scheme</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="beneficiaryName" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Beneficiary Customer Name</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="beneficiaryAddress1" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Beneficiary Customer Address</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="beneficiaryAddress2" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Beneficiary Customer Address</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="beneficiaryAddress3" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Beneficiary Customer Address</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="beneficiaryAddress4" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Beneficiary Customer Address</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="beneficiaryAddress5" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Beneficiary Customer Address</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="beneficiaryAddress6" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Beneficiary Customer Address</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="beneficiaryAddress7" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Beneficiary Customer Address</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="beneficiaryIdentification" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Identification assigned by an institution</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="beneficiaryPrivateIdentification" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>eneficiary customer Unique and unambiguous identification, e.gpassport number,cif</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="beneficiaryOtherDetails" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Unique identification of a person, as assigned by an institution, using an identification scheme</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="beneficiaryPhoneNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Beneficiary Phone Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="beneficiaryMobileNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Beneficiary Mobile Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="relationshipCode" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>relationship to beneficiary Code</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="relationshipDescription" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>relationship to beneficiary Description</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customData1" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>customData1</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customData2" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>customData2</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customData3" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>customData3</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customData4" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>customData4</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customData5" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>customData5</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customData6" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>customData6</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="verificationUseCase" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>- ID_MATCH
								- ADD_BENEFICIARY
								- VERIFY_ACC_QUICK_TRANSFER
								- PROXY_ONLY_SP
								- PROXY_ONLY_BENEF_IBAN</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="feeAmount" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Fee Amount</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="feeCurrency" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Fee Currency</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="vatAmount" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>vat Amount</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="vatCurrency" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Vat Currency</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="action" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Account Finder Action: Can have below values
						          - INITIATE_ACCOUNT_FINDER
						          - INQUIRE_ACCOUNT_FINDER</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="RequestHeaderType">
    <xsd:sequence>
      <xsd:element name="requestId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="channelId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="version" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="customerId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="timeStamp" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="language" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="customerSegment" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>customer segment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ValidateBenAccountRTPResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:ValidateBenAccountRTPResponseSuccessType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="ValidateBenAccountRTPResponseSuccessType">
    <xsd:sequence>
      <xsd:element name="responseHeader" type="tns:ResponseHeaderType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="valid" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>valid</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="reasonCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Reason Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="reasonDescription" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Reason Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Beneficiary Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="beneficiaryNameMasked" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Beneficiary Name Masked</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element type="xsd:string" name="bankNameArabic" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Bank Name In Arabic</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element type="xsd:string" name="bankNameEnglish" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Bank Name In English</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element type="xsd:string" name="bankCode" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Beneficiary Bank BIC</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element type="xsd:string" name="participantCode" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Bank Participant Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element type="tns:AccountFinderResultType" name="accountFinderResult" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Account Finder Result</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AccountFinderResultType">
    <xsd:sequence>
      <xsd:element type="xsd:string" name="lastUpdated" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Last updated date and time : dd-MM-yyyy HH:mm:ss</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element type="xsd:string" name="maxRetriesExceeded" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Flag indicating if max customer account finder
						retries exceeded</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element type="tns:AccountFinderListType" name="accountFinderList" maxOccurs="unbounded" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>List of Account Verification Result obtained</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AccountFinderListType">
    <xsd:sequence>
      <xsd:element type="xsd:string" name="statusCode" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Status of Account Finder result
						- AVX00 (Account
						Exists)
						- AV02(Account Does Not Exist)
						-AVX01(Bank Is Not
						Responding)
						etc...</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element type="xsd:string" name="statusDescription" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Status description of Account Finder results
						-
						Account Exists
						- Account Does Not Exist
						- Bank Is Not Responding
						etc...</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element type="xsd:string" name="beneficiaryName" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Beneficiary Customer Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element type="xsd:string" name="beneficiaryNameMasked" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Beneficiary Customer Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element type="xsd:string" name="bankNameArabic" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Bank Name In Arabic</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element type="xsd:string" name="bankNameEnglish" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Bank Name In English</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element type="xsd:string" name="bankCode" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Beneficiary Bank BIC</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element type="xsd:string" name="participantCode" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Beneficiary Bank Participant Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ResponseHeaderType">
    <xsd:sequence>
      <xsd:element name="responseId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="channelId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="version" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="customerId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="timeStamp" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="language" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="status" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="description" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ActOnRequestToPayRTPRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="requestHeader" type="tns:RequestHeaderType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="reference" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>unique reference number of request provided by ips for the rtp exchange</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="action" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Status of the rtp exchange</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="reasonCode" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Reason Code behind invalidity of account</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="reasonDescription" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Reason Description</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="reminderDate" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Reminder Date</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="reminderPeriod" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Reminder Period</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="ActOnRequestToPayRTPResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:ActOnRequestToPayRTPSuccessType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="ActOnRequestToPayRTPSuccessType">
    <xsd:sequence>
      <xsd:element name="responseHeader" type="tns:ResponseHeaderType" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="InquireRequestToPayRTPRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="requestHeader" type="tns:RequestHeaderType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="reference" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>reference number of request to pay exchange</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fromDate" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>From Date</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="toDate" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Create Date of rtp exchange</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="status" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Status of the rtp exchange</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="direction" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Direction of RTP exchange, if incoming or outgoing</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="startIndex" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Start index of result set</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="resultsize" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>size of result set</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="internalReference" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Bank internal reference</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="creditorName" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>RTP Creditor Name</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="debtorName" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>RTP Debtor Name</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fromAmount" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>From Amount</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="toAmount" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>To Amount</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InquireRequestToPayRTPResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:InquireRequestToPayRTPSuccessType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="InquireRequestToPayRTPSuccessType">
    <xsd:sequence>
      <xsd:element name="responseHeader" type="tns:ResponseHeaderType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="numberOfRecords" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Number Of Records</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="rtpRecords" type="tns:RTPRecordsType" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="RTPRecordsType">
    <xsd:sequence>
      <xsd:element name="reference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>unique reference number of request provided by ips for the rtp exchange</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="direction" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Direction of RTP exchange, if incoming or outgoing</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="productCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>The product type code of the request to pay exchange</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="productDescription" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>The product type description of the request to pay exchange</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="createDate" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Create Date of rtp exchange</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="modifiedDate" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Modified Date of rtp exchange</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="status" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Status of the rtp exchange</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorIban" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>debtor iban</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>debtor name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorNameMasked" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>debtor name Masked</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="amount" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>requested amount to be payed</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="currency" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>currency</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="expiryDate" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Expiry Date of rtp exchange</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorBankBIC" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>debtor Bank BIC</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorBankName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>debtor Bank Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorBankBIC" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Creditor Bank BIC</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorBankName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Creditor Bank Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorIBAN" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Creditor IBAN</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Creditor Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorNameMasked" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Creditor Name Masked</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="requestDetailsCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Creditor Bank Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="requestDetailsDescription" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>request details description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="rejectionCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Rejection Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="rejectionDescription" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>request  description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="proxyReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>proxy Reference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="proxyType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>proxy Type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="proxyValue" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>proxy Value</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="instructionForDebtorCustomer" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>instruction For Debtor Customer</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="rtpReminderEnabled" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>rtp Reminder Enabled</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="internalReference" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Bank internal reference</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="InquireRTPProductTypesRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="requestHeader" type="tns:RequestHeaderType" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InquireRTPProductTypesResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:InquireRTPProductTypesSuccessType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="InquireRTPProductTypesSuccessType">
    <xsd:sequence>
      <xsd:element name="responseHeader" type="tns:ResponseHeaderType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="productTypes" type="tns:ProductTypesType" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ProductTypesType">
    <xsd:sequence>
      <xsd:element name="code" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Product Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="description" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Product Type Value</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="RequestToPayRTPRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="requestHeader" type="tns:RequestHeaderType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="productType" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Product Type Value</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="paymentMethod" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Payment Method, for time being should be set as TRF (credit transfer)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="creditorCustomerType" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Customer Type</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="expiryDate" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Expiry Date of rtp exchange yyyy-MM-dd</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="amount" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>amount requested in SAR</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="debtorCustomerIban" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>debtor customer iban</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="creditorCustomerIban" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>creditor customer iban</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="creditorCustomerAccountCurrency" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>creditor customer account currency</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="creditorCustomerAccountName" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Creditor customer account name</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="debtorCustomerType" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Customer Type</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="proxyType" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Proxy Type</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="proxyValue" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Proxy Value</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="debtorBank" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Debtor Bank</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="debtorCustomerDetails" type="tns:DebtorCustomerDetailsType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="creditorCustomerDetails" type="tns:CreditorCustomerDetailsType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="remittanceMethod" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Method used to deliver the remittance advice</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="remittanceInformation1" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Information supplied to enable the matching of an entry with 
							                     the items that the transfer is intended to settle,
							                     such as commercial invoices in an accounts' receivable system.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="remittanceDescription1" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>remittance Description1</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="remittanceInformation2" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>remittance Information2</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="remittanceDescription2" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>remittance Description2</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="remittanceInformation3" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>remittance Information3</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="remittanceDescription3" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>remittance Description3</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="remittanceGoal" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>remittance Goal</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="referredDocInfo" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>referred Doc Info</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="referredDocDate" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>referred Doc Date</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="purpose" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Purpose of RTP Request</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="purposeDescription" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Purpose Description of RTP Request</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="instructionForDebtorCustomer" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>coded instruction or instruction to the creditor's agent that is bilaterally agreed or specific to a user community</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="chargeBearer" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Specifies which party/parties will 
							 bear the charges associated with the processing of the payment transaction</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customerMobile" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Customer Contact Mobile to be used when sending notification</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customerEmail" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Customer Contact E-mail to be used when sending notification</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customData1" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>custom Data1</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customData2" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>custom Data2</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customData3" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>custom Data3</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customData4" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>custom Data4</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customData5" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>custom Data5</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="internalReference" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Bank internal reference</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="feeAmount" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Fee Amount</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="feeCurrency" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Fee Currency</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="vatAmount" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>vat Amount</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="vatCurrency" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Vat Currency</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="DebtorCustomerDetailsType">
    <xsd:sequence>
      <xsd:element name="debtorCustomerName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerAddressLine1" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerAddressLine2" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerAddressLine3" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerAddressLine4" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerAddressLine5" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerBirthday" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Birthday</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerProvinceOfBirth" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Province of Birth</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerCityOfBirth" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer City of Birth</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerCountryOfBirth" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Country of Birth</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerNamePrefix" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Specifies the terms used to formally address a person</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerPhoneNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Phone Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerMobileNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Mobile Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerFaxNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Fax Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerEmailAddress" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Email Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerOtherDetails" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Other Details</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerAddressType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Identifies the nature of the postal address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerStreetName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Street Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerBuildingNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Building Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerPostalCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Postal Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerTownName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Town Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerCountrySubDivision" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Identifies a subdivision of a country such as state, region, county</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerCountry" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Country</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="debtorCustomerIdentification" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Customer Identification</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CreditorCustomerDetailsType">
    <xsd:sequence>
      <xsd:element name="creditorCustomerName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerAddressLine1" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerAddressLine2" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerAddressLine3" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerAddressLine4" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerAddressLine5" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerBirthday" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Birthday</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerProvinceOfBirth" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Province of Birth</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerCityOfBirth" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer City of Birth</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerCountryOfBirth" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Country of Birth</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerNamePrefix" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Specifies the terms used to formally address a person</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerPhoneNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Phone Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerMobileNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Mobile Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerFaxNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Fax Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerEmailAddress" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Email Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerOtherDetails" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Other Details</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerAddressType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Identifies the nature of the postal address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerStreetName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Street Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerBuildingNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Building Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerPostalCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Postal Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerTownName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Town Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerCountrySubDivision" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Identifies a subdivision of a country such as state, region, county</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="creditorCustomerCountry" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>creditor Customer Country</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="RequestToPayRTPResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:RequestToPayRTPSuccessType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="RequestToPayRTPSuccessType">
    <xsd:sequence>
      <xsd:element name="responseHeader" type="tns:ResponseHeaderType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="reference" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="UpdateRTPStatusRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="requestHeader" type="tns:RequestHeaderType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="reference" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>unique reference number of request provided by ips for the rtp exchange</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="rtpStatus" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>updated Status of the rtp exchange .
							enum:
							  - ACCEPTED
							  - REJECTED
							  - CANCELLED
							  - EXPIRED
							  - PENDING
							  - ACCEPTED_BY_CUSTOMER
							  - PAID
							  - PAID_REJECTED
							  - ERROR</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="cbsStatus" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>updated CBS Status of the rtp exchange .
							enum:
							  - POSTED
							  - ERROR
							  - REVERSED
							  - SKIPPED</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="paymentReference" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>RTP Payment reference</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="UpdateRTPStatusResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CreditContingentAccountRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountNumber" type="Q1:AccountId" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Account number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="amount" type="Q1:Money" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Amount</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="date" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Date</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="promoNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Promo Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="transactionAmount" type="Q1:Money" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Transaction Amount</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="baseAmount" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Base Amount</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="commission" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Commission</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="change" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Change</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="rateType" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:minInclusive value="0"/>
              <xsd:maxInclusive value="2"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="currencyVersion" default="ZZZZ" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="4"/>
              <xsd:minLength value="4"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="description" type="xsd:string" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Description</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CreditContingentAccountResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:CreditContingentAccountResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="CreditContingentAccountResponseType">
    <xsd:sequence>
      <xsd:element name="journalId" type="Q1:JournalId" minOccurs="1" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="DebitContingentAccountRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountNumber" type="Q1:AccountId" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Account number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="amount" type="Q1:Money" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Amount</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="date" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Date</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="promoNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Promo Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="transactionAmount" type="Q1:Money" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Transaction Amount</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="baseAmount" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Base Amount</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="commission" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Commission</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="change" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Change</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="rateType" minOccurs="0" maxOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:minInclusive value="0"/>
              <xsd:maxInclusive value="2"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="currencyVersion" default="ZZZZ" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="4"/>
              <xsd:minLength value="4"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="description" type="xsd:string" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Description</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="DebitContingentAccountResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:DebitContingentAccountResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="DebitContingentAccountResponseType">
    <xsd:sequence>
      <xsd:element name="journalId" type="Q1:JournalId" minOccurs="1" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="TransferFundToCharityRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="fromAccountId" type="Q1:AccountIdOptional"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="fromCardId" type="Q1:EncryptedText"/>
        <xsd:element maxOccurs="1" minOccurs="1" name="toAccountId" type="Q1:AccountId"/>
        <xsd:element maxOccurs="1" minOccurs="1" name="amount" type="Q1:Money"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="promoCode" type="Q1:Max2Text"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="transactionDescription" type="xsd:string"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="currency" type="Q1:ISOCurrencyCodeOptional"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="AMLPurposeCode" type="xsd:string"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="productType">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="EHSAN"/>
              <xsd:enumeration value="ZAKATY"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="statementNarrative" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>Arabic UI: “حوالة داخلية صادرة - احسان”
											English UI: “Debit Internal Transfer-Ehsan”</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="commissionAmount" type="Q1:Money"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="chargeAmount" type="Q1:Money"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="TransferFundToCharityResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" minOccurs="0" maxOccurs="1" type="tns:TransferFundToCharityResponseType"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="TransferFundToCharityResponseType">
    <xsd:sequence>
      <xsd:element name="journalId" type="Q1:JournalId" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="authorizationCode" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="balanceAmount" default="0.0" type="Q1:Money" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetAvailableRitualsRequest" type="xsd:string"/>
  <xsd:element name="GetAvailableRitualsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:GetAvailableRitualsResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetAvailableRitualsResponseType">
    <xsd:sequence>
      <xsd:element name="ritualList" maxOccurs="unbounded" minOccurs="0" type="tns:RitualListType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="RitualListType">
    <xsd:sequence>
      <xsd:element name="id" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Unique Identifier per each Ritual.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="name" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Name of ritual translated as per the http header provided language  Ex.(Hady, SADAQA, ..).</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="brief" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetAvailableProductRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="resellerId" maxOccurs="1" minOccurs="0" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>ResellerId represent the id value of the reseller record in Adahi Database</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetAvailableProductResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:GetAvailableProductResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetAvailableProductResponseType">
    <xsd:sequence>
      <xsd:element name="productList" maxOccurs="unbounded" minOccurs="0" type="tns:ProductListType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ProductListType">
    <xsd:sequence>
      <xsd:element name="productId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Unique Identifier per each Ritual.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="productName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Name of Product translated as per the http header provided language</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="totalPrice" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetAdahiCountriesRequest" type="xsd:string"/>
  <xsd:element name="GetAdahiCountriesResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:GetAdahiCountriesResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetAdahiCountriesResponseType">
    <xsd:sequence>
      <xsd:element name="adahiCountryList" maxOccurs="unbounded" minOccurs="0" type="tns:AdahiCountryListType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AdahiCountryListType">
    <xsd:sequence>
      <xsd:element name="countryId" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="displayName" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="SubmitAdahiOrderRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element maxOccurs="1" minOccurs="0" name="fromAccountId" type="Q1:AccountIdOptional"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="fromCardId" type="Q1:EncryptedText"/>
        <xsd:element maxOccurs="1" minOccurs="1" name="toAccountId" type="Q1:AccountId"/>
        <xsd:element maxOccurs="1" minOccurs="1" name="amount" type="Q1:Money"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="promoCode" type="Q1:Max2Text"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="transactionDescription" type="xsd:string"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="currency" type="Q1:ISOCurrencyCodeOptional"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="AMLPurposeCode" type="xsd:string"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="productType">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="EHSAN"/>
              <xsd:enumeration value="ZAKATY"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="statementNarrative" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>Arabic UI: “حوالة داخلية صادرة - احسان”
											English UI: “Debit Internal Transfer-Ehsan”</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" name="commissionAmount" type="Q1:Money"/>
        <xsd:element maxOccurs="1" minOccurs="0" name="chargeAmount" type="Q1:Money"/>
        <xsd:element name="sellingPlace" maxOccurs="1" minOccurs="0" type="xsd:string"/>
        <xsd:element name="coupons" maxOccurs="unbounded" minOccurs="0" type="tns:OrderItemType"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="OrderItemType">
    <xsd:sequence>
      <xsd:element name="couponNumber" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>12 digits’ serial as following: 
													• First two digits are the Hijri Year 
													• Second two digits are the Reseller Number.
													• The fifth digit is the Adahi Product number 
													• The sixth digit is the selling method type = 1 for system to system integration 
													• Then last six digits for the coupon number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="serialNumber" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Represent the last six digit for the coupon number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="productId" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="productPrice" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>product totalPrice from the returned Product list</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="rituaId" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ritual id from the returned Ritual list</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="countryId" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="customerName" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="customerEmail" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="customerMobile" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>(Country Code + Mobile number) 
														Ex. KSA number (+966 55 939 
														9320)
														Ex. EGP number +20 128 935 
														2351)</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="purchaseDate" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="SubmitAdahiOrderResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:SubmitAdahiOrderResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="SubmitAdahiOrderResponseType">
    <xsd:sequence>
      <xsd:element name="journalId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="authorizationCode" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="balanceAmount" default="0.0" type="Q1:Money" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="UpdateCouponStatusRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="couponsStatus" maxOccurs="unbounded" minOccurs="0" type="tns:CouponStatusType"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="CouponStatusType">
    <xsd:sequence>
      <xsd:element name="couponNumber" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>12 digits’ serial as following: 
												• First two digits are the Hijri Year .
												• Second two digits are the Reseller Number.
												• The fifth digit is the Adahi Product number .
												• The sixth digit is the selling method type =1 for system to system integration .
												• Then last six digits for the coupon number .</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="couponStatus" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Status Code --  Description 
												1           --         PendingExecution
												2           --         OnHold
												3           --         Cancelled
												4           --         Executed .</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="statusChangeDate" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="comment" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="UpdateCouponStatusResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
</xsd:schema>