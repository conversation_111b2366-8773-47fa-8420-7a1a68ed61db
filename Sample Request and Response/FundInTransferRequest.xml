<soapenv:Envelope	xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"	xmlns:xsd="http://www.w3.org/2001/XMLSchema"	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<soapenv:Header>
		<ns1:Security			xmlns:ns1="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"			soapenv:actor="http://schemas.xmlsoap.org/soap/actor/next"			soapenv:mustUnderstand="0">
			<ns1:UsernameToken				soapenv:actor="http://schemas.xmlsoap.org/soap/actor/next"				soapenv:mustUnderstand="0">
				<ns1:Username>XXXX</ns1:Username>
				<ns1:Password>XXXX</ns1:Password>
			</ns1:UsernameToken>
		</ns1:Security>
		<ns2:ServiceHeader			xmlns:ns2="http://corp.alahli.com/middlewareservices/header/1.0/"			soapenv:actor="http://schemas.xmlsoap.org/soap/actor/next"			soapenv:mustUnderstand="0">
			<ns2:languageCode/>
		</ns2:ServiceHeader>
	</soapenv:Header>
	<soapenv:Body>
		<FundInTransferRequest			xmlns="http://corp.alahli.com/middlewareservices/payment/1.0/">
			<fromAccountId>**************</fromAccountId>
			<toAccountId>**************</toAccountId>
			<fromAmount				isoCurrencyCode="SAR">11</fromAmount>
			<toAmount				isoCurrencyCode="SAR">11</toAmount>
			<description>FINGBSPL-**************-F</description>
			<shortCIF>********</shortCIF>
			<transCurrencyCode>SAR</transCurrencyCode>
			<transactionAmount				isoCurrencyCode="SAR">11</transactionAmount>
			<transferPurpose1/>
		</FundInTransferRequest>
	</soapenv:Body>
</soapenv:Envelope>