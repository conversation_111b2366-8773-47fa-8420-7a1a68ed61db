<?xml version="1.0" encoding="UTF-8"?>
<schema targetNamespace="http://corp.alahli.com/schemas/middleware/temporaltypes/2012/10/29/" elementFormDefault="qualified" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://corp.alahli.com/schemas/middleware/temporaltypes/2012/10/29/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:element name="accountId" type="tns:AccountId"/>
  <xsd:element name="customerAddress" type="tns:PostalAddress"/>
  <xsd:element name="employeeAddesss" type="tns:PostalAddress"/>
  <xsd:simpleType name="ChannelType">
    <xsd:annotation>
      <xsd:documentation>Valid values of Channel :
				05,62 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="2"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="BBAN">
    <xsd:annotation>
      <xsd:documentation>Valid values of BBAN :
				**************,
				************** etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="14"/>
      <xsd:maxLength value="14"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EncryptedText">
    <xsd:annotation>
      <xsd:documentation>Represents encrypted data</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string"/>
  </xsd:simpleType>
  <xsd:simpleType name="EncryptedText2">
    <xsd:annotation>
      <xsd:documentation>Represents encrypted data passed from channels like IVR etc.</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string"/>
  </xsd:simpleType>
  <simpleType name="ECNId">
    <xsd:annotation>
      <xsd:documentation>Ecn Identification.
				Valid values are :
				23747091,
				12948435, 11408939 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="8"/>
      <xsd:maxLength value="8"/>
    </xsd:restriction>
  </simpleType>
  <simpleType name="ECNIdOptional">
    <xsd:annotation>
      <xsd:documentation>Ecn Identification.
				Valid values are :
				23747091,
				12948435, 11408939 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="0"/>
      <xsd:maxLength value="8"/>
    </xsd:restriction>
  </simpleType>
  <xsd:simpleType name="TimeZone">
    <xsd:annotation>
      <xsd:documentation>Represents the timezone offset from UTC
				Valid
				values are : +5:30 , -7:45 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[+'\-'.'](([01][0'\-'.'9])|(2[0'\-'.'3]))(:[0'\-'.'5][0'\-'.'9]){2}(\.[0'\-'.'9]+)?"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="TimeZoneOptional">
    <xsd:annotation>
      <xsd:documentation>Represents the timezone offset from UTC. Empty
				value is allowed.
				Valid values are : +5:30 , -7:45 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([+'\-'.'](([01][0'\-'.'9])|(2[0'\-'.'3]))(:[0'\-'.'5][0'\-'.'9]){2}(\.[0'\-'.'9]+)?)|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="DateTimeOptional2">
    <xsd:annotation>
      <xsd:documentation>Represents the date in the format of
				yyyy-mm-dd+hh:mm
				Valid values are : 2019-09-08+03:00 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="((000[1-9])|(00[1-9][0-9])|(0[1-9][0-9]{2})|([1-9][0-9]{3}))-((0[1-9])|(1[012]))-((0[1-9])|([12][0-9])|(3[01]))[+'\-'.'](0[0-9]|1[0-9]|2[0-3]|[0-9]):([0-5][0-9])|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="DateTimeOptional3">
    <xsd:annotation>
      <xsd:documentation>Represents the date in the format of
				yyyy-mm-ddThh:mm:ss
				Valid values are : 2019-09-08T03:00:25 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="((000[1-9])|(00[1-9][0-9])|(0[1-9][0-9]{2})|([1-9][0-9]{3}))-((0[1-9])|(1[012]))-((0[1-9])|([12][0-9])|(3[01]))[T](0[0-9]|1[0-9]|2[0-3]|[0-9])[:]([0-5][0-9])[:]([0-5][0-9])|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="DateTimeOptional4">
    <xsd:annotation>
      <xsd:documentation>Represents the date in the format of
				yyyy-mm-ddThh:mm:ss
				Valid values are : 2019-09-08 03:00:25 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="((000[1-9])|(00[1-9][0-9])|(0[1-9][0-9]{2})|([1-9][0-9]{3}))-((0[1-9])|(1[012]))-((0[1-9])|([12][0-9])|(3[01]))[ ](0[0-9]|1[0-9]|2[0-3]|[0-9])[:]([0-5][0-9])[:]([0-5][0-9])|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Date">
    <xsd:annotation>
      <xsd:documentation>Represents the date in the format of yyyy-mm-dd
				Valid values are : 2013-05-22 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="((000[1-9])|(00[1-9][0-9])|(0[1-9][0-9]{2})|([1-9][0-9]{3}))-((0[1-9])|(1[012]))-((0[1-9])|([12][0-9])|(3[01]))"/>
      <!--<xsd:pattern value="^(19[0-9]{2}|[2-9][0-9]{3})-((0(1|3|5|7|8)|10|12)-(0[1-9]|1[0-9]|2[0-9]|3[0-1])|(0(4|6|9)|11)-(0[1-9]|1[0-9]|2[0-9]|30)|(02)-(0[1-9]|1[0-9]|2[0-9]))T(0[0-9]|1[0-9]|2[0-3])(:[0-5][0-9]){2}$" 
				/>-->
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="DateFormat">
    <xsd:annotation>
      <xsd:documentation>Represents the date in the format of dd/mm/yyyy
				Valid values are : 22/05/2013 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="((0[1-9])|([12][0-9])|(3[01]))/((0[1-9])|(1[012]))/((000[1-9])|(00[1-9][0-9])|(0[1-9][0-9]{2})|([1-9][0-9]{3}))|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="DateFormat1">
    <xsd:annotation>
      <xsd:documentation>Represents the date in the format of yyyy/mm/dd
				Valid values are : 2013/05/22 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="((000[1-9])|(00[1-9][0-9])|(0[1-9][0-9]{2})|([1-9][0-9]{3}))/((0[1-9])|(1[012]))/((0[1-9])|([12][0-9])|(3[01]))|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="CustomNumeric">
    <xsd:annotation>
      <xsd:documentation>Represents the numeric value till 25 characters
				.Exmaple : 9999999999999999999999999</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:decimal">
      <xsd:maxInclusive value="9999999999999999999999999"/>
      <xsd:fractionDigits value="0"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="DateOptional">
    <xsd:annotation>
      <xsd:documentation>Represents the date in the format of yyyy-mm-dd.
				Empty value is allowed
				Valid values are : 2013-05-22 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="(((000[1-9])|(00[1-9][0-9])|(0[1-9][0-9]{2})|([1-9][0-9]{3}))-((0[1-9])|(1[012]))-((0[1-9])|([12][0-9])|(3[01])))|()"/>
      <!--<xsd:pattern value="^(19[0-9]{2}|[2-9][0-9]{3})-((0(1|3|5|7|8)|10|12)-(0[1-9]|1[0-9]|2[0-9]|3[0-1])|(0(4|6|9)|11)-(0[1-9]|1[0-9]|2[0-9]|30)|(02)-(0[1-9]|1[0-9]|2[0-9]))T(0[0-9]|1[0-9]|2[0-3])(:[0-5][0-9]){2}$" 
				/>-->
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MWDate">
    <xsd:annotation>
      <xsd:documentation>Represents the date in the format of yyyymmdd.
				Valid values are : 20130522 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]){8}"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="XLSDate">
    <xsd:annotation>
      <xsd:documentation>Represents the date in the format of mm-dd-yyyy.
				Empty value is not allowed
				Valid values are : 2013-05-22 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="(((0[1-9])|(1[012]))-((0[1-9])|([12][0-9])|(3[01]))-((000[1-9])|(00[1-9][0-9])|(0[1-9][0-9]{2})|([1-9][0-9]{3})))"/>
      <!--<xsd:pattern value="^(19[0-9]{2}|[2-9][0-9]{3})-((0(1|3|5|7|8)|10|12)-(0[1-9]|1[0-9]|2[0-9]|3[0-1])|(0(4|6|9)|11)-(0[1-9]|1[0-9]|2[0-9]|30)|(02)-(0[1-9]|1[0-9]|2[0-9]))T(0[0-9]|1[0-9]|2[0-3])(:[0-5][0-9]){2}$" 
				/>-->
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="XLSDateOptional">
    <xsd:annotation>
      <xsd:documentation>Represents the date in the format of mm-dd-yyyy.
				Empty value is allowed
				Valid values are : 2013-05-22 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="(((0[1-9])|(1[012]))-((0[1-9])|([12][0-9])|(3[01]))-((000[1-9])|(00[1-9][0-9])|(0[1-9][0-9]{2})|([1-9][0-9]{3})))|()"/>
      <!--<xsd:pattern value="^(19[0-9]{2}|[2-9][0-9]{3})-((0(1|3|5|7|8)|10|12)-(0[1-9]|1[0-9]|2[0-9]|3[0-1])|(0(4|6|9)|11)-(0[1-9]|1[0-9]|2[0-9]|30)|(02)-(0[1-9]|1[0-9]|2[0-9]))T(0[0-9]|1[0-9]|2[0-3])(:[0-5][0-9]){2}$" 
				/>-->
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MWDate1">
    <xsd:annotation>
      <xsd:documentation>Represents the date in the format of ddmmyyyy.
				Valid values are : 15052013 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]){8}|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MWDateOptional">
    <xsd:annotation>
      <xsd:documentation>Represents the date in the format of yyyymmdd.
				Empty value is allowed
				Valid values are : 20130522 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]){8}|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MWDateOptional1">
    <xsd:annotation>
      <xsd:documentation>Represents the date in the format of ddmmyyyy.
				Empty value is allowed
				Valid values are : 15052013 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]){8}|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MWDateOptional2">
    <xsd:annotation>
      <xsd:documentation>Represents the date in the format of yyyymmdd.
				Empty value is allowed
				Valid values are : 20130522 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]){8}|(0)"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="DateTimeOptional">
    <xsd:annotation>
      <xsd:documentation>Represents the date in the format of yyyymmdd.
				Empty value is allowed
				Valid values are : 20130522 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="(((000[1-9])|(00[1-9][0-9])|(0[1-9][0-9]{2})|([1-9][0-9]{3}))-((0[1-9])|(1[012]))-((0[1-9])|([12][0-9])|(3[01]))T([0-9]|0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9].[0-9])|()|(((000[1-9])|(00[1-9][0-9])|(0[1-9][0-9]{2})|([1-9][0-9]{3}))-((0[1-9])|(1[012]))-((0[1-9])|([12][0-9])|(3[01]))T([0-9]|0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9])"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MWTime">
    <xsd:annotation>
      <xsd:documentation>Represents the time in the format of hhmm or
				hhmmss (hh-hours ; mm - minutes; ss - seconds).
				Valid values are :
				053456 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]){4}|([0-9]){6}"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MWTimeFormat">
    <xsd:annotation>
      <xsd:documentation>Represents the time in the format of
				hh:mm:ss
				(hh-hours ; mm - minutes; ss - seconds).
				Valid values are :
				05:34:56
				etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[0-2][0-9]:[0-5][0-9]:[0-5][0-9]"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MWTimeOptional">
    <xsd:annotation>
      <xsd:documentation>Represents the time in the format of hhmm or
				hhmmss (hh-hours ; mm - minutes; ss - seconds).
				Empty value is
				allowed.
				Valid values are : 053456 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]){4}|([0-9]){6}|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ISOCurrencyCodeOptional">
    <xsd:annotation>
      <xsd:documentation>
        <!--The standard currency codes published by ISO. Empty value is also 
					allowed. The format is three upper case letters. Valid values are : AED, 
					SAR, USD, EUR etc. Below is the list of all ISO currency codes (Code - Country 
					Name ) AED United Arab Emirates Dirham AFN Afghanistan Afghani ALL Albania 
					Lek AMD Armenia Dram ANG Netherlands Antilles Guilder AOA Angola Kwanza ARS 
					Argentina Peso AUD Australia Dollar AWG Aruba Guilder AZN Azerbaijan New 
					Manat BAM Bosnia and Herzegovina Convertible Marka BBD Barbados Dollar BDT 
					Bangladesh Taka BGN Bulgaria Lev BHD Bahrain Dinar BIF Burundi Franc BMD 
					Bermuda Dollar BND Brunei Darussalam Dollar BOB Bolivia Boliviano BRL Brazil 
					Real BSD Bahamas Dollar BTN Bhutan Ngultrum BWP Botswana Pula BYR Belarus 
					Ruble BZD Belize Dollar CAD Canada Dollar CDF Congo/Kinshasa Franc CHF Switzerland 
					Franc CLP Chile Peso CNY China Yuan Renminbi COP Colombia Peso CRC Costa 
					Rica Colon CUC Cuba Convertible Peso CUP Cuba Peso CVE Cape Verde Escudo 
					CZK Czech Republic Koruna DJF Djibouti Franc DKK Denmark Krone DOP Dominican 
					Republic Peso DZD Algeria Dinar EGP Egypt Pound ERN Eritrea Nakfa ETB Ethiopia 
					Birr EUR Euro Member Countries FJD Fiji Dollar FKP Falkland Islands (Malvinas) 
					Pound GBP United Kingdom Pound GEL Georgia Lari GGP Guernsey Pound GHS Ghana 
					Cedi GIP Gibraltar Pound GMD Gambia Dalasi GNF Guinea Franc GTQ Guatemala 
					Quetzal GYD Guyana Dollar HKD Hong Kong Dollar HNL Honduras Lempira HRK Croatia 
					Kuna HTG Haiti Gourde HUF Hungary Forint IDR Indonesia Rupiah ILS Israel 
					Shekel IMP Isle of Man Pound INR India Rupee IQD Iraq Dinar IRR Iran Rial 
					ISK Iceland Krona JEP Jersey Pound JMD Jamaica Dollar JOD Jordan Dinar JPY 
					Japan Yen KES Kenya Shilling KGS Kyrgyzstan Som KHR Cambodia Riel KMF Comoros 
					Franc KPW Korea (North) Won KRW Korea (South) Won KWD Kuwait Dinar KYD Cayman 
					Islands Dollar KZT Kazakhstan Tenge LAK Laos Kip LBP Lebanon Pound LKR Sri 
					Lanka Rupee LRD Liberia Dollar LSL Lesotho Loti LTL Lithuania Litas LVL Latvia 
					Lat LYD Libya Dinar MAD Morocco Dirham MDL Moldova Leu MGA Madagascar Ariary 
					MKD Macedonia Denar MMK Myanmar (Burma) Kyat MNT Mongolia Tughrik MOP Macau 
					Pataca MRO Mauritania Ouguiya MUR Mauritius Rupee MVR Maldives (Maldive Islands) 
					Rufiyaa MWK Malawi Kwacha MXN Mexico Peso MYR Malaysia Ringgit MZN Mozambique 
					Metical NAD Namibia Dollar NGN Nigeria Naira NIO Nicaragua Cordoba NOK Norway 
					Krone NPR Nepal Rupee NZD New Zealand Dollar OMR Oman Rial PAB Panama Balboa 
					PEN Peru Nuevo Sol PGK Papua New Guinea Kina PHP Philippines Peso PKR Pakistan 
					Rupee PLN Poland Zloty PYG Paraguay Guarani QAR Qatar Riyal RON Romania New 
					Leu RSD Serbia Dinar RUB Russia Ruble RWF Rwanda Franc SAR Saudi Arabia Riyal 
					SBD Solomon Islands Dollar SCR Seychelles Rupee SDG Sudan Pound SEK Sweden 
					Krona SGD Singapore Dollar SHP Saint Helena Pound SLL Sierra Leone Leone 
					SOS Somalia Shilling SRD Suriname Dollar STD São Tomé and Príncipe Dobra 
					SVC El Salvador Colon SYP Syria Pound SZL Swaziland Lilangeni THB Thailand 
					Baht TJS Tajikistan Somoni TMT Turkmenistan Manat TND Tunisia Dinar TOP Tonga 
					Pa'anga TRY Turkey Lira TTD Trinidad and Tobago Dollar TVD Tuvalu Dollar 
					TWD Taiwan New Dollar TZS Tanzania Shilling UAH Ukraine Hryvna UGX Uganda 
					Shilling USD United States Dollar UYU Uruguay Peso UZS Uzbekistan Som VEF 
					Venezuela Bolivar VND Viet Nam Dong VUV Vanuatu Vatu WST Samoa Tala XAF Communauté 
					Financière Africaine (BEAC) CFA Franc BEAC XCD East Caribbean Dollar XDR 
					International Monetary Fund (IMF) Special Drawing Rights XOF Communauté Financière 
					Africaine (BCEAO) Franc XPF Comptoirs Français du Pacifique (CFP) Franc YER 
					Yemen Rial ZAR South Africa Rand ZMW Zambia Kwacha ZWD Zimbabwe Dollar-->
      </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([A-Z]{3,3})|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ISOCountryCode">
    <xsd:annotation>
      <xsd:documentation>International Standard for country codes. Empty
				value is NOT allowed.
				The format is two upper case letters. for ex
				SA, US, CA etc
				Below is the list of all ISO country codes (Country
				Name -- Code)

				AFGHANISTAN -- AF
				Ã…LAND ISLANDS -- AX
				ALBANIA -- AL
				ALGERIA -- DZ
				AMERICAN SAMOA -- AS
				ANDORRA -- AD
				ANGOLA -- AO
				ANGUILLA
				-- AI
				ANTARCTICA -- AQ
				ANTIGUA AND BARBUDA -- AG
				ARGENTINA -- AR
				ARMENIA --
				AM
				ARUBA -- AW
				AUSTRALIA -- AU
				AUSTRIA -- AT
				AZERBAIJAN -- AZ
				BAHAMAS --
				BS
				BAHRAIN -- BH
				BANGLADESH -- BD
				BARBADOS -- BB
				BELARUS -- BY
				BELGIUM --
				BE
				BELIZE -- BZ
				BENIN -- BJ
				BERMUDA -- BM
				BHUTAN -- BT
				BOLIVIA,
				PLURINATIONAL STATE OF -- BO
				BONAIRE, SINT EUSTATIUS AND SABA
				-- BQ
				BOSNIA AND HERZEGOVINA -- BA
				BOTSWANA -- BW
				BOUVET ISLAND -- BV
				BRAZIL
				-- BR
				BRITISH INDIAN OCEAN TERRITORY -- IO
				BRUNEI DARUSSALAM --
				BN
				BULGARIA -- BG
				BURKINA FASO -- BF
				BURUNDI -- BI
				CAMBODIA -- KH
				CAMEROON
				-- CM
				CANADA -- CA
				CAPE VERDE -- CV
				CAYMAN ISLANDS -- KY
				CENTRAL AFRICAN
				REPUBLIC -- CF
				CHAD -- TD
				CHILE -- CL
				CHINA -- CN
				CHRISTMAS ISLAND -- CX
				COCOS (KEELING) ISLANDS -- CC
				COLOMBIA -- CO
				COMOROS -- KM
				CONGO -- CG
				CONGO, THE DEMOCRATIC REPUBLIC OF THE -- CD
				COOK ISLANDS -- CK
				COSTA
				RICA -- CR
				CÔTE D'IVOIRE -- CI
				CROATIA -- HR
				CUBA -- CU
				CURAÇAO -- CW
				CYPRUS -- CY
				CZECH REPUBLIC -- CZ
				DENMARK -- DK
				DJIBOUTI -- DJ
				DOMINICA
				-- DM
				DOMINICAN REPUBLIC -- DO
				ECUADOR -- EC
				EGYPT -- EG
				EL SALVADOR --
				SV
				EQUATORIAL GUINEA -- GQ
				ERITREA -- ER
				ESTONIA -- EE
				ETHIOPIA -- ET
				FALKLAND ISLANDS (MALVINAS) -- FK
				FAROE
				ISLANDS -- FO
				FIJI -- FJ
				FINLAND -- FI
				FRANCE -- FR
				FRENCH GUIANA -- GF
				FRENCH POLYNESIA -- PF
				FRENCH SOUTHERN TERRITORIES -- TF
				GABON -- GA
				GAMBIA -- GM
				GEORGIA --
				GE
				GERMANY -- DE
				GHANA -- GH
				GIBRALTAR -- GI
				GREECE -- GR
				GREENLAND -- GL
				GRENADA -- GD
				GUADELOUPE -- GP
				GUAM -- GU
				GUATEMALA -- GT
				GUERNSEY -- GG
				GUINEA -- GN
				GUINEA-BISSAU -- GW
				GUYANA
				-- GY
				HAITI -- HT
				HEARD ISLAND
				AND MCDONALD ISLANDS -- HM
				HOLY SEE
				(VATICAN CITY STATE) -- VA
				HONDURAS -- HN
				HONG KONG -- HK
				HUNGARY -- HU
				ICELAND -- IS
				INDIA -- IN
				INDONESIA -- ID
				IRAN, ISLAMIC REPUBLIC OF --
				IR
				IRAQ -- IQ
				IRELAND --
				IE
				ISLE OF MAN -- IM
				ISRAEL -- IL
				ITALY -- IT
				JAMAICA -- JM
				JAPAN -- JP
				JERSEY -- JE
				JORDAN -- JO
				KAZAKHSTAN -- KZ
				KENYA -- KE
				KIRIBATI -- KI
				KOREA, DEMOCRATIC PEOPLE'S REPUBLIC OF --
				KP
				KOREA, REPUBLIC OF -- KR
				KUWAIT -- KW
				KYRGYZSTAN -- KG
				LAO PEOPLE'S
				DEMOCRATIC REPUBLIC -- LA
				LATVIA -- LV
				LEBANON -- LB
				LESOTHO -- LS
				LIBERIA -- LR
				LIBYA -- LY
				LIECHTENSTEIN -- LI
				LITHUANIA -- LT
				LUXEMBOURG -- LU
				MACAO -- MO
				MACEDONIA, THE FORMER YUGOSLAV REPUBLIC
				OF -- MK
				MADAGASCAR -- MG
				MALAWI -- MW
				MALAYSIA -- MY
				MALDIVES -- MV
				MALI -- ML
				MALTA -- MT
				MARSHALL ISLANDS -- MH
				MARTINIQUE -- MQ
				MAURITANIA -- MR
				MAURITIUS --
				MU
				MAYOTTE -- YT
				MEXICO -- MX
				MICRONESIA,
				FEDERATED STATES OF -- FM
				MOLDOVA, REPUBLIC OF -- MD
				MONACO -- MC
				MONGOLIA -- MN
				MONTENEGRO -- ME
				MONTSERRAT -- MS
				MOROCCO -- MA
				MOZAMBIQUE -- MZ
				MYANMAR -- MM
				NAMIBIA --
				NA
				NAURU -- NR
				NEPAL -- NP
				NETHERLANDS -- NL
				NEW CALEDONIA -- NC
				NEW
				ZEALAND -- NZ
				NICARAGUA -- NI
				NIGER -- NE
				NIGERIA -- NG
				NIUE -- NU
				NORFOLK ISLAND -- NF
				NORTHERN
				MARIANA ISLANDS -- MP
				NORWAY -- NO
				OMAN
				-- OM
				PAKISTAN -- PK
				PALAU -- PW
				PALESTINE, STATE OF -- PS
				PANAMA -- PA
				PAPUA NEW GUINEA -- PG
				PARAGUAY
				-- PY
				PERU -- PE
				PHILIPPINES -- PH
				PITCAIRN -- PN
				POLAND -- PL
				PORTUGAL
				-- PT
				PUERTO RICO -- PR
				QATAR -- QA
				RÃ‰UNION -- RE
				ROMANIA -- RO
				RUSSIAN
				FEDERATION -- RU
				RWANDA -- RW
				SAINT
				BARTHÃ‰LEMY -- BL
				SAINT HELENA,
				ASCENSION AND TRISTAN DA CUNHA -- SH
				SAINT KITTS AND NEVIS -- KN
				SAINT LUCIA -- LC
				SAINT MARTIN (FRENCH
				PART) -- MF
				SAINT PIERRE AND
				MIQUELON -- PM
				SAINT VINCENT AND THE
				GRENADINES -- VC
				SAMOA -- WS
				SAN
				MARINO -- SM
				SAO TOME AND PRINCIPE --
				ST
				SAUDI ARABIA -- SA
				SENEGAL --
				SN
				SERBIA -- RS
				SEYCHELLES -- SC
				SIERRA
				LEONE -- SL
				SINGAPORE -- SG
				SINT
				MAARTEN (DUTCH PART) -- SX
				SLOVAKIA --
				SK
				SLOVENIA -- SI
				SOLOMON ISLANDS
				-- SB
				SOMALIA -- SO
				SOUTH AFRICA -- ZA
				SOUTH GEORGIA AND THE SOUTH
				SANDWICH ISLANDS -- GS
				SOUTH SUDAN -- SS
				SPAIN -- ES
				SRI LANKA -- LK
				SUDAN -- SD
				SURINAME -- SR
				SVALBARD AND JAN
				MAYEN -- SJ
				SWAZILAND -- SZ
				SWEDEN -- SE
				SWITZERLAND -- CH
				SYRIAN ARAB
				REPUBLIC -- SY
				TAIWAN,
				PROVINCE OF CHINA -- TW
				TAJIKISTAN -- TJ
				TANZANIA, UNITED REPUBLIC OF
				-- TZ
				THAILAND -- TH
				TIMOR-LESTE -- TL
				TOGO -- TG
				TOKELAU -- TK
				TONGA --
				TO
				TRINIDAD AND TOBAGO -- TT
				TUNISIA
				-- TN
				TURKEY -- TR
				TURKMENISTAN --
				TM
				TURKS AND CAICOS ISLANDS -- TC
				TUVALU -- TV
				UGANDA -- UG
				UKRAINE --
				UA
				UNITED ARAB EMIRATES -- AE
				UNITED KINGDOM -- GB
				UNITED STATES -- US
				UNITED STATES MINOR OUTLYING
				ISLANDS -- UM
				URUGUAY -- UY
				UZBEKISTAN --
				UZ
				VANUATU -- VU
				VENEZUELA,
				BOLIVARIAN REPUBLIC OF -- VE
				VIET NAM -- VN
				VIRGIN ISLANDS, BRITISH --
				VG
				VIRGIN ISLANDS, U.S. -- VI
				WALLIS AND
				FUTUNA -- WF
				WESTERN SAHARA --
				EH
				YEMEN -- YE
				ZAMBIA -- ZM
				ZIMBABWE -- ZW</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[A-Z]{2,2}"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ISOCountryCodeOptional">
    <xsd:annotation>
      <xsd:documentation>International Standard for country codes. Empty
				value is allowed.
				The format is two upper case letters. for ex SA,
				US, CA etc
				Below is the list of all ISO country codes (Country Name
				-- Code)

				AFGHANISTAN -- AF
				Ã…LAND ISLANDS -- AX
				ALBANIA -- AL
				ALGERIA --
				DZ
				AMERICAN SAMOA -- AS
				ANDORRA -- AD
				ANGOLA -- AO
				ANGUILLA -- AI
				ANTARCTICA -- AQ
				ANTIGUA AND BARBUDA -- AG
				ARGENTINA -- AR
				ARMENIA --
				AM
				ARUBA -- AW
				AUSTRALIA -- AU
				AUSTRIA -- AT
				AZERBAIJAN -- AZ
				BAHAMAS --
				BS
				BAHRAIN -- BH
				BANGLADESH -- BD
				BARBADOS -- BB
				BELARUS -- BY
				BELGIUM --
				BE
				BELIZE -- BZ
				BENIN -- BJ
				BERMUDA -- BM
				BHUTAN -- BT
				BOLIVIA,
				PLURINATIONAL STATE OF -- BO
				BONAIRE, SINT EUSTATIUS AND SABA -- BQ
				BOSNIA AND HERZEGOVINA -- BA
				BOTSWANA -- BW
				BOUVET ISLAND -- BV
				BRAZIL
				-- BR
				BRITISH INDIAN OCEAN TERRITORY -- IO
				BRUNEI DARUSSALAM -- BN
				BULGARIA -- BG
				BURKINA FASO -- BF
				BURUNDI -- BI
				CAMBODIA -- KH
				CAMEROON
				-- CM
				CANADA -- CA
				CAPE VERDE -- CV
				CAYMAN ISLANDS -- KY
				CENTRAL AFRICAN
				REPUBLIC -- CF
				CHAD -- TD
				CHILE -- CL
				CHINA -- CN
				CHRISTMAS ISLAND -- CX
				COCOS (KEELING) ISLANDS -- CC
				COLOMBIA -- CO
				COMOROS -- KM
				CONGO -- CG
				CONGO, THE DEMOCRATIC REPUBLIC OF THE -- CD
				COOK ISLANDS -- CK
				COSTA
				RICA -- CR
				CÃ”TE D'IVOIRE -- CI
				CROATIA -- HR
				CUBA -- CU
				CURAÃ‡AO -- CW
				CYPRUS -- CY
				CZECH REPUBLIC -- CZ
				DENMARK -- DK
				DJIBOUTI -- DJ
				DOMINICA
				-- DM
				DOMINICAN REPUBLIC -- DO
				ECUADOR -- EC
				EGYPT -- EG
				EL SALVADOR --
				SV
				EQUATORIAL GUINEA -- GQ
				ERITREA -- ER
				ESTONIA -- EE
				ETHIOPIA -- ET
				FALKLAND ISLANDS (MALVINAS) -- FK
				FAROE ISLANDS -- FO
				FIJI -- FJ
				FINLAND -- FI
				FRANCE -- FR
				FRENCH GUIANA -- GF
				FRENCH POLYNESIA -- PF
				FRENCH SOUTHERN TERRITORIES -- TF
				GABON -- GA
				GAMBIA -- GM
				GEORGIA --
				GE
				GERMANY -- DE
				GHANA -- GH
				GIBRALTAR -- GI
				GREECE -- GR
				GREENLAND -- GL
				GRENADA -- GD
				GUADELOUPE -- GP
				GUAM -- GU
				GUATEMALA -- GT
				GUERNSEY -- GG
				GUINEA -- GN
				GUINEA-BISSAU -- GW
				GUYANA -- GY
				HAITI -- HT
				HEARD ISLAND
				AND MCDONALD ISLANDS -- HM
				HOLY SEE (VATICAN CITY STATE) -- VA
				HONDURAS -- HN
				HONG KONG -- HK
				HUNGARY -- HU
				ICELAND -- IS
				INDIA -- IN
				INDONESIA -- ID
				IRAN, ISLAMIC REPUBLIC OF -- IR
				IRAQ -- IQ
				IRELAND --
				IE
				ISLE OF MAN -- IM
				ISRAEL -- IL
				ITALY -- IT
				JAMAICA -- JM
				JAPAN -- JP
				JERSEY -- JE
				JORDAN -- JO
				KAZAKHSTAN -- KZ
				KENYA -- KE
				KIRIBATI -- KI
				KOREA, DEMOCRATIC PEOPLE'S REPUBLIC OF -- KP
				KOREA, REPUBLIC OF -- KR
				KUWAIT -- KW
				KYRGYZSTAN -- KG
				LAO PEOPLE'S DEMOCRATIC REPUBLIC -- LA
				LATVIA -- LV
				LEBANON -- LB
				LESOTHO -- LS
				LIBERIA -- LR
				LIBYA -- LY
				LIECHTENSTEIN -- LI
				LITHUANIA -- LT
				LUXEMBOURG -- LU
				MACAO -- MO
				MACEDONIA, THE FORMER YUGOSLAV REPUBLIC OF -- MK
				MADAGASCAR -- MG
				MALAWI -- MW
				MALAYSIA -- MY
				MALDIVES -- MV
				MALI -- ML
				MALTA -- MT
				MARSHALL ISLANDS -- MH
				MARTINIQUE -- MQ
				MAURITANIA -- MR
				MAURITIUS --
				MU
				MAYOTTE -- YT
				MEXICO -- MX
				MICRONESIA, FEDERATED STATES OF -- FM
				MOLDOVA, REPUBLIC OF -- MD
				MONACO -- MC
				MONGOLIA -- MN
				MONTENEGRO -- ME
				MONTSERRAT -- MS
				MOROCCO -- MA
				MOZAMBIQUE -- MZ
				MYANMAR -- MM
				NAMIBIA --
				NA
				NAURU -- NR
				NEPAL -- NP
				NETHERLANDS -- NL
				NEW CALEDONIA -- NC
				NEW
				ZEALAND -- NZ
				NICARAGUA -- NI
				NIGER -- NE
				NIGERIA -- NG
				NIUE -- NU
				NORFOLK ISLAND -- NF
				NORTHERN MARIANA ISLANDS -- MP
				NORWAY -- NO
				OMAN
				-- OM
				PAKISTAN -- PK
				PALAU -- PW
				PALESTINE, STATE OF -- PS
				PANAMA -- PA
				PAPUA NEW GUINEA -- PG
				PARAGUAY -- PY
				PERU -- PE
				PHILIPPINES -- PH
				PITCAIRN -- PN
				POLAND -- PL
				PORTUGAL -- PT
				PUERTO RICO -- PR
				QATAR -- QA
				RÉUNION -- RE
				ROMANIA -- RO
				RUSSIAN FEDERATION -- RU
				RWANDA -- RW
				SAINT
				BARTHÉLEMY -- BL
				SAINT HELENA, ASCENSION AND TRISTAN DA CUNHA -- SH
				SAINT KITTS AND NEVIS -- KN
				SAINT LUCIA -- LC
				SAINT MARTIN (FRENCH
				PART) -- MF
				SAINT PIERRE AND MIQUELON -- PM
				SAINT VINCENT AND THE
				GRENADINES -- VC
				SAMOA -- WS
				SAN MARINO -- SM
				SAO TOME AND PRINCIPE --
				ST
				SAUDI ARABIA -- SA
				SENEGAL -- SN
				SERBIA -- RS
				SEYCHELLES -- SC
				SIERRA
				LEONE -- SL
				SINGAPORE -- SG
				SINT MAARTEN (DUTCH PART) -- SX
				SLOVAKIA --
				SK
				SLOVENIA -- SI
				SOLOMON ISLANDS -- SB
				SOMALIA -- SO
				SOUTH AFRICA -- ZA
				SOUTH GEORGIA AND THE SOUTH SANDWICH ISLANDS -- GS
				SOUTH SUDAN -- SS
				SPAIN -- ES
				SRI LANKA -- LK
				SUDAN -- SD
				SURINAME -- SR
				SVALBARD AND JAN
				MAYEN -- SJ
				SWAZILAND -- SZ
				SWEDEN -- SE
				SWITZERLAND -- CH
				SYRIAN ARAB
				REPUBLIC -- SY
				TAIWAN, PROVINCE OF CHINA -- TW
				TAJIKISTAN -- TJ
				TANZANIA, UNITED REPUBLIC OF -- TZ
				THAILAND -- TH
				TIMOR-LESTE -- TL
				TOGO -- TG
				TOKELAU -- TK
				TONGA -- TO
				TRINIDAD AND TOBAGO -- TT
				TUNISIA
				-- TN
				TURKEY -- TR
				TURKMENISTAN -- TM
				TURKS AND CAICOS ISLANDS -- TC
				TUVALU -- TV
				UGANDA -- UG
				UKRAINE -- UA
				UNITED ARAB EMIRATES -- AE
				UNITED KINGDOM -- GB
				UNITED STATES -- US
				UNITED STATES MINOR OUTLYING
				ISLANDS -- UM
				URUGUAY -- UY
				UZBEKISTAN -- UZ
				VANUATU -- VU
				VENEZUELA,
				BOLIVARIAN REPUBLIC OF -- VE
				VIET NAM -- VN
				VIRGIN ISLANDS, BRITISH --
				VG
				VIRGIN ISLANDS, U.S. -- VI
				WALLIS AND FUTUNA -- WF
				WESTERN SAHARA --
				EH
				YEMEN -- YE
				ZAMBIA -- ZM
				ZIMBABWE -- ZW</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([A-Z]{2,2})|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MWISOCountryCode">
    <xsd:annotation>
      <xsd:documentation>Valid values : IDN, YEM, LKA, PHL, EGY etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[A-Z]{3,3}"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MWISOCountryCodeOptional">
    <xsd:annotation>
      <xsd:documentation>Empty value is allowed.
				Valid values : IDN, YEM,
				LKA, PHL, EGY etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([A-Z]{3,3})|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ISOCurrencyCode">
    <xsd:annotation>
      <xsd:documentation>The standard currency codes published by ISO.
				The
				format is three upper case letters.
				Valid values are : AED, SAR,
				USD,
				EUR etc.
				Below is the list of all ISO currency codes (Code - Country
				Name )

				AED United Arab Emirates Dirham
				AFN Afghanistan Afghani
				ALL
				Albania Lek
				AMD Armenia Dram
				ANG Netherlands Antilles Guilder
				AOA
				Angola Kwanza
				ARS Argentina Peso
				AUD Australia Dollar
				AWG Aruba Guilder
				AZN
				Azerbaijan New Manat
				BAM Bosnia and Herzegovina Convertible Marka
				BBD
				Barbados Dollar
				BDT Bangladesh Taka
				BGN Bulgaria Lev
				BHD Bahrain
				Dinar
				BIF Burundi Franc
				BMD Bermuda Dollar
				BND Brunei Darussalam Dollar
				BOB
				Bolivia Boliviano
				BRL Brazil Real
				BSD Bahamas Dollar
				BTN Bhutan
				Ngultrum
				BWP Botswana Pula
				BYR Belarus Ruble
				BZD Belize Dollar
				CAD
				Canada Dollar
				CDF Congo/Kinshasa Franc
				CHF Switzerland Franc
				CLP Chile
				Peso
				CNY China Yuan Renminbi
				COP Colombia Peso
				CRC Costa Rica Colon
				CUC
				Cuba Convertible Peso
				CUP Cuba Peso
				CVE Cape Verde Escudo
				CZK Czech
				Republic Koruna
				DJF Djibouti Franc
				DKK Denmark Krone
				DOP Dominican
				Republic Peso
				DZD Algeria Dinar
				EGP Egypt Pound
				ERN Eritrea Nakfa
				ETB
				Ethiopia Birr
				EUR Euro Member Countries
				FJD Fiji Dollar
				FKP Falkland
				Islands (Malvinas) Pound
				GBP United Kingdom Pound
				GEL Georgia Lari
				GGP
				Guernsey Pound
				GHS Ghana Cedi
				GIP Gibraltar Pound
				GMD Gambia Dalasi
				GNF
				Guinea Franc
				GTQ Guatemala Quetzal
				GYD Guyana Dollar
				HKD Hong Kong
				Dollar
				HNL Honduras Lempira
				HRK Croatia Kuna
				HTG Haiti Gourde
				HUF
				Hungary Forint
				IDR Indonesia Rupiah
				ILS Israel Shekel
				IMP Isle of Man
				Pound
				INR India Rupee
				IQD Iraq Dinar
				IRR Iran Rial
				ISK Iceland Krona
				JEP
				Jersey Pound
				JMD Jamaica Dollar
				JOD Jordan Dinar
				JPY Japan Yen
				KES Kenya
				Shilling
				KGS Kyrgyzstan Som
				KHR Cambodia Riel
				KMF Comoros Franc
				KPW
				Korea (North) Won
				KRW Korea (South) Won
				KWD Kuwait Dinar
				KYD Cayman
				Islands Dollar
				KZT Kazakhstan Tenge
				LAK Laos Kip
				LBP Lebanon Pound
				LKR
				Sri Lanka Rupee
				LRD Liberia Dollar
				LSL Lesotho Loti
				LTL Lithuania Litas
				LVL Latvia Lat
				LYD Libya Dinar
				MAD Morocco Dirham
				MDL Moldova Leu
				MGA
				Madagascar Ariary
				MKD Macedonia Denar
				MMK Myanmar (Burma) Kyat
				MNT
				Mongolia Tughrik
				MOP Macau Pataca
				MRO Mauritania Ouguiya
				MUR Mauritius
				Rupee
				MVR Maldives (Maldive Islands) Rufiyaa
				MWK Malawi Kwacha
				MXN
				Mexico Peso
				MYR Malaysia Ringgit
				MZN Mozambique Metical
				NAD Namibia
				Dollar
				NGN Nigeria Naira
				NIO Nicaragua Cordoba
				NOK Norway Krone
				NPR
				Nepal Rupee
				NZD New Zealand Dollar
				OMR Oman Rial
				PAB Panama Balboa
				PEN
				Peru Nuevo Sol
				PGK Papua New Guinea Kina
				PHP Philippines Peso
				PKR
				Pakistan Rupee
				PLN Poland Zloty
				PYG Paraguay Guarani
				QAR Qatar Riyal
				RON Romania New Leu
				RSD Serbia Dinar
				RUB Russia Ruble
				RWF Rwanda Franc
				SAR Saudi Arabia Riyal
				SBD Solomon Islands Dollar
				SCR Seychelles Rupee
				SDG Sudan Pound
				SEK Sweden Krona
				SGD Singapore Dollar
				SHP Saint Helena
				Pound
				SLL Sierra Leone Leone
				SOS Somalia Shilling
				SRD Suriname Dollar
				STD São Tomé and Príncipe Dobra
				SVC El Salvador Colon
				SYP Syria Pound
				SZL Swaziland Lilangeni
				THB Thailand Baht
				TJS Tajikistan Somoni
				TMT
				Turkmenistan Manat
				TND Tunisia Dinar
				TOP Tonga Pa'anga
				TRY Turkey Lira
				TTD Trinidad and Tobago Dollar
				TVD Tuvalu Dollar
				TWD Taiwan New Dollar
				TZS Tanzania Shilling
				UAH Ukraine Hryvna
				UGX Uganda Shilling
				USD United
				States Dollar
				UYU Uruguay Peso
				UZS Uzbekistan Som
				VEF Venezuela Bolivar
				VND Viet Nam Dong
				VUV Vanuatu Vatu
				WST Samoa Tala
				XAF Communauté
				Financière Africaine (BEAC) CFA Franc BEAC
				XCD East Caribbean Dollar
				XDR International Monetary Fund (IMF) Special Drawing Rights
				XOF
				Communauté Financière Africaine (BCEAO) Franc
				XPF Comptoirs Français
				du Pacifique (CFP) Franc
				YER Yemen Rial
				ZAR South Africa Rand
				ZMW
				Zambia Kwacha
				ZWD Zimbabwe Dollar</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[A-Z]{3,3}"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Amount">
    <xsd:annotation>
      <xsd:documentation>This element represents amount of money in the
				format xxxx.xxx or -xxxx.xxx.
				It can have maximum 15 decimal places
				and can have 18 total digits.
				Valid values are : 232334.454 ,
				65717171772372.5562 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:decimal">
      <!--<xsd:minInclusive value="0" />-->
      <xsd:fractionDigits value="15"/>
      <xsd:totalDigits value="18"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="AmountOptional">
    <xsd:union memberTypes="tns:Amount tns:emptyStringType"/>
  </xsd:simpleType>
  <xsd:complexType name="Money">
    <xsd:simpleContent>
      <xsd:extension base="tns:Amount">
        <xsd:attribute name="isoCurrencyCode" type="tns:ISOCurrencyCode" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="MoneyOptional">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="tns:Amount">
        <xsd:attribute name="isoCurrencyCode" type="tns:ISOCurrencyCodeOptional" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:simpleType name="LanguageCode">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string"/>
  </xsd:simpleType>
  <xsd:simpleType name="LanguageCodeOptional">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string"/>
  </xsd:simpleType>
  <xsd:simpleType name="BIC">
    <xsd:annotation>
      <xsd:documentation>BIC is a standard format of Business Identifier
				Codes approved by the
				ISO.
				It is a unique identification code for both
				financial and
				non-financial institutions.

				Structure :
				The BIC code is 8
				or 11 characters, made up of:
				4 letters: Institution
				Code or bank
				code.
				2 letters: ISO 3166-1 alpha-2 country code
				2
				letters or digits:
				location code
				if the second character is "0", then
				it is typically a
				test BIC as opposed to a BIC used on the live
				network.
				if the second
				character is "1", then it denotes a passive
				participant in the SWIFT
				network
				if the second character is "2", then
				it typically indicates a
				reverse billing BIC, where the recipient
				pays for the message
				as
				opposed to the more usual mode whereby the
				sender pays for the
				message.
				3 letters or digits: branch code,
				optional ('XXX' for primary
				office)
				Where an 8-digit code is given,
				it may be assumed that it
				refers to the primary office.

				Valid Values are : DEUTDEFF, NEDSZAJJ ,
				UNCRIT2B912, DABADKKK,
				UNCRIT2B912 etc

				For more info refer to
				wikipedia article at :
				http://en.wikipedia.org/wiki/ISO_9362</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="AccountId">
    <xsd:annotation>
      <xsd:documentation>Id of the accounts in NCB BaNCS system.
				Valid
				values are : **************, **************, ************** etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="24"/>
      <xsd:minLength value="14"/>
      <xsd:pattern value="([0-9]{14})|(SA[0-9]{4}[a-zA-Z0-9]{18})"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="AccountIdOptional">
    <xsd:annotation>
      <xsd:documentation>Id of the accounts in NCB BaNCS system.
				This is
				optional. Empty values are allowed.
				Valid values are :
				**************, **************, ************** etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]{14})|(SA[0-9]{4}[a-zA-Z0-9]{18})|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="SalaryAccountIdOptional">
    <xsd:annotation>
      <xsd:documentation>Id of the accounts in NCB BaNCS system.
				This is
				optional. Empty values are allowed.
				Valid values are :
				000**************, 000**************, 000************** etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]{17})|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="AccountNumberType">
    <xsd:annotation>
      <xsd:documentation>Id of the accounts in NCB BaNCS system.
				Empty
				values are not allowed.
				Valid values are :
				000**************,
				*****************, ************** etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="17"/>
      <xsd:pattern value="([0-9]{1,17})"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="InvestmentAccountId">
    <xsd:annotation>
      <xsd:documentation>Id of the investment accounts in NCB system.
				An
				integer with a maximum of 7 digits
				Valid values are : 5516500,
				3616098, 105186 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:int">
      <xsd:totalDigits value="7"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="CompanyId">
    <xsd:annotation>
      <xsd:documentation>Id of the customer company in NCB system.
				A string
				with a maximum length of 3.
				Valid values are : XAS, MSF, NCB etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="3"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PId">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="24"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PIdType">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="2"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="NIN">
    <xsd:annotation>
      <xsd:documentation>This element represents the national
				identification number.
				Valid Values are : 1016626036, 1018876126,
				1037297759 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="24"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="NINType">
    <xsd:annotation>
      <xsd:documentation>This element represents the type of national
				identification number.
				Valid Values are : 0001, 01 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="4"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ApplicationID">
    <xsd:annotation>
      <xsd:documentation>The id of the credit card application. A string of
				maximum 10 characters.
				Valid values are 2003062143001, 2013230130850,
				2013122111325 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="10"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PartyNumber">
    <xsd:annotation>
      <xsd:documentation>The identification number of customer party.
				An
				integer of maximum 14 digits.
				Valid values : 3892311, 32822212128,
				572161 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:totalDigits value="14"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="SubscriberCount">
    <xsd:annotation>
      <xsd:documentation>An integer of maximum 3 digits.
				Valid values : 0,
				2, 23, 187 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
      <xsd:totalDigits value="3"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="SharesPerSubscriber">
    <xsd:annotation>
      <xsd:documentation>An integer of maximum 15 digits.
				Valid values : 0,
				235235, ********, 1923 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
      <xsd:totalDigits value="15"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="SharesValue">
    <xsd:annotation>
      <xsd:documentation>An integer of maximum 13 digits.
				Valid values : 0,
				235235, ********, 1923 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
      <xsd:totalDigits value="13"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="TotalShares">
    <xsd:annotation>
      <xsd:documentation>An integer of maximum 15 digits.
				Valid values : 0,
				235235, ********, 1923 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
      <xsd:totalDigits value="15"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="BankCode">
    <xsd:annotation>
      <xsd:documentation>An integer of maximum 15 digits.
				Valid values : 0,
				122, 654 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
      <xsd:totalDigits value="3"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="SubscriberCategory">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="1"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PaymentMode">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="1"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="IPOType">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="1"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PhoneNumber">
    <xsd:annotation>
      <xsd:documentation>Full international telephone number is up to 15
				digits long.
				The country code is up to 3 digits long. The area code
				is
				country-dependent and is not used for some countries. If
				used, it
				seems to be from 1 to 5 digits long. This leaves
				the subscriber
				number up to 14 digits. Plus there is the
				optional extension. Putting
				this all together, we get:

				country-area-subscriber-extension

				The area
				code and extension are optional. Here are a few
				examples:

				1-800-1234567 (country-area-subscriber)
				1-800-1234567-89
				(country-area-subscriber-extension)
				34-912345678 (country-subscriber)
				34-912345678-9 (country-subscriber-extension)

				See also
				http://en.wikipedia.org/wiki/Area_code
				http://en.wikipedia.org/wiki/E.164</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <!--<xsd:pattern value="([1-9][0-9]{0,2})(-[1-9][0-9]{0,4})?(-[1-9][0-9]{0,13})(-[0-9]+)?" 
				/>-->
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PhoneNumberOptional">
    <xsd:annotation>
      <xsd:documentation>Full international telephone number is up to 15
				digits long.
				The country code is up to 3 digits long. The area code
				is
				country-dependent and is not used for some countries. If
				used, it
				seems to be from 1 to 5 digits long. This leaves
				the subscriber
				number up to 14 digits. Plus there is the
				optional extension. Putting
				this all together, we get:

				country-area-subscriber-extension

				The area
				code and extension are optional. Here are a few
				examples:

				1-800-1234567 (country-area-subscriber)
				1-800-1234567-89
				(country-area-subscriber-extension)
				34-912345678 (country-subscriber)
				34-912345678-9 (country-subscriber-extension)

				See also
				http://en.wikipedia.org/wiki/Area_code
				http://en.wikipedia.org/wiki/E.164</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <!--<xsd:pattern value="(([1-9][0-9]{0,2})(-[1-9][0-9]{0,4})?(-[1-9][0-9]{0,13})(-[0-9]+)?)|()" 
				/>-->
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MobileNumber">
    <xsd:annotation>
      <xsd:documentation>Mobile phone number.
				Valid Values are :
				966554325688 , 00966554325688, 554325688 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[0-9]{9,15}"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MobileNumberOptional">
    <xsd:annotation>
      <xsd:documentation>Mobile phone number.
				Valid Values are :
				966554325688 , 00966554325688, 554325688 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[0-9]{9,15}|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EmailAddress">
    <xsd:annotation>
      <xsd:documentation>Email address.
				Valid Values are :
				<EMAIL>, <EMAIL> etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([a-zA-Z0-9_\-])([a-zA-Z0-9_\-\.]*)@(\[((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}|((([a-zA-Z0-9\-]+)\.)+))([a-zA-Z]{2,}|(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\])"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="EmailAddressOptional">
    <xsd:annotation>
      <xsd:documentation>Optional Email address.
				Valid Values are :
				<EMAIL>, <EMAIL> etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([a-zA-Z0-9_\-])([a-zA-Z0-9_\-\.]*)@(\[((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}|((([a-zA-Z0-9\-]+)\.)+))([a-zA-Z]{2,}|(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\])|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:complexType name="PostalAddress">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="addressLine1" type="tns:Max70Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="addressLine2" type="tns:Max70Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="addressLine3" type="tns:Max70Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="city" type="tns:Max35Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="region" type="tns:Max35Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="postalCode" type="tns:Max16Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="country" type="tns:ISOCountryCodeOptional" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Cursor">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="position" maxOccurs="1" minOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:integer">
            <xsd:minInclusive value="0"/>
            <xsd:totalDigits value="4"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="noOfRows" maxOccurs="1" minOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:integer">
            <xsd:minInclusive value="0"/>
            <xsd:totalDigits value="4"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Biller">
    <xsd:sequence maxOccurs="unbounded" minOccurs="1">
      <xsd:element name="id" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:simpleType name="Max2Text">
    <xsd:annotation>
      <xsd:documentation>Any string of maximum length 2.</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="0"/>
      <xsd:maxLength value="2"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Max16Text">
    <xsd:annotation>
      <xsd:documentation>Any string of maximum length 16</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="0"/>
      <xsd:maxLength value="16"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Max20Text">
    <xsd:annotation>
      <xsd:documentation>Any string of maximum length 20</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="0"/>
      <xsd:maxLength value="20"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Max35Text">
    <xsd:annotation>
      <xsd:documentation>Any string of maximum length 35</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="0"/>
      <xsd:maxLength value="35"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Max30Text">
    <xsd:annotation>
      <xsd:documentation>Any string of maximum length 30</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="0"/>
      <xsd:maxLength value="30"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Max60Text">
    <xsd:annotation>
      <xsd:documentation>Any string of maximum length 60</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="0"/>
      <xsd:maxLength value="60"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Max5Text">
    <xsd:annotation>
      <xsd:documentation>Any string of maximum length 5</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="0"/>
      <xsd:maxLength value="5"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Max70Text">
    <xsd:annotation>
      <xsd:documentation>Any string of maximum length 70</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="0"/>
      <xsd:maxLength value="70"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Max80Text">
    <xsd:annotation>
      <xsd:documentation>Any string of maximum length 80</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="0"/>
      <xsd:maxLength value="80"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Max100Text">
    <xsd:annotation>
      <xsd:documentation>Any string of maximum length 100</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="0"/>
      <xsd:maxLength value="100"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Max40Text">
    <xsd:annotation>
      <xsd:documentation>Any string of maximum length 40</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="0"/>
      <xsd:maxLength value="40"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Max10Text">
    <xsd:annotation>
      <xsd:documentation>Any string of maximum length 10</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="0"/>
      <xsd:maxLength value="10"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Max50Text">
    <xsd:annotation>
      <xsd:documentation>Any string of maximum length 50</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="0"/>
      <xsd:maxLength value="50"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="TransactionProcessType">
    <xsd:restriction base="xsd:string">
      <maxLength value="3"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="TransactionRefSNo">
    <xsd:restriction base="xsd:string">
      <maxLength value="3"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="TransactionRefNo">
    <xsd:restriction base="xsd:string">
      <maxLength value="10"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:complexType name="DocumentType">
    <xsd:sequence>
      <xsd:element name="documentId" type="tns:DocumentId"/>
      <xsd:element name="documentSubId" type="tns:PageNumber"/>
      <xsd:element name="totalPages" type="tns:PageNumber"/>
      <xsd:element name="documentType" type="xsd:string"/>
      <xsd:element name="archivedDate" type="tns:Date"/>
      <xsd:element name="page" type="tns:PageType" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PageType">
    <xsd:sequence>
      <xsd:element name="pageNo" type="tns:PageNumber"/>
      <xsd:element name="pageExt" type="xsd:string"/>
      <xsd:element name="pageContent" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:simpleType name="ExchangeRate">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:decimal">
      <xsd:fractionDigits value="4"/>
      <xsd:totalDigits value="12"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="DocumentId">
    <xsd:restriction base="xsd:string">
      <!--maxLength value="7"></maxLength-->
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="LibraryId">
    <xsd:restriction base="xsd:string">
      <maxLength value="3"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="CreditCardId">
    <xsd:annotation>
      <xsd:documentation>Id of the credit card.
				A string of maximum length
				16, containing a combination of
				1) small case letters (a to z)
				2)
				upper case letters (A to Z)
				3) digits
				(0 to 9 )</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[a-zA-Z0-9]{16}"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="CreditCardIdOptional">
    <xsd:annotation>
      <xsd:documentation>Id of the credit card. Optional.
				A string of
				maximum length 16, containing a combination of
				1) small case letters
				(a to z)
				2) upper case letters (A to Z)
				3) digits
				(0 to 9 )</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[a-zA-Z0-9]{16}|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ShortCIF">
    <xsd:annotation>
      <xsd:documentation>First 8 characters of complete CIF (customer
				identification number)
				Valid Values are : 72415789, 70000036,
				70000045, 70000007 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]){8}|([0]){1}"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ShortCIFOptional">
    <xsd:annotation>
      <xsd:documentation>First 8 characters of complete CIF (customer
				identification number)
				Valid Values are : 72415789, 70000036,
				70000045, 70000007 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]){8}|([0]){1}|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="CIFOptional">
    <xsd:annotation>
      <xsd:documentation>Customer identification number
				Valid Values are :
				12948435765223, 11408939762911 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]){14}|([0]){1}|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="CIF">
    <xsd:annotation>
      <xsd:documentation>Customer identification number
				Valid Values are :
				12948435765223, 11408939762911 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]){14}|([0]){1}"/>
    </xsd:restriction>
  </xsd:simpleType>
  <!--ShortAndCIF consists either 8 or 14.-->
  <xsd:simpleType name="ShortAndCIF">
    <xsd:annotation>
      <xsd:documentation>First 8 characters of complete CIF (customer
				identification number)
				Valid Values are : 72415789, 70000036,
				70000045, 70000007 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]){8}|([0-9]){14}"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="JournalId">
    <xsd:annotation>
      <xsd:documentation>An integer with maximum of 9 digits</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <!--<xsd:minInclusive value="0" /> <xsd:totalDigits value="9" />-->
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="AuthorizationId">
    <xsd:annotation>
      <xsd:documentation>A string with maximum length of 6.</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="6"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PartyId">
    <xsd:annotation>
      <xsd:documentation>A string with maximum length of 15.</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="15"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="BillCycle">
    <xsd:annotation>
      <xsd:documentation>Valid Values : New, XXX, YYY</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="New"/>
      <xsd:enumeration value="XXX"/>
      <xsd:enumeration value="YYY"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PaymentType">
    <xsd:annotation>
      <xsd:documentation>Valid Values : POST, RCHG, RNEW</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <!--<xsd:enumeration value="POST"></xsd:enumeration>
			<xsd:enumeration value="RCHG"></xsd:enumeration>
			<xsd:enumeration value="RNEW"></xsd:enumeration>-->
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Flag">
    <xsd:annotation>
      <xsd:documentation>Valid Values : Y or N</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="Y"/>
      <xsd:enumeration value="N"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ServiceType">
    <xsd:annotation>
      <xsd:documentation>Valid Values :
				AUTL,CCRD,CIP,ENTR,GOVT,GSM,LLIN,LLHT,SMDI,UTIL,ISPS,GOFT,
				Private,MDIA,TRAN,EDUC,BKSV,LOAN,ELCT,PCIN,EXAM,WATR,CDUR,PHON,INSR,MED,TUIT</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <!--<xsd:enumeration value="AUTL" />
			<xsd:enumeration value="CCRD" />
			<xsd:enumeration value="CIP" />
			<xsd:enumeration value="ENTR" />
			<xsd:enumeration value="GOVT" />
			<xsd:enumeration value="GSM" />
			<xsd:enumeration value="LLIN" />
			<xsd:enumeration value="LLHT" />
			<xsd:enumeration value="SMDI" />
			<xsd:enumeration value="UTIL" />
			<xsd:enumeration value="ISPS" />
			<xsd:enumeration value="GOFT" />
			<xsd:enumeration value="Private" />
			<xsd:enumeration value="MDIA" />
			<xsd:enumeration value="TRAN" />
			<xsd:enumeration value="EDUC" />
			<xsd:enumeration value="BKSV" />
			<xsd:enumeration value="LOAN" />
			<xsd:enumeration value="ELCT" />
			<xsd:enumeration value="PCIN" />
			<xsd:enumeration value="EXAM" />
			<xsd:enumeration value="WATR" />
			<xsd:enumeration value="CDUR" />
			<xsd:enumeration value="PHON" />
			<xsd:enumeration value="INSR" />
			<xsd:enumeration value="MED" />
			<xsd:enumeration value="TUIT" />-->
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="SadadReferenceId">
    <xsd:annotation>
      <xsd:documentation>Sadad Reference Number
				Valid values are :
				WOA00000000002804</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string"/>
  </xsd:simpleType>
  <xsd:simpleType name="TokenReferenceId">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="10"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="OfferToTag">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="Yes"/>
      <xsd:enumeration value="No"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Version">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:decimal">
      <xsd:minInclusive value="0"/>
      <xsd:fractionDigits value="1"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Max2Digits">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:decimal">
      <xsd:minInclusive value="0"/>
      <xsd:maxInclusive value="2"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MaximumRecordCount">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PageStart">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PageEnd">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:complexType name="CustomerId">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence maxOccurs="1" minOccurs="1">
      <xsd:element name="id">
        <xsd:simpleType>
          <xsd:restriction base="xsd:integer">
            <xsd:minInclusive value="0"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="type" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CustomerId2">
    <xsd:sequence maxOccurs="1" minOccurs="1">
      <xsd:element name="id" type="xsd:string"/>
      <xsd:element name="type" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:simpleType name="BillerId">
    <xsd:annotation>
      <xsd:documentation>Id of the Biller.
				Valid Values are : 2112, 8362,
				232, 676 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:complexType name="Parameter">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence maxOccurs="1" minOccurs="1">
      <xsd:element name="name" type="xsd:string"/>
      <xsd:element name="value" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:simpleType name="MerchantId">
    <xsd:annotation>
      <xsd:documentation>Id of Merchant
				Valid Values are : SEMA, MBLI</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="4"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Merchant">
    <xsd:annotation>
      <xsd:documentation>A string of maximum length 25</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="25"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="TransactionType">
    <xsd:annotation>
      <xsd:documentation>Valid values are BIANNUALCYCLE, TODAY,
				CYCLETODATE, CTD etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="BIANNUALCYCLE"/>
      <xsd:enumeration value="TODAY"/>
      <xsd:enumeration value="CYCLETODATE"/>
      <xsd:enumeration value="CTD"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="DecimalPattern_1">
    <xsd:annotation>
      <xsd:documentation>Valid values are : 2312412.23 , 8371247.22 , 24312
				etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:decimal">
      <xsd:minInclusive value="0"/>
      <xsd:fractionDigits value="2"/>
      <xsd:totalDigits value="11"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="DecimalPattern_10_2">
    <xsd:annotation>
      <xsd:documentation>Valid values are : 2312412.23 , 8371247.22 ,
				24312, etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:decimal">
      <xsd:minInclusive value="0"/>
      <xsd:fractionDigits value="2"/>
      <xsd:totalDigits value="10"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="TransactionId">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="TransactionDesc">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="22"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="RecordId">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
      <xsd:totalDigits value="9"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PageNumber">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
      <xsd:totalDigits value="2"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PageSize">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
      <xsd:totalDigits value="4"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ServiceCode">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="10"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="CorrelationId">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1"/>
      <xsd:maxLength value="20"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="FeeReferenceId">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:complexType name="BeneficiaryId">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence maxOccurs="1" minOccurs="1">
      <xsd:element name="id" default="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:integer">
            <xsd:minInclusive value="0"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="type" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:simpleType name="StatusCode">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="3"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="StatusDesc">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="30"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Active">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="1"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="NumberOfTransactions">
    <xsd:annotation>
      <xsd:documentation>Valid values are : 0, 231, 121, 22, 4 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
      <xsd:totalDigits value="3"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Location">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="25"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="HolderName">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="102"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="RemainingLimitCount">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
      <!--<xsd:fractionDigits value="0" />-->
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="TransactionStatus">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string"/>
  </xsd:simpleType>
  <xsd:simpleType name="Branch">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string"/>
  </xsd:simpleType>
  <xsd:simpleType name="AccountType">
    <xsd:annotation>
      <xsd:documentation>Represents the type of the account.
				Valid values :
				0200, 0100, 0203 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="4"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="AccountIntCatType">
    <xsd:annotation>
      <xsd:documentation>Represents the type of the account.
				Valid values :
				0200, 0100, 0203 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="4"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="accountStatus">
    <xsd:annotation>
      <xsd:documentation>A code representing the status of the account.
				Valid values : 00 , 01 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="4"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="accountStatusDesc">
    <xsd:annotation>
      <xsd:documentation>A description of the status code of the account.
				Valid values : Active , Inactive etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="60"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="accountSubStatus">
    <xsd:annotation>
      <xsd:documentation>A code representing the sub status of the account.
				Valid values : 00 , 01 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="2"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="accountSubStatusDesc">
    <xsd:annotation>
      <xsd:documentation>A description of the sub status code of the
				account.
				Valid values : Active , Inactive etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="60"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Boolean">
    <xsd:annotation>
      <xsd:documentation>Valid values : true, false</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:boolean">
      <xsd:pattern value="true"/>
      <xsd:pattern value="false"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="OTPUserId">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="OTPUserName">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="OTPUserGroup">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MachineNonce">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="SequenceNonce">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MachineLabel">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string"/>
  </xsd:simpleType>
  <xsd:complexType name="DeviceDetails">
    <xsd:sequence>
      <xsd:element name="machineNonce" type="tns:MachineNonce"/>
      <xsd:element name="sequenceNonce" type="tns:SequenceNonce"/>
      <xsd:element name="machineLabel" type="tns:MachineLabel"/>
      <xsd:element name="machineProperties" type="tns:MachineProperties"/>
      <element name="createDate" type="tns:Date"/>
      <element name="lastUsedDate" type="tns:Date"/>
      <element name="expiryDate" type="tns:Date"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AuthenticationDeviceDetails">
    <xsd:sequence>
      <xsd:element name="machineNonce" type="tns:MachineNonce"/>
      <xsd:element name="sequenceNonce" type="tns:SequenceNonce"/>
      <xsd:element name="machineLabel" type="tns:MachineLabel"/>
      <xsd:element name="machineProperties" type="tns:MachineProperties"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="MachineProperties">
    <xsd:sequence maxOccurs="1" minOccurs="1">
      <xsd:element name="applicationData" type="tns:ApplicationData" maxOccurs="unbounded" minOccurs="3"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ApplicationData">
    <xsd:sequence>
      <xsd:element name="name">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:minLength value="1"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="value">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:minLength value="1"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:simpleType name="HardToken">
    <xsd:annotation>
      <xsd:documentation>Valid values : true, false</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:boolean"/>
  </xsd:simpleType>
  <xsd:simpleType name="SoftToken">
    <xsd:annotation>
      <xsd:documentation>Valid values : true, false</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:boolean"/>
  </xsd:simpleType>
  <xsd:simpleType name="SiteImage">
    <xsd:annotation>
      <xsd:documentation>Valid values : true, false</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:boolean"/>
  </xsd:simpleType>
  <xsd:simpleType name="TrustedPC">
    <xsd:annotation>
      <xsd:documentation>Valid values : true, false</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:boolean"/>
  </xsd:simpleType>
  <xsd:simpleType name="CallerId">
    <xsd:annotation>
      <xsd:documentation>Valid values : true, false</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:boolean"/>
  </xsd:simpleType>
  <xsd:simpleType name="OTP">
    <xsd:annotation>
      <xsd:documentation>Valid values : true, false</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:boolean"/>
  </xsd:simpleType>
  <xsd:simpleType name="InNumber">
    <xsd:annotation>
      <xsd:documentation>Valid values : true, false</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:boolean"/>
  </xsd:simpleType>
  <xsd:complexType name="AuthenticationInfo">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="methods" type="tns:Methods"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Methods">
    <xsd:sequence>
      <xsd:element name="hardToken" type="tns:HardToken"/>
      <xsd:element name="softToken" type="tns:SoftToken"/>
      <xsd:element name="siteImage" type="tns:SiteImage"/>
      <xsd:element name="trustedPC" type="tns:TrustedPC"/>
      <xsd:element name="callerId" type="tns:CallerId"/>
      <xsd:element name="oneTimePass" type="tns:OTP"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TokenDetails">
    <xsd:sequence>
      <xsd:element name="userId" type="tns:OTPUserId"/>
      <xsd:element name="userName" type="tns:OTPUserName"/>
      <xsd:element name="userGroup" type="tns:OTPUserGroup"/>
      <xsd:element name="vendorId" type="xsd:string"/>
      <xsd:element name="serialNumber" type="xsd:string"/>
      <xsd:element name="tokenType" type="xsd:string"/>
      <xsd:element name="signatureSupported" type="tns:Boolean"/>
      <xsd:element name="loadDate" type="tns:Date"/>
      <xsd:element name="lastUsedDate" type="tns:Date"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:simpleType name="ApplicationId">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Logo">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
      <!--<xsd:fractionDigits value="0" />-->
      <xsd:totalDigits value="3"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="CustomerIdentificationId">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PhoneArea">
    <xsd:annotation>
      <xsd:documentation>Area code of the Phone.
				Valid values : 800, 566 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="BillingCyclePeriod">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
      <xsd:totalDigits value="2"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="OrganizationId">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
      <xsd:totalDigits value="2"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="BinId">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
      <xsd:totalDigits value="3"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="TermsAndConditionsReferenceId">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="0"/>
      <xsd:totalDigits value="2"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="GlClassificationCode">
    <xsd:annotation>
      <xsd:documentation>A string of maximum length 40
				Valid values :
				9060**************** etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="40"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="Fund">
    <xsd:annotation>
      <xsd:documentation>A string identifier of the fund.
				Valid Values : 44,
				50 , 51 etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="4"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:complexType name="AccountDetails">
    <xsd:sequence>
      <xsd:element name="accountId" type="tns:AccountId"/>
      <xsd:element name="balance" type="tns:Money"/>
      <xsd:element name="overdraft" type="tns:Money"/>
      <xsd:element name="balanceSAR" type="tns:Money"/>
      <xsd:element name="availableSAR" type="tns:Money"/>
      <xsd:element name="averageLastSixMonths" type="tns:Money"/>
      <xsd:element name="isoCurrencyCode" type="tns:ISOCurrencyCode"/>
      <xsd:element name="division" type="xsd:string"/>
      <xsd:element name="statusCode" type="tns:StatusCode"/>
      <xsd:element name="accountType" type="tns:AccountType"/>
      <xsd:element name="glClassificationCode" type="tns:GlClassificationCode"/>
    </xsd:sequence>
  </xsd:complexType>
  <simpleType name="FamilyName">
    <xsd:annotation>
      <xsd:documentation>A string of maximum length 25</xsd:documentation>
    </xsd:annotation>
    <restriction base="string">
      <maxLength value="25"/>
      <minLength value="0"/>
    </restriction>
  </simpleType>
  <simpleType name="FirstName">
    <xsd:annotation>
      <xsd:documentation>A string of maximum length 25</xsd:documentation>
    </xsd:annotation>
    <restriction base="string">
      <maxLength value="25"/>
      <minLength value="0"/>
    </restriction>
  </simpleType>
  <simpleType name="MiddleName">
    <xsd:annotation>
      <xsd:documentation>A string of maximum length 25</xsd:documentation>
    </xsd:annotation>
    <restriction base="string">
      <maxLength value="25"/>
      <minLength value="0"/>
    </restriction>
  </simpleType>
  <simpleType name="LastName">
    <xsd:annotation>
      <xsd:documentation>A string of maximum length 25</xsd:documentation>
    </xsd:annotation>
    <restriction base="string">
      <maxLength value="25"/>
      <minLength value="0"/>
    </restriction>
  </simpleType>
  <simpleType name="FullName">
    <xsd:annotation>
      <xsd:documentation>A string of maximum length 75</xsd:documentation>
    </xsd:annotation>
    <restriction base="string">
      <maxLength value="75"/>
      <minLength value="0"/>
    </restriction>
  </simpleType>
  <simpleType name="SegmentCd">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <restriction base="string">
      <maxLength value="4"/>
    </restriction>
  </simpleType>
  <simpleType name="Gender">
    <xsd:annotation>
      <xsd:documentation>Valid values are : M , F etc</xsd:documentation>
    </xsd:annotation>
    <restriction base="string">
      <length value="1"/>
      <enumeration value="M"/>
      <enumeration value="F"/>
    </restriction>
  </simpleType>
  <xsd:simpleType name="ChequeProperties">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="11"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ChequeBookTypeCode">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="4"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="AutoReorderFlag">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="1"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="NoOfBooks">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:integer">
      <xsd:minInclusive value="1"/>
      <xsd:maxInclusive value="99"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="trueOrFalseType">
    <xsd:annotation>
      <xsd:documentation>Valid values are : true, false, True, False</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="true"/>
      <xsd:enumeration value="false"/>
      <xsd:enumeration value="True"/>
      <xsd:enumeration value="False"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="trueOrFalseTypeOptional">
    <xsd:annotation>
      <xsd:documentation>Optional.
				Valid values are : true, false, True,
				False etc</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="true"/>
      <xsd:enumeration value="false"/>
      <xsd:enumeration value="True"/>
      <xsd:enumeration value="False"/>
      <xsd:enumeration value=""/>
    </xsd:restriction>
  </xsd:simpleType>
  <simpleType name="YorNType">
    <xsd:annotation>
      <xsd:documentation>Valid values are : Y, N</xsd:documentation>
    </xsd:annotation>
    <restriction base="string">
      <enumeration value="Y"/>
      <enumeration value="N"/>
    </restriction>
  </simpleType>
  <simpleType name="YorNTypeOptional">
    <xsd:annotation>
      <xsd:documentation>Optional.
				Valid values are : Y, N</xsd:documentation>
    </xsd:annotation>
    <restriction base="string">
      <enumeration value="Y"/>
      <enumeration value="N"/>
      <enumeration value=""/>
    </restriction>
  </simpleType>
  <simpleType name="contactlessIndicatorTypeOptional">
    <xsd:annotation>
      <xsd:documentation>Optional.
				Valid values are : Y, N, V</xsd:documentation>
    </xsd:annotation>
    <restriction base="string">
      <enumeration value="Y"/>
      <enumeration value="N"/>
      <enumeration value="V"/>
      <enumeration value=""/>
    </restriction>
  </simpleType>
  <xsd:simpleType name="InterestRate">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:restriction base="xsd:decimal">
      <xsd:fractionDigits value="4"/>
      <xsd:totalDigits value="7"/>
    </xsd:restriction>
  </xsd:simpleType>
  <!--Added by Sreekanth for Redmine ticket (http://83.61.23.146/redmine/issues/4341) 
		from common.xsd-->
  <simpleType name="CustomerChannelType">
    <restriction base="string">
      <enumeration value="BOTH"/>
      <enumeration value="MOBILE"/>
      <enumeration value="AOL"/>
    </restriction>
  </simpleType>
  <simpleType name="ErrorCategoryType">
    <restriction base="string">
      <enumeration value="BUSINESS"/>
      <enumeration value="SYSTEM"/>
    </restriction>
  </simpleType>
  <complexType name="CommonServiceFaultType">
    <sequence>
      <element name="category" type="tns:ErrorCategoryType"/>
      <element name="code" type="string"/>
      <element name="message" type="string"/>
      <element name="detail" type="string"/>
    </sequence>
  </complexType>
  <!--Added by Sreekanth for Redmine ticket (http://83.61.23.146/redmine/issues/4341) 
		from user.xsd-->
  <simpleType name="ServicesStatusType">
    <restriction base="string">
      <enumeration value="ACTIVE"/>
      <enumeration value="DISABLE_USER"/>
      <enumeration value="DISABLE_ADMIN"/>
      <enumeration value="DELETED"/>
    </restriction>
  </simpleType>
  <simpleType name="ServiceLevelType">
    <restriction base="string">
      <enumeration value="INQUIRY"/>
      <enumeration value="FULL"/>
    </restriction>
  </simpleType>
  <complexType name="CustomerStatusType">
    <sequence>
      <element name="userId" type="string"/>
      <element name="aol" type="tns:AolStatusType"/>
      <element name="mobile" type="tns:MobileStatusType"/>
      <element name="emergencyCash" type="tns:EmergencyCashStatus"/>
    </sequence>
  </complexType>
  <complexType name="AolStatusType">
    <sequence>
      <element name="createdBy" type="string"/>
      <element name="createdDate" type="tns:DateTimeOptional"/>
      <element name="lastLoginDate" type="tns:DateTimeOptional"/>
      <element name="serviceLevel" type="tns:ServiceLevelType"/>
      <element name="serviceStatus" type="tns:ServicesStatusType"/>
      <element name="passwordRevoked" type="boolean"/>
    </sequence>
  </complexType>
  <complexType name="MobileStatusType">
    <sequence>
      <element name="registeredBy" type="string"/>
      <element name="registeredDate" type="tns:DateTimeOptional"/>
      <element name="lastLoginDate" type="tns:DateTimeOptional"/>
      <element name="serviceLevel" type="tns:ServiceLevelType"/>
      <element name="serviceStatus" type="tns:ServicesStatusType"/>
    </sequence>
  </complexType>
  <complexType name="EmergencyCashStatus">
    <sequence>
      <element name="registrationDate" type="tns:DateTimeOptional"/>
      <element name="status" type="string"/>
    </sequence>
  </complexType>
  <xsd:simpleType name="ATMCardNumber">
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="19"/>
      <xsd:minLength value="19"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="VoucherNumber">
    <xsd:restriction base="xsd:int">
      <xsd:minInclusive value="0"/>
      <xsd:fractionDigits value="0"/>
      <xsd:totalDigits value="10"/>
    </xsd:restriction>
  </xsd:simpleType>
  <complexType name="TransactionInfo">
    <sequence>
      <element name="transactionId" type="string" maxOccurs="1" minOccurs="0"/>
      <element name="relatedTransactionId" type="string" maxOccurs="1" minOccurs="0"/>
      <element name="transactionType" type="string" maxOccurs="1" minOccurs="1"/>
      <element name="timestamp" type="string" maxOccurs="1" minOccurs="0"/>
    </sequence>
  </complexType>
  <complexType name="ClientInfo">
    <sequence>
      <element name="clientId" type="string" maxOccurs="1" minOccurs="1"/>
    </sequence>
  </complexType>
  <complexType name="SecurityInfo">
    <sequence>
      <element name="username" type="string" maxOccurs="1" minOccurs="1"/>
      <element name="password" type="string" maxOccurs="1" minOccurs="1"/>
    </sequence>
  </complexType>
  <complexType name="CreationInfo">
    <sequence>
      <element name="date" maxOccurs="1" minOccurs="0">
        <simpleType>
          <restriction base="string">
            <length value="8"/>
          </restriction>
        </simpleType>
      </element>
      <element name="branch" maxOccurs="1" minOccurs="0">
        <simpleType>
          <restriction base="string">
            <maxLength value="5"/>
          </restriction>
        </simpleType>
      </element>
      <element name="institution" maxOccurs="1" minOccurs="0">
        <simpleType>
          <restriction base="string">
            <maxLength value="3"/>
          </restriction>
        </simpleType>
      </element>
      <element name="teller" maxOccurs="1" minOccurs="0">
        <simpleType>
          <restriction base="string">
            <maxLength value="8"/>
          </restriction>
        </simpleType>
      </element>
    </sequence>
  </complexType>
  <complexType name="RequestHeader">
    <sequence>
      <element name="transactionInfo" type="tns:TransactionInfo" maxOccurs="1" minOccurs="0"/>
      <element name="clientInfo" type="tns:ClientInfo" maxOccurs="1" minOccurs="0"/>
      <element name="securityInfo" type="tns:SecurityInfo" maxOccurs="1" minOccurs="0"/>
      <element name="creationInfo" type="tns:CreationInfo" maxOccurs="1" minOccurs="0"/>
    </sequence>
  </complexType>
  <complexType name="ErrorInfo">
    <sequence>
      <element name="level" maxOccurs="1" minOccurs="1">
        <simpleType>
          <restriction base="string">
            <enumeration value="SUCCESS"/>
            <enumeration value="WARNING"/>
            <enumeration value="FAILURE"/>
          </restriction>
        </simpleType>
      </element>
      <element name="code" type="string" maxOccurs="1" minOccurs="0"/>
      <element name="context" type="string" maxOccurs="1" minOccurs="0"/>
      <element name="message" type="string" maxOccurs="1" minOccurs="0"/>
      <element name="details" type="string" maxOccurs="1" minOccurs="0"/>
    </sequence>
  </complexType>
  <complexType name="ReplyHeader">
    <sequence>
      <element name="transactionInfo" type="tns:TransactionInfo" maxOccurs="1" minOccurs="0"/>
      <element name="clientInfo" type="tns:ClientInfo" maxOccurs="1" minOccurs="0"/>
      <element name="errorInfo" type="tns:ErrorInfo" maxOccurs="1" minOccurs="0"/>
    </sequence>
  </complexType>
  <xsd:simpleType name="emptyStringType">
    <xsd:restriction base="xsd:string">
      <xsd:length value="0"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="YearOptional">
    <xsd:annotation>
      <xsd:documentation>Year in YYYY format
				eg:-2018</xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]{4})|()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="AccountNumber">
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]{14})|(SA[0-9]{4}[a-zA-Z0-9]{18})"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="AppID">
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="10"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="CustomerNumber">
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="20"/>
    </xsd:restriction>
  </xsd:simpleType>
</schema>