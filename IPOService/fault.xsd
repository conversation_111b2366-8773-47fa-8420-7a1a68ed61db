<?xml version="1.0" encoding="UTF-8"?>
<schema targetNamespace="http://corp.alahli.com/middlewareservices/fault/1.0/" elementFormDefault="qualified" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://corp.alahli.com/middlewareservices/fault/1.0/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:element name="fault" type="tns:FaultType"/>
  <xsd:complexType name="FaultType">
    <xsd:sequence>
      <xsd:element name="type" type="string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="number" type="int"/>
      <xsd:element name="description" type="string"/>
      <xsd:element name="retryAfter" type="string" minOccurs="0"/>
      <xsd:element name="system" type="string" minOccurs="0"/>
      <xsd:element name="nativeError" type="string" minOccurs="0"/>
      <xsd:element name="nativeDescription" type="string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
</schema>