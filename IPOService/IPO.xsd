<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema targetNamespace="http://corp.alahli.com/middlewareservices/ipo/1.0/" elementFormDefault="qualified" xmlns:tns="http://corp.alahli.com/middlewareservices/ipo/1.0/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:fault="http://corp.alahli.com/middlewareservices/fault/1.0/" xmlns:Q1="http://corp.alahli.com/schemas/middleware/temporaltypes/2012/10/29/">
  <xsd:import namespace="http://corp.alahli.com/middlewareservices/fault/1.0/" schemaLocation="fault.xsd"/>
  <xsd:import namespace="http://corp.alahli.com/schemas/middleware/temporaltypes/2012/10/29/" schemaLocation="schemas.xsd"/>
  <xsd:element name="GetIPOCompanyRequest">
    <xsd:complexType>
      <xsd:all maxOccurs="1" minOccurs="0">
        <xsd:element name="type" maxOccurs="1" minOccurs="1">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="R"/>
              <xsd:enumeration value="S"/>
              <xsd:enumeration value="I"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="companyId" type="Q1:CompanyId" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="option" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="P"/>
              <xsd:enumeration value="PP"/>
              <xsd:enumeration value="A"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="atmReceipt" type="xsd:int" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="atmLocation" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="languageCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="case" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetIPOCompanyResponse">
    <xsd:complexType>
      <xsd:all maxOccurs="1" minOccurs="0">
        <xsd:element name="companyInfo" type="tns:IPOCompanyResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetIPOSellingCompaniesRequest">
    <xsd:complexType>
      <xsd:all maxOccurs="1" minOccurs="0">
        <xsd:element name="shortCIF" maxOccurs="1" minOccurs="1" type="Q1:ShortCIF"/>
        <xsd:element name="accountId" type="Q1:AccountId" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetIPOSellingCompaniesResponse">
    <xsd:complexType>
      <xsd:all maxOccurs="1" minOccurs="0">
        <xsd:element name="ipoSellingCompany" type="tns:IPOSellingCompanyType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="IPOCreateRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accId" type="xsd:int"/>
        <xsd:element name="noOfSubscriber" type="xsd:int"/>
        <xsd:element name="noOfShares" type="xsd:int"/>
        <xsd:element name="companyId" type="xsd:string"/>
        <xsd:element name="atmBranch" type="xsd:string"/>
        <xsd:element name="sharesReqEligible" type="xsd:string"/>
        <xsd:element name="sharesReqAdditional" type="xsd:string"/>
        <xsd:element name="biddingPrice" type="xsd:string"/>
        <xsd:element name="atmReceipt" type="xsd:string"/>
        <xsd:element name="atmLoc" type="xsd:string"/>
        <xsd:element name="mobileNumber" type="xsd:string"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="IPOCreateResponse">
    <xsd:complexType>
      <xsd:attribute name="journal" type="xsd:string"/>
      <xsd:attribute name="serial" type="xsd:string"/>
      <xsd:attribute name="balance" type="xsd:string"/>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InquireIPORequest">
    <xsd:complexType>
      <xsd:all maxOccurs="1" minOccurs="0">
        <xsd:element name="cif" type="xsd:string" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="companyId" type="Q1:CompanyId" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="applicationNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="identificationNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="type" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="01"/>
              <xsd:enumeration value="02"/>
              <xsd:enumeration value="03"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="shareHolder" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="ipoType" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="C"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="id" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InquireIPOResponse">
    <xsd:complexType>
      <xsd:all maxOccurs="1" minOccurs="0">
        <xsd:element name="New" type="tns:IPOInquiryResponseNewType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="Register" type="tns:IPOInquiryResponseRegisterType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="NoRegister" type="tns:IPOInquiryResponseNoRegisterType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="IPOCompanyInfoType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="amountShare" default="0.0" type="Q1:Amount"/>
      <xsd:element name="biddingFlag" type="Q1:YorNTypeOptional"/>
      <xsd:element name="biddingPrice1" default="0.0" type="Q1:Amount"/>
      <xsd:element name="biddingPrice2" default="0.0" type="Q1:Amount"/>
      <xsd:element name="biddingPrice3" default="0.0" type="Q1:Amount"/>
      <xsd:element name="biddingPrice4" default="0.0" type="Q1:Amount"/>
      <xsd:element name="biddingPrice5" default="0.0" type="Q1:Amount"/>
      <xsd:element name="biddingPrice6" default="0.0" type="Q1:Amount"/>
      <xsd:element name="biddingPrice7" default="0.0" type="Q1:Amount"/>
      <xsd:element name="biddingPrice8" default="0.0" type="Q1:Amount"/>
      <xsd:element name="biddingPrice9" default="0.0" type="Q1:Amount"/>
      <xsd:element name="companyId" type="xsd:string"/>
      <xsd:element name="endDate" type="Q1:MWDateOptional"/>
      <xsd:element name="endDateTime" type="xsd:string"/>
      <xsd:element name="maximumBidding" default="0" type="xsd:string"/>
      <xsd:element name="maximumShareNumber" default="0" type="xsd:string"/>
      <xsd:element name="maximumSubNumber" default="0" type="xsd:string"/>
      <xsd:element name="minimumShareNumber" default="0" type="xsd:string"/>
      <xsd:element name="multiShare" default="0" type="xsd:string"/>
      <xsd:element name="name" type="xsd:string"/>
      <xsd:element name="offeringType" type="xsd:string"/>
      <xsd:element name="refreshDate" type="xsd:string"/>
      <xsd:element name="refundFlag" type="Q1:YorNTypeOptional"/>
      <xsd:element name="shortName" type="xsd:string"/>
      <xsd:element name="startDate" type="Q1:MWDateOptional"/>
      <xsd:element name="startDateTime" type="xsd:string"/>
      <xsd:element name="status" default="0" type="xsd:int"/>
      <xsd:element name="symbolCode" type="xsd:string"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="IPOInquiryResponseNewType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="NIN" type="Q1:NIN"/>
      <xsd:element name="NINType" type="Q1:NINType"/>
      <xsd:element name="CIF" type="Q1:CIFOptional"/>
      <xsd:element name="Names" type="xsd:string"/>
      <xsd:element name="FirstName" type="Q1:Max80Text"/>
      <xsd:element name="FamilyName" type="Q1:Max80Text"/>
      <xsd:element name="FatherName" type="Q1:Max70Text"/>
      <xsd:element name="GrandFatherName" type="Q1:Max70Text"/>
      <xsd:element name="DateBirth" type="Q1:MWDateOptional"/>
      <xsd:element name="PoBox" default="0" type="xsd:int"/>
      <xsd:element name="Address1" type="Q1:Max70Text"/>
      <xsd:element name="Address2" type="Q1:Max70Text"/>
      <xsd:element name="City" type="xsd:string"/>
      <xsd:element name="Country" type="xsd:string"/>
      <xsd:element name="ZipCode" type="xsd:int"/>
      <xsd:element name="CitizenShip" type="xsd:string"/>
      <xsd:element name="Phone" type="Q1:PhoneNumberOptional"/>
      <xsd:element name="Gender" type="Q1:Gender"/>
      <xsd:element name="NoOfSubscriber" type="xsd:int"/>
      <xsd:element name="SubsCat" type="xsd:string"/>
      <xsd:element name="BankCode" type="Q1:BankCode"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="recTypes">
    <xsd:attribute name="shareCode" type="xsd:string"/>
    <xsd:attribute name="symbolDesc" type="xsd:string"/>
    <xsd:attribute name="qty" type="xsd:string"/>
    <xsd:attribute name="currentValue" type="xsd:string"/>
  </xsd:complexType>
  <xsd:complexType name="IPOInquiryResponseRegisterType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="NIN" type="Q1:NIN" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="NINType" type="Q1:NINType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ApplNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="CIF" type="Q1:CIFOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BanckAcc" type="Q1:AccountId" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BankCode" type="Q1:BankCode" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="NoOfSubscriber" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="NoOfShares" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="TotalShares" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="TotalValue" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BranchCode" type="Q1:Branch" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="DatePaid" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Name" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="FullDetails" type="tns:IPOInquiryResponseFullDetailsType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Dependent" type="tns:DependentType1" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="IPOInquiryResponseNoRegisterType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="IPOType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ClientType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="NIN" type="Q1:NIN" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="NINType" type="Q1:NINType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="FullName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Name1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Name2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Name3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Name4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Name5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ApplNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SubsDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BankCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BranchCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="FirstName" type="Q1:Max70Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="FamilyName" type="Q1:Max70Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="FatherName" type="Q1:Max70Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="GrandFatherName" default="0" type="Q1:Max70Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="GrandGFatherName" type="Q1:Max70Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SubsCat" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="DateBirth" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PlaceBirth" default="0" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Gender" type="Q1:Max70Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Minor" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="DateBirthH" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="DateBirthG" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Title" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PoBox" type="Q1:Max70Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Address1" default="0" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Address2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="City" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Country" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ZipCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="CitizenShip" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Phone" type="Q1:PhoneNumberOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BankBicCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BankAccType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BankAccNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="MemberId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="MemberAcc" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Symbol" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="HoldingShare" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SharesSubscribe" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="InvestorType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ShareHolder" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="NoOfSubscriber" default="0" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="NoOfShares" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="TotalShares" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SharesValue" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PaymentMode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ChequeNo" default="0" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BanckAcc" default="0" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AttName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AttNIN" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AttNINType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AttPoBox" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AttCity" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AttCountry" default="0" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AttZipCode" default="0" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AttPhone" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="CreateDate" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Transaction" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Status" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="CIF" type="Q1:CIFOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="CustomerNames" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Dependent" type="tns:DependentType1" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="IPOCompanyResponseType">
    <xsd:sequence>
      <xsd:element name="company" type="tns:IPOCompanyInfoType" maxOccurs="unbounded" minOccurs="0"/>
      <xsd:element name="totalCompanies" type="xsd:int" maxOccurs="1" minOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="IPOInquiryResponseFullDetailsType">
    <xsd:all>
      <xsd:element name="SubsDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BankCode" type="Q1:BankCode" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BranchCode" type="Q1:Branch" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="FirstName" type="Q1:FirstName" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="FamilyName" type="Q1:FamilyName" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="FatherName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="GrandFatherName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="GrandGFatherName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SubsCat" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="DateBirth" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PlaceBirth" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Gender" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PoBox" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Address1" type="Q1:Max70Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Address2" type="Q1:Max70Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="City" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Country" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ZipCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Phone" type="Q1:PhoneNumberOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="NoOfSubscriber" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="NoOfShares" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="TotalShares" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SharesValue" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PaymentMode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ChequeNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BanckAcc" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AttName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AttNIN" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AttNINType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AttPoBox" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AttCity" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AttCountry" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AttZipCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AttPhone" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="CreateDate" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Transaction" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Status" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ClientType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Minor" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Title" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="CitizenShip" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BankBicCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BankAccType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BankAccNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="MemberId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="MemberAcc" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ShareHolder" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SharesReqEligible" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SharesReqAdditional" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BiddingPrice" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="MobileNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ChannelID" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="HoldingShare" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SharesSubscribe" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="RefundType" type="xsd:string" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>value     Description
					   0         Receive Cash Refund ...(Refund as Cash)
					   1         Utilize my excess   ...(Refund as Shares)</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="FinanceApplicationNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="IPOSellingCompanyType">
    <xsd:sequence minOccurs="0" maxOccurs="1">
      <xsd:element name="shortCIF" type="Q1:ShortCIF" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="portFolioNumber" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="numberRecord" type="xsd:int" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="ipoCompany" type="tns:IPOCompanyRecordType" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="IPOCompanyRecordType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="shareCode" type="xsd:int"/>
      <xsd:element name="symbolDescription" type="xsd:string"/>
      <xsd:element name="quantity" type="xsd:int"/>
      <xsd:element name="currentValue" type="Q1:Amount"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:element name="SubscriptionRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="SubscriptionRequestDetails" type="tns:SubscriptionRequestDetailsType" minOccurs="1" maxOccurs="1"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="SubscriptionResponse">
    <xsd:complexType>
      <xsd:all maxOccurs="1" minOccurs="0">
        <xsd:element name="SubscriptionResponseDetails" type="tns:SubscriptionResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="IPOFinInquiryRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="id" type="xsd:string" minOccurs="0"/>
        <xsd:element name="type" type="xsd:string" minOccurs="0"/>
        <xsd:element name="companyID" type="xsd:string" minOccurs="0"/>
        <xsd:element name="shareHolder" type="xsd:string" minOccurs="0"/>
        <xsd:element name="cif" type="Q1:CIFOptional" minOccurs="0"/>
        <xsd:element name="serial" type="xsd:string" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="IPOFinInquiryResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:FinInquiryResponseType" minOccurs="0"/>
        <xsd:element name="fault" type="fault:FaultType" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="IPOCompanyMaintenanceRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="companyID" type="Q1:CompanyId" minOccurs="0"/>
        <xsd:element name="function" type="xsd:string" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="IPOCompanyMaintenanceResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:Record" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="fault" type="fault:FaultType" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="DependentType">
    <xsd:attribute name="subscriberNIN" type="xsd:string"/>
    <xsd:attribute name="id" type="xsd:string"/>
    <xsd:attribute name="NIN" type="xsd:string"/>
    <xsd:attribute name="relationship" type="xsd:string"/>
    <xsd:attribute name="name">
      <xsd:simpleType>
        <xsd:restriction base="xsd:string">
          <xsd:maxLength value="40"/>
        </xsd:restriction>
      </xsd:simpleType>
    </xsd:attribute>
  </xsd:complexType>
  <xsd:complexType name="SubscriptionResponseType">
    <xsd:all>
      <xsd:element name="journalId" type="Q1:JournalId"/>
      <xsd:element name="serial" type="xsd:string"/>
      <xsd:element name="balanceAmount" type="Q1:MoneyOptional"/>
      <xsd:element name="amountSAR" type="Q1:MoneyOptional"/>
      <xsd:element name="dailyTotalAmount" type="Q1:MoneyOptional"/>
      <xsd:element name="dailyThirdPartyTotalAmount" type="Q1:MoneyOptional"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="SubscriptionRequestDetailsType">
    <xsd:sequence>
      <xsd:element name="companyId" type="Q1:CompanyId" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="nin" type="Q1:NIN" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="ninType" type="Q1:NIN" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="applicationId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="cif" type="Q1:CIF" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="subsribedDate" type="Q1:Date" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="name" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="firstName" type="Q1:FirstName" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="familyName" type="Q1:FamilyName" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="fatherName" type="Q1:Max70Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="grandFatherName" type="Q1:Max70Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="greatGFatherName" type="Q1:Max70Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="subscriberCategory" type="Q1:SubscriberCategory" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="dateOfBirth" type="Q1:Date" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="placeOfBirth" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="gender" type="Q1:Gender" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="postBox" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="address1" type="Q1:Max70Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="address2" type="Q1:Max70Text" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="city" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="country" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="zipCode" type="xsd:int" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="phone" type="Q1:PhoneNumberOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="noOfSubscribers" type="xsd:int" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="noOfShares" type="xsd:int" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="totalShares" type="xsd:int" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="sharesValue" type="xsd:int" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="chequeNo" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="attorneyName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="attorneyNIN" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="attorneyNINType" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="attorneyPostBox" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="attorneyCity" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="attorneyCountry" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="attorneyZipCode" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="attorneyPhone" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="shareHolder" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="accountId" type="Q1:AccountId" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="loanAccountId" type="Q1:AccountId" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="clientType" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="minor" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="title" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="citizenship" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankBicCode" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankAccountType" type="Q1:AccountType" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankAccountId" type="Q1:AccountId" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="memberId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="memberAccountId" type="Q1:AccountId" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="shortCIF" type="Q1:ShortCIF" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="atmBranch" type="Q1:Branch" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="payMethod" type="Q1:PaymentMode" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="override" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="tadawulNumber" minOccurs="0" maxOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="15"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="sharesReqEligible" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="sharesReqAdditional" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="biddingPrice" type="Q1:Amount" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="mobileNumber" type="Q1:MobileNumber" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="segmentLimit" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="financialProcessOnly" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="dependent" type="tns:DependentType" minOccurs="0" maxOccurs="unbounded"/>
      <xsd:element name="refundType" type="xsd:string" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>value     Description
					   0         Receive Cash Refund ...(Refund as Cash)
					   1         Utilize my excess   ...(Refund as Shares)</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="financeApplicationNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="retrieveDependents" type="xsd:string" minOccurs="0" maxOccurs="1" default="Y">
        <xsd:annotation>
          <xsd:documentation>RETRIEVE DEPENDENTS FLAG : Values can be "Y"/"N"</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="DependentType1">
    <xsd:sequence/>
    <xsd:attribute name="SubsNIN" type="xsd:string"/>
    <xsd:attribute name="DepNo" type="xsd:string"/>
    <xsd:attribute name="DepNIN" type="xsd:string"/>
    <xsd:attribute name="DepRelShip" type="xsd:string"/>
    <xsd:attribute name="DepName" type="xsd:string"/>
  </xsd:complexType>
  <xsd:complexType name="FinInquiryResponseType">
    <xsd:sequence>
      <xsd:element name="journal" type="xsd:string" minOccurs="0"/>
      <xsd:element name="scheduleTxnCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="branch" type="xsd:string" minOccurs="0"/>
      <xsd:element name="idType" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ipoCompany" type="xsd:string" minOccurs="0"/>
      <xsd:element name="glAccID" type="xsd:string" minOccurs="0"/>
      <xsd:element name="tadawalNum" type="xsd:string" minOccurs="0"/>
      <xsd:element name="sharesReqAdditional" type="xsd:string" minOccurs="0"/>
      <xsd:element name="status" type="xsd:string" minOccurs="0"/>
      <xsd:element name="bidPrice" type="xsd:string" minOccurs="0"/>
      <xsd:element name="refundedShares" type="xsd:string" minOccurs="0"/>
      <xsd:element name="sharesPerSubs" type="xsd:string" minOccurs="0"/>
      <xsd:element name="fromAccID" type="xsd:string" minOccurs="0"/>
      <xsd:element name="sharesReqEligibleValue" type="xsd:string" minOccurs="0"/>
      <xsd:element name="allotedShares" type="xsd:string" minOccurs="0"/>
      <xsd:element name="sharesReqEligible" type="xsd:string" minOccurs="0"/>
      <xsd:element name="idNo" type="xsd:string" minOccurs="0"/>
      <xsd:element name="cifNo" type="xsd:string" minOccurs="0"/>
      <xsd:element name="allotedValue" type="xsd:string" minOccurs="0"/>
      <xsd:element name="subsCount" type="xsd:string" minOccurs="0"/>
      <xsd:element name="totalValue" type="xsd:string" minOccurs="0"/>
      <xsd:element name="name" type="xsd:string" minOccurs="0"/>
      <xsd:element name="sharesReqAdditionalValue" type="xsd:string" minOccurs="0"/>
      <xsd:element name="cif" type="xsd:string" minOccurs="0"/>
      <xsd:element name="refundedValue" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ipoAction" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ipo" type="xsd:string" minOccurs="0"/>
      <xsd:element name="tadawalBankId" type="xsd:string" minOccurs="0"/>
      <xsd:element name="serial" type="xsd:string" minOccurs="0"/>
      <xsd:element name="mobileNumber" type="Q1:MobileNumberOptional" minOccurs="0"/>
      <xsd:element name="teller" type="xsd:string" minOccurs="0"/>
      <xsd:element name="loanAccID" type="xsd:string" minOccurs="0"/>
      <xsd:element name="payMode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="system" type="xsd:string" minOccurs="0"/>
      <xsd:element name="action" type="xsd:string" minOccurs="0"/>
      <xsd:element name="channelID" type="xsd:string" minOccurs="0"/>
      <xsd:element name="totalShares" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="IPOComapanyRecordsy">
    <xsd:sequence>
      <xsd:element name="no" type="xsd:string" minOccurs="0"/>
      <xsd:element name="companyID" type="xsd:string" minOccurs="0"/>
      <xsd:element name="nameArabic" type="xsd:string" minOccurs="0"/>
      <xsd:element name="nameEnglish" type="xsd:string" minOccurs="0"/>
      <xsd:element name="nameShortArabic" type="xsd:string" minOccurs="0"/>
      <xsd:element name="nameShortEnglish" type="xsd:string" minOccurs="0"/>
      <xsd:element name="ncbCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="maxSubscriberCount" type="xsd:string" minOccurs="0"/>
      <xsd:element name="minSharePerSubscriber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="maxSharePerSubscriber" type="xsd:string" minOccurs="0"/>
      <xsd:element name="maxSharePerApplication" type="xsd:string" minOccurs="0"/>
      <xsd:element name="multipleShares" type="xsd:string" minOccurs="0"/>
      <xsd:element name="amountPerShares" type="xsd:string" minOccurs="0"/>
      <xsd:element name="glaccount" type="xsd:string" minOccurs="0"/>
      <xsd:element name="validSub" type="xsd:string" minOccurs="0"/>
      <xsd:element name="validPayMode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="startDate" type="Q1:MWDate1" minOccurs="0"/>
      <xsd:element name="closeDate" type="Q1:MWDate1" minOccurs="0"/>
      <xsd:element name="dateCreate" type="Q1:MWDate1" minOccurs="0"/>
      <xsd:element name="dateMaintain" type="Q1:MWDate1" minOccurs="0"/>
      <xsd:element name="tellerCreate" type="xsd:string" minOccurs="0"/>
      <xsd:element name="tellerMaintain" type="xsd:string" minOccurs="0"/>
      <xsd:element name="activeFlag" type="xsd:string" minOccurs="0"/>
      <xsd:element name="offerringType" type="xsd:string" minOccurs="0"/>
      <xsd:element name="symbolCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="biddingPriceFlag" type="xsd:string" minOccurs="0"/>
      <xsd:element name="biddingPrice1" type="xsd:string" minOccurs="0"/>
      <xsd:element name="biddingPrice2" type="xsd:string" minOccurs="0"/>
      <xsd:element name="biddingPrice3" type="xsd:string" minOccurs="0"/>
      <xsd:element name="biddingPrice4" type="xsd:string" minOccurs="0"/>
      <xsd:element name="biddingPrice5" type="xsd:string" minOccurs="0"/>
      <xsd:element name="biddingPrice6" type="xsd:string" minOccurs="0"/>
      <xsd:element name="biddingPrice7" type="xsd:string" minOccurs="0"/>
      <xsd:element name="biddingPrice8" type="xsd:string" minOccurs="0"/>
      <xsd:element name="biddingPrice9" type="xsd:string" minOccurs="0"/>
      <xsd:element name="maxBidding" type="xsd:string" minOccurs="0"/>
      <xsd:element name="startTime" type="xsd:string" minOccurs="0"/>
      <xsd:element name="endTime" type="xsd:string" minOccurs="0"/>
      <xsd:element name="moduleType" type="xsd:string" minOccurs="0"/>
      <xsd:element name="serialStart" type="xsd:string" minOccurs="0"/>
      <xsd:element name="serialEnd" type="xsd:string" minOccurs="0"/>
      <xsd:element name="serialRunning" type="xsd:string" minOccurs="0"/>
      <xsd:element name="refundFlag" type="xsd:string" minOccurs="0"/>
      <xsd:element name="centralUnit" type="xsd:string" minOccurs="0"/>
      <xsd:element name="leadManagerCode" type="xsd:string" minOccurs="0"/>
      <xsd:element name="xmlEnabled" type="xsd:string" minOccurs="0"/>
      <xsd:element name="xmlAddress" type="xsd:string" minOccurs="0"/>
      <xsd:element name="xmlUserId" type="xsd:string" minOccurs="0"/>
      <xsd:element name="xmlPassword" type="xsd:string" minOccurs="0"/>
      <xsd:element name="allowChannels" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Record">
    <xsd:sequence>
      <xsd:element name="record" type="tns:IPOComapanyRecordsy" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
</xsd:schema>