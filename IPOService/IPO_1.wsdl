<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions name="IPOService" targetNamespace="http://corp.alahli.com/middlewareservices/ipo/1.0/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://corp.alahli.com/middlewareservices/ipo/1.0/" xmlns:header="http://corp.alahli.com/middlewareservices/header/1.0/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <wsdl:types>
    <xsd:schema>
      <xsd:import namespace="http://corp.alahli.com/middlewareservices/ipo/1.0/" schemaLocation="IPO.xsd"/>
      <xsd:import namespace="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" schemaLocation="securityheader.xsd"/>
      <xsd:import namespace="http://corp.alahli.com/middlewareservices/header/1.0/" schemaLocation="header.xsd"/>
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="GetIPOCompanyRequest">
    <wsdl:part name="body" element="tns:GetIPOCompanyRequest"/>
  </wsdl:message>
  <wsdl:message name="GetIPOCompanyResponse">
    <wsdl:part name="body" element="tns:GetIPOCompanyResponse"/>
  </wsdl:message>
  <wsdl:message name="InquireIPORequest">
    <wsdl:part element="tns:InquireIPORequest" name="body"/>
  </wsdl:message>
  <wsdl:message name="InquireIPOResponse">
    <wsdl:part element="tns:InquireIPOResponse" name="body"/>
  </wsdl:message>
  <wsdl:message name="ServiceHeader">
    <wsdl:part name="header" element="header:ServiceHeader"/>
  </wsdl:message>
  <wsdl:message name="Security" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
    <wsdl:part name="header" element="wsse:Security"/>
  </wsdl:message>
  <wsdl:message name="GetIPOSellingCompaniesRequest">
    <wsdl:part name="body" element="tns:GetIPOSellingCompaniesRequest"/>
  </wsdl:message>
  <wsdl:message name="GetIPOSellingCompaniesResponse">
    <wsdl:part name="body" element="tns:GetIPOSellingCompaniesResponse"/>
  </wsdl:message>
  <wsdl:message name="SubscriptionRequest">
    <wsdl:part name="body" element="tns:SubscriptionRequest"/>
  </wsdl:message>
  <wsdl:message name="SubscriptionResponse">
    <wsdl:part name="body" element="tns:SubscriptionResponse"/>
  </wsdl:message>
  <wsdl:message name="IPOFinInquiryRequest">
    <wsdl:part name="body" element="tns:IPOFinInquiryRequest"/>
  </wsdl:message>
  <wsdl:message name="IPOFinInquiryResponse">
    <wsdl:part name="body" element="tns:IPOFinInquiryResponse"/>
  </wsdl:message>
  <wsdl:message name="IPOCompanyMaintenanceRequest">
    <wsdl:part name="body" element="tns:IPOCompanyMaintenanceRequest"/>
  </wsdl:message>
  <wsdl:message name="IPOCompanyMaintenanceResponse">
    <wsdl:part name="body" element="tns:IPOCompanyMaintenanceResponse"/>
  </wsdl:message>
  <wsdl:portType name="IPOService">
    <wsdl:operation name="InquireIPO">
      <wsdl:documentation>This operation provides the initial offer and right issue features</wsdl:documentation>
      <wsdl:input message="tns:InquireIPORequest"/>
      <wsdl:output message="tns:InquireIPOResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetIPOCompany">
      <wsdl:documentation>Returns IPO Company information such as Company Id, Date Time frame (Start, End), Status, Refresh Date</wsdl:documentation>
      <wsdl:input message="tns:GetIPOCompanyRequest"/>
      <wsdl:output message="tns:GetIPOCompanyResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetIPOSellingCompanies">
      <wsdl:documentation>Returns IPO Selling Companies information linked to a portfolio such as Share Code, Share Desc , Quantity and Current Price</wsdl:documentation>
      <wsdl:input message="tns:GetIPOSellingCompaniesRequest"/>
      <wsdl:output message="tns:GetIPOSellingCompaniesResponse"/>
    </wsdl:operation>
    <wsdl:operation name="Subscribe">
      <wsdl:input message="tns:SubscriptionRequest"/>
      <wsdl:output message="tns:SubscriptionResponse"/>
    </wsdl:operation>
    <wsdl:operation name="IPOFinInquiry">
      <wsdl:input message="tns:IPOFinInquiryRequest"/>
      <wsdl:output message="tns:IPOFinInquiryResponse"/>
    </wsdl:operation>
    <wsdl:operation name="IPOCompanyMaintenance">
      <wsdl:input message="tns:IPOCompanyMaintenanceRequest"/>
      <wsdl:output message="tns:IPOCompanyMaintenanceResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="IPOService" type="tns:IPOService">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="InquireIPO">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/InquireIPO"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetIPOCompany">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/GetIPOCompany"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetIPOSellingCompanies">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/GetIPOSellingCompanies"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Subscribe">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/Subscribe"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IPOFinInquiry">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/IPOFinInquiry"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IPOCompanyMaintenance">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/IPOCompanyMaintenance"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="IPOService">
    <wsdl:port binding="tns:IPOService" name="IPOService">
      <soap:address location="http://**********:8080/cordys/com.alahli.gateway.NCBGateway.wcp?organization=o=FoundationServices,cn=cordys,cn=defaultInst,o=alahli.com"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>