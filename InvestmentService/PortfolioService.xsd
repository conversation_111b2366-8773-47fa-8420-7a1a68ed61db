<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema targetNamespace="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/" elementFormDefault="qualified" xmlns:tns="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:fault="http://corp.alahli.com/middlewareservices/fault/1.0/" xmlns:Q1="http://corp.alahli.com/schemas/middleware/temporaltypes/2012/10/29/">
  <xsd:import namespace="http://corp.alahli.com/middlewareservices/fault/1.0/" schemaLocation="fault.xsd"/>
  <xsd:import namespace="http://corp.alahli.com/schemas/middleware/temporaltypes/2012/10/29/" schemaLocation="schemas.xsd"/>
  <!--<AUTHOR> Eswar GetShareName Starts-->
  <xsd:element name="GetShareNameRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="shareCode" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Share Code</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetShareNameResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:GetShareNameType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetShareNameType">
    <xsd:sequence>
      <xsd:element name="share" type="tns:GetShareNameDetailsType" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="GetShareNameDetailsType">
    <xsd:sequence>
      <xsd:element name="shareCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Share code.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="shareNameEnglish" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>english name of the shares.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="shareNameArabic" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>arabic name of the shares.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lossPercentage" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>percentage of loss.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="alertCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="settlementCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <!--GetShareName Ends-->
  <!--<AUTHOR> Eswar GetShareDetails Starts-->
  <xsd:element name="GetShareDetailsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="shareCode" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Share code.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetShareDetailsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:GetShareDetailsType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetShareDetailsType">
    <xsd:sequence>
      <xsd:element name="share" type="tns:GetShareDetailsResponseType" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="GetShareDetailsResponseType">
    <xsd:sequence>
      <xsd:element name="shareCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="shareNameEnglish" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>english name of the shares.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="shareNameArabic" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>arabic name of the shares.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="sectorCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="sectorEnglish" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>english name of the sectors.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="sectorArabic" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>arabic name of the sectors.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="openingPrice" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>opening Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="closingPrice" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>closing Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="currentPrice" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>current Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lastPrice" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>last Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lastVolume" type="xsd:integer" maxOccurs="1" minOccurs="0" default="0">
        <xsd:annotation>
          <xsd:documentation>last Voulme</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="highPrice" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>high Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lowPrice" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>low Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="highPrice52" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="lowPrice52" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="shareQuantity" type="xsd:integer" maxOccurs="1" minOccurs="0" default="0">
        <xsd:annotation>
          <xsd:documentation>share Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bidPrice" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>bid Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bidQuantity" type="xsd:integer" maxOccurs="1" minOccurs="0" default="0">
        <xsd:annotation>
          <xsd:documentation>bid Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bestPrice" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>best price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="bestQuantity" type="xsd:integer" maxOccurs="1" minOccurs="0" default="0">
        <xsd:annotation>
          <xsd:documentation>best Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <!--GetShareName Ends-->
  <!--GetSectorName Starts-->
  <xsd:element name="GetSectorNameRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="sector" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Sector Name</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetSectorNameResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:GetSectorNameType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetSectorNameType">
    <xsd:sequence>
      <xsd:element name="SectorName" type="tns:GetSectorNamesDetailsType" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="GetSectorNamesDetailsType">
    <xsd:sequence>
      <xsd:element name="sector" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="sectorNameEnglish" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>English name of the sector</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="sectorNameArabic" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Arabic name of the sector</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <!--GetSectorName Ends-->
  <!--PortfolioSearch Starts-->
  <xsd:element name="PortfolioSearchRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="portfolioNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Port Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="id" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Identification Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="accountNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>BANCS Account Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="branch" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Branch Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="isPersonalFinance" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Personal Finance Flag.The default value is
							false.Should be true or false.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="PortfolioSearchResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:PortfolioSearchType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="PortfolioSearchType">
    <xsd:sequence>
      <xsd:element name="portfolio" type="tns:PortfolioSearchDetailsType" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PortfolioSearchDetailsType">
    <xsd:sequence>
      <xsd:element name="portfolioNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="name" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Name is the name on the portfolio</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="branch" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Branch is the branch number of the bank</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>AccNo is the BANCS account number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="id" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ID is the Identification number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="idType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>IDType is the Identification number type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="portfolioType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>PortfolioType is the type of the portfolio</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="status" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Status ( 0 Confirmed)</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="marketValue" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="personalFinanceAllowFlag" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>To allow trade “Y” or “N”</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="personalFinanceAllowDescription" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="dailyRpl" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Realized P/L Of The Day</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <!--PortfolioSearch Ends-->
  <!--PortfolioHoldings starts-->
  <xsd:element name="PortfolioHoldingsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="id" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="portfolioNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Portfolio
							Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="PortfolioHoldingsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:PortfolioHoldingsType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="PortfolioHoldingsType">
    <xsd:sequence>
      <xsd:element name="share" type="tns:PortfolioHoldingsDetailsType" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PortfolioHoldingsDetailsType">
    <xsd:sequence>
      <xsd:element name="shareCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ShareCode is the share codes</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="shareNameEnglish" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>English name of the shares</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="shareNameArabic" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Arabic name of the shares</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="quantity" type="xsd:integer" maxOccurs="1" minOccurs="0" default="0">
        <xsd:annotation>
          <xsd:documentation>quantity of the shares</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="pledged" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>pledged</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ownQuantity" type="xsd:integer" maxOccurs="1" minOccurs="0" default="0">
        <xsd:annotation>
          <xsd:documentation>quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lastTradePrice" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>last Trade Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="marketValue" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>market Value of the Shares</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="costPrice" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>cost Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="costValue" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>cost Value</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="dailyRpl" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Realized P/L Of The  Day</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <!--PortfolioHoldings ends-->
  <!--GetBuyingPowerDetails starts-->
  <xsd:element name="GetBuyingPowerDetailsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="portfolioNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Portfolio Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetBuyingPowerDetailsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:GetBuyingPowerType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetBuyingPowerType">
    <xsd:sequence>
      <xsd:element name="buyingPower" type="tns:GetBuyingPowerDetailsType" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="GetBuyingPowerDetailsType">
    <xsd:sequence>
      <xsd:element name="bancsAccountId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BANCS Account ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="availableBalance" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Available Balance</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="buyingPower" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Buying Power</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="currentHolding" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Current Holding</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <!--GetBuyingPowerDetails ends-->
  <!--GetMarketOverviewDetails starts-->
  <xsd:element name="GetMarketOverviewDetailsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:annotation>
          <xsd:documentation>No Input is required for this service.</xsd:documentation>
        </xsd:annotation>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetMarketOverviewDetailsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:GetMarketOverviewType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetMarketOverviewType">
    <xsd:sequence>
      <xsd:element name="losers" type="tns:GetLosersGainersType" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="gainers" type="tns:GetLosersGainersType" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="index" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Index.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="indexNetChange" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Index Net Change.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="time" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Time.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="status" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Status.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="quantity" type="xsd:string" maxOccurs="1" minOccurs="0" default="0">
        <xsd:annotation>
          <xsd:documentation>Quantity.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="value" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Value.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="traded" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Traded.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="advanced" type="tns:GetMarketOverviewMandatoryDetailsType" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="declined" type="tns:GetMarketOverviewMandatoryDetailsType" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="unchanged" type="tns:GetMarketOverviewMandatoryDetailsType" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="GetLosersGainersType">
    <xsd:sequence>
      <xsd:element name="shares" type="tns:GetShareDetailsMarketOverviewType" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="GetShareDetailsMarketOverviewType">
    <xsd:sequence>
      <xsd:element name="shareCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="shareNameEnglish" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="shareNameArabic" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="amount" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="GetMarketOverviewMandatoryDetailsType">
    <xsd:sequence>
      <xsd:element name="number" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="quantity" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="amount" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <!--GetMarketOverviewDetails ends-->
  <!--GetLocalSharesStatement Starts-->
  <xsd:element name="GetLocalSharesStatementRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="portfolioNumber" type="xsd:integer" maxOccurs="1" minOccurs="1" default="0">
          <xsd:annotation>
            <xsd:documentation>Portfolio Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fromDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>From Date</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="toDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>To Date</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="accountNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Account Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="transactionType" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Transaction Type</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetLocalSharesStatementResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:GetLocalSharesStatementType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetLocalSharesStatementType">
    <xsd:sequence>
      <xsd:element name="portfolioNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="statement" type="tns:GetLocalSharesStatementDetailsType" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="GetLocalSharesStatementDetailsType">
    <xsd:sequence>
      <xsd:element name="accountNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Account Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="symbolName" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Symbol Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="orderNumber" type="xsd:integer" maxOccurs="1" minOccurs="0" default="0">
        <xsd:annotation>
          <xsd:documentation>Order Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="tradeDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Trade Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="tranType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Tran Type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="tradedQuantity" type="xsd:integer" maxOccurs="1" minOccurs="0" default="0">
        <xsd:annotation>
          <xsd:documentation>Traded Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="tradedPrice" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Traded Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="commission" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Commission</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="totalTradeAmount" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Total Trade Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="transactionPrice" type="Q1:MoneyOptional" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Transaction Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <!--CreatePortfolioAccount Starts-->
  <xsd:element name="CreatePortfolioAccountRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="MSGHDR" type="tns:messageHeaderType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="CTRL" type="tns:messageCTRLType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="WFLOG" type="tns:messageWFLOGType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="PTY" type="tns:messagePartyType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="PTYADD" type="tns:messagePartyADDType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="PTYLNG" type="tns:messagePartyLangType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="PTYACCTADDR" type="tns:messagePartyACCTAddressType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="PTYIDS" type="tns:messagePartyIDSType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="PTYCONT" type="tns:messagePartyContactType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="PTYSYSLNK" type="tns:messagePartyLinkType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="PTYDATA" type="tns:messagePartyDataType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="ACCT" type="tns:accountDataType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="PRTFACCTADDR" type="tns:messagePRTFACCTADDRType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="PRTFACCTCONT" type="tns:messagePRTFACCTCONTType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="PRTFACCTDTL" type="tns:messagePRTFACCTDTLType" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="messageHeaderType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="MsgDateTime" type="Q1:DateFormat" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="SessionId" type="xsd:int" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="ChannelId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="FunctionId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="UserId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="Lang" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="TerminalId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="Reserved1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="messageCTRLType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="CheckBancsAcctFlg" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="messageWFLOGType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="WfUserId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="WfFunctionId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="WfUserIdApprover" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="WfTerminalAddress" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="WfSessionId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="WfApproverResponse" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="WfLogTimeStamp" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="WfAuthLevel" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="messagePartyType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="ApprovalDate" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ApprovalDateH" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="CreationBranchCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="CrtDate" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="CrtUsr" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="EcnId" type="xsd:string"/>
      <xsd:element name="FamilyName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="FirstName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="FullName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="LastMaintDate" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="LastMaintUsr" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="IsSpecialCaseFlg" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="LanguageCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Lastname" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="LegalEntityCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="MainbranchNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="MiddleName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="NationalityCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="OccupancyCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PartyId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PartyStatusCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PartySubTypeCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PartyTypeCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ResidencyCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SamaApprNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SamaNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="TitleCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SubLegalEntityCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="RegistrationStatusCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AcctNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="messagePartyADDType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="TotalPoints" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ClassCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="StatementElectronicCopyFlg" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="CustCategoryCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AddRmCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AddRmANm" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AddRmLNm" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="messagePartyLangType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="FamilyName2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="FirstName2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="FullName2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="LanguageCd2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="LastName2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="MiddleName2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="messagePartyACCTAddressType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="Address1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Address2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Address3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Address4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Address5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AddressInd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AddressStopDate" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AddressStopDateH" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AddressTypeCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AdditionalNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ApartmentNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BasicNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="City" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="CountryCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="DefaultAddressFlg" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="District" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="HouseNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="InternalMail" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="LangCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PartyAddressId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PoBox" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PostalCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Status" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Street" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="UnitNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="messagePartyIDSType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="DefaultFlg" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="IdExpiryDate" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="IdExpiryDateH" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="IdIssueDate" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="IdIssueDateH" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="IdIssuePlace" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="IdLanguageCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="IdNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="IdTypeCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="messagePartyContactType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="ContactId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ContactTypeCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="DefaultContact" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Email" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Fax1Ext" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="FaxNo1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="MobileNo1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Phone1Ext" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PhoneNo1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="messagePartyLinkType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="SystemCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ChanelIndFlg" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SysCustUsrNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="StatusCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="StartDate" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="StartDateH" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="EndDate" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="EndDateH" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="messagePartyDataType">
    <xsd:sequence>
      <xsd:element name="PTYIND" type="tns:messagePartyINDType" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="messagePartyINDType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="DateOfBirth" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="DateOfBirthH" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="GenderCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="accountDataType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="AcctId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AcctNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AcctStatusCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AcctTypeCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AcctSubTypeCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="CreateBranchCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="CPANo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="messagePRTFACCTADDRType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="Address1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Address2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Address3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Address4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Address5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AddressInd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AdditionalNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ApartmentNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="BasicNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="City" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="CountryCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="District" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="HouseNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="InternalMail" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="LangCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PoBox" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PartyAddressId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PostalCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Status" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Street" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="UnitNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="messagePRTFACCTCONTType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="ContactId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Fax1Ext" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="FaxNo1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="MobileNo1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Phone1Ext" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PhoneNo1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Email" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="messagePRTFACCTDTLType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="AcctDescANm" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AcctDescLNm" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="CreateBranchCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Notes" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PrintStatementFlg" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PrtfMailOptCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PrtfNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ExistPrtfFlg" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PortfolioCategoryCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="CreatePortfolioAccountResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:CreatePortfolioAccountResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="CreatePortfolioAccountResponseType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="CustNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="RequestNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <!--CreatePortfolioAccount Ends-->
  <!--<AUTHOR> Ajay CheckTradableRightsEligibility & SubscribeTradableRights 
		28 DEC 2016 Starts-->
  <xsd:element name="TradableRightsSubscriptionRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="shortCif" type="Q1:ShortCIF" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="portfolioNumber" type="xsd:string" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Portfolio Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="shareCode" type="xsd:string" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Share code.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="shareQuantity" type="xsd:integer" maxOccurs="1" minOccurs="1" default="0">
          <xsd:annotation>
            <xsd:documentation>share Quantity</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="TradableRightsSubscriptionResponse">
    <xsd:complexType>
      <xsd:choice>
        <xsd:element name="success" type="tns:TradableRightsSubscriptionResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:choice>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="TradableRightsSubscriptionResponseType">
    <xsd:sequence>
      <xsd:element name="orderNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="TradableRightsEligibilityRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="shortCif" type="Q1:ShortCIF" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="portfolioNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Portfolio Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="TradableRightsEligibilityResponse">
    <xsd:complexType>
      <xsd:choice>
        <xsd:element name="success" type="tns:TradableRightsEligibilityResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:choice>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="TradableRightsEligibilityResponseType">
    <xsd:sequence>
      <xsd:element name="share" type="tns:TradableRightsEligibilityResponseDetailsType" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradableRightsEligibilityResponseDetailsType">
    <xsd:sequence>
      <xsd:element minOccurs="0" name="customerCode" type="xsd:string"/>
      <xsd:element minOccurs="0" name="shortCif" type="Q1:ShortCIF"/>
      <xsd:element minOccurs="0" name="portfolioNumber" type="xsd:string"/>
      <xsd:element minOccurs="0" name="shareCode" type="xsd:string"/>
      <xsd:element minOccurs="0" name="shareNameEnglish" type="xsd:string"/>
      <xsd:element minOccurs="0" name="shareNameArabic" type="xsd:string"/>
      <xsd:element minOccurs="0" name="ownedQuantity" type="xsd:integer"/>
      <xsd:element minOccurs="0" name="shareOfferPrice" default="0.0" type="Q1:MoneyOptional"/>
      <xsd:element minOccurs="0" name="availableQuantity" type="xsd:integer"/>
      <xsd:element minOccurs="0" name="startDateGregorian" type="Q1:MWDateOptional"/>
      <xsd:element minOccurs="0" name="endDateGregorian" type="Q1:MWDateOptional"/>
      <xsd:element minOccurs="0" name="startDateHijri" type="Q1:MWDateOptional"/>
      <xsd:element minOccurs="0" name="endDateHijri" type="Q1:MWDateOptional"/>
      <xsd:element minOccurs="0" name="subStartDateGregorian" type="Q1:MWDateOptional"/>
      <xsd:element minOccurs="0" name="subEndDateGregorian" type="Q1:MWDateOptional"/>
      <xsd:element minOccurs="0" name="subStartDateHijri" type="Q1:MWDateOptional"/>
      <xsd:element minOccurs="0" name="subEndDateHijri" type="Q1:MWDateOptional"/>
    </xsd:sequence>
  </xsd:complexType>
  <!--<AUTHOR> Ajay CheckTradableRightsEligibility & SubscribeTradableRights 
		28 DEC 2016 Ends-->
  <xsd:element name="ChargeCustomerRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="shortCif" type="Q1:ShortCIF" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Customer Identification Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="amount" type="Q1:Amount" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Amount in decimals</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="cashAccountNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Account Number of the Customer to be Charged</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="ChargeCustomerResponse">
    <xsd:complexType>
      <xsd:choice>
        <xsd:element name="success" type="tns:ChargeCustomerResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:choice>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="ChargeCustomerResponseType">
    <xsd:sequence>
      <xsd:element name="uniqueReferenceId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Unique Reference Id given to the Customer for
						identification</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetTadawulUserRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="cifNumber" type="Q1:CIF" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Customer identification number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetTadawulUserResponse">
    <xsd:complexType>
      <xsd:choice>
        <xsd:element name="success" type="tns:GetTadawulUserResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:choice>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetTadawulUserResponseType">
    <xsd:sequence>
      <xsd:element name="CustomerDetails" type="tns:CustomerDetailsResponseType" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CustomerDetailsResponseType">
    <xsd:sequence>
      <xsd:element name="portfolioNumber" type="xsd:string" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>Portfolio Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="status" type="xsd:string" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>Status</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="userName" type="xsd:string" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>Name of the User</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ActivateTadawulUserRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="cifNumber" type="Q1:CIF" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Customer Identification Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="portfolioNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Portfolio Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="userName" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Name of the User</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="ActivateTadawulUserResponse">
    <xsd:complexType>
      <xsd:choice>
        <xsd:element name="success" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:choice>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InquireCashBlockRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="IANumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>IA Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="inquiryRequestUser" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="inquiryRequestDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="inquiryRequestId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InquireCashBlockResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:CashBlockType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="CashBlockType">
    <xsd:sequence>
      <xsd:element name="status" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="IANumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>IA Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="source" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="amountBookedList" type="tns:AmountBookedListType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="activeCashBlockList" type="tns:ActiveCashBlockListType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="transactionLogNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="transactionLogDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="inquiryRequestID" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AmountBookedListType">
    <xsd:sequence>
      <xsd:element name="amountBooked" type="Q1:MoneyOptional" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ActiveCashBlockListType">
    <xsd:sequence>
      <xsd:element name="activeCashBlock" type="tns:ActiveCashBlockType" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ActiveCashBlockType">
    <xsd:sequence>
      <xsd:element name="amount" type="Q1:MoneyOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="blockReasonDescription" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cashAccountNo" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>cpa</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blockID" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="blockInsertDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="narrative" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="source" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="InquireInvestmentProfileRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="idNumber" type="Q1:NIN" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="idType" type="Q1:NINType" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InquireInvestmentProfileResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:InvestmentProfileType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="InvestmentProfileType">
    <xsd:sequence>
      <xsd:element name="cashAccountsList" type="tns:CashAccountsListType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="portfolioHoldingsList" type="tns:PortfolioHoldingsListType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="mutualFundsList" type="tns:MutualFundsListType" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CashAccountsListType">
    <xsd:sequence>
      <xsd:element name="cashAccount" maxOccurs="unbounded" minOccurs="0" type="tns:CashAccountType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CashAccountType">
    <xsd:sequence>
      <xsd:element name="accountId" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="14"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="balance" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="referencePortfolio" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="14"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="idNumber" type="Q1:NIN" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="idType" type="Q1:NINType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cif" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>CIF</xsd:documentation>
        </xsd:annotation>
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="14"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="IANumber" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>IA Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PortfolioHoldingsListType">
    <xsd:sequence>
      <xsd:element name="portfolioHoldings" maxOccurs="unbounded" minOccurs="0" type="tns:PortfolioHoldingsType1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PortfolioHoldingsType1">
    <xsd:sequence>
      <xsd:element name="portfolioNumber" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="14"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="isLocal" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="2"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="companyCode" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="50"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="sharesQuantity" type="Q1:Amount" maxOccurs="1" minOccurs="0" default="0.0"/>
      <xsd:element name="marketPrice" type="Q1:Amount" maxOccurs="1" minOccurs="0" default="0.0"/>
      <xsd:element name="idNumber" type="Q1:NIN" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="idType" type="Q1:NINType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cif" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>IA Number in ETS</xsd:documentation>
        </xsd:annotation>
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="14"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="IANumber" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>IA Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="MutualFundsListType">
    <xsd:sequence>
      <xsd:element name="mutualFund" maxOccurs="unbounded" minOccurs="0" type="tns:MutualFundType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="MutualFundType">
    <xsd:sequence>
      <xsd:element name="fundName" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="500"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="isLocal" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="2"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="unitsQuantity" type="Q1:Amount" maxOccurs="1" minOccurs="0" default="0.0"/>
      <xsd:element name="unitPrice" type="Q1:Amount" maxOccurs="1" minOccurs="0" default="0.0"/>
      <!--<xsd:element name="cashAccountId" type="Q1:AccountId" maxOccurs="1" minOccurs="0"></xsd:element>-->
      <xsd:element name="idNumber" type="Q1:NIN" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="idType" type="Q1:NINType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cif" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>IA Number in ETS</xsd:documentation>
        </xsd:annotation>
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="14"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="IANumber" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>IA Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="CashAccountDetailsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountId" type="Q1:AccountId" maxOccurs="1" minOccurs="1"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CashAccountDetailsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0">
          <xsd:complexType>
            <xsd:sequence>
              <xsd:element name="cashAccountNumber" type="Q1:AccountIdOptional" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Customer account number in ETS
										system. Used for trading
										purpose.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="availableBalance" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="balance" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="accountId" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="currency" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="overDraft" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="held" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="status" type="xsd:string" maxOccurs="1" minOccurs="0"/>
              <xsd:element name="unsettledBalance" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0"/>
            </xsd:sequence>
          </xsd:complexType>
        </xsd:element>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CashUnblockRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="blockID" type="xsd:string" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="IANumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>IA Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="unblockReasonDescription" type="xsd:string" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="unblockRequestUser" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="unblockRequestDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CashUnblockResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:CashUnblockType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="CashUnblockType">
    <xsd:sequence>
      <xsd:element name="blockID" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="unblockDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="status" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="transactionLogNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="transactionLogDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="CashBlockRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="externalReference" type="xsd:string" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="IANumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>IA Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="narrativeCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="cpaNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="source" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="ftOutBlock" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>fund transfer Out block</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="ftInBlock" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>fund transfer In block</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="cpaToCpaBlock" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="blockByAmount" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="amountToBlock" type="Q1:MoneyOptional" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="csdOrTradexID" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="buyOrderBlock" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="sellOrderBlock" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="securityTransferInBlock" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="securityTransferOutBlock" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="interbankTransferBlocked" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="subscriptionBlock" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="redemptionBlock" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="switchBlock" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="thimarBlock" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="transferMutualFundBlock" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="blockReasonDescription" type="xsd:string" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="blockRequestUser" type="xsd:string" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="blockRequestDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="blockRequestEndDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CashBlockResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:CashBlockResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="CashBlockResponseType">
    <xsd:sequence>
      <xsd:element name="blockID" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="blockDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="transactionLogNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="transactionLogDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="status" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="PortfolioListRequest">
    <xsd:complexType>
      <xsd:annotation>
        <xsd:documentation>DPM Account Number in case of shares.</xsd:documentation>
      </xsd:annotation>
      <xsd:sequence>
        <xsd:element name="refType" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Type of pledging will be identified
            						on the basis of value: P if
            						'Portfolio Number' D if 'Type' is
            						'DPM Account Number'. Required to be sent only in case of shares.</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="1"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="cif" type="Q1:CIFOptional" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="portfolioNumber" type="xsd:string" minOccurs="0" maxOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Portfolio Number, this can be MF or shares portfolio no based upon portfolio type sent in the request.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="DPMAccountId" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="21"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="portfolioType" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Shares : S, MutualFund: M</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="1"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="PortfolioListResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:PortfolioListType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="PortfolioListType">
    <xsd:sequence>
      <xsd:element name="records" type="tns:PortfolioRecordsType" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PortfolioRecordsType">
    <xsd:sequence>
      <xsd:element name="record" type="tns:PortfolioRecordType" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PortfolioRecordType">
    <xsd:annotation>
      <xsd:documentation>Currency of Portfolio/DPM Account</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="portfolioNumber" type="xsd:string" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>Portfolio Number, this can be MF or shares
        				portfolio no based upon portfolio type sent in
        				the request.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DPMAccountId" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>DPM Account Number in case of shares.</xsd:documentation>
        </xsd:annotation>
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="21"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="currency" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="totalValue" type="Q1:Amount" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation/>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="availableAmount" type="Q1:Amount" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation/>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="portfolioName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cif" type="Q1:CIFOptional" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetPortfolioDetailsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="portfioloNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetPortfolioDetailsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:PortfolioDetailsType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="PortfolioDetailsType">
    <xsd:sequence>
      <xsd:element name="spaNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>SPA NUMBER</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="portfolioNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>PORTFOLIO NUMBER</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="portfolioCategory" type="tns:string5" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>PORTFOLIO CATEGORY</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="portfolioStatus" type="tns:string1" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>PORTFOLIO STATUS</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="portfolioOpenDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>PORTFOLIO OPENDATE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="portfolioBranch" type="Q1:Branch" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>PORTFOLIO BRANCH</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blockSecIn" type="tns:string1" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BLOCKSECIN</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blockSecOut" type="tns:string1" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BLOCKSECOUT</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blockSecBuy" type="tns:string1" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BLOCKSECBUY</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blockSecSell" type="tns:string1" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BLOCKSECSELL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blockMFSubscription" type="tns:string1" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BLOCK MF SUBSCRIPTION</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blockMFRedemption" type="tns:string1" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BLOCK MF REDEMPTION</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blockMFSwitch" type="tns:string1" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BLOCK MF SWITCH</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blockMFThimar" type="tns:string1" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BLOCK MF THIMAR</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blockMFTransfer" type="tns:string1" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BLOCK MF TRANSFER</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="portfolioValue" type="tns:PortfolioValueType" maxOccurs="unbounded" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>list  Portfolio Value Per Currency</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="holdings" type="tns:HoldingsType" maxOccurs="unbounded" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>list of Customer Holdings</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cashAccounts" type="tns:CashAccountsType" maxOccurs="unbounded" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>list of Customer Holdings</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CashAccountsType">
    <xsd:sequence>
      <xsd:element name="cpaNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>CPA Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cpaCategory" type="tns:string5" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>CPA Category</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="currency" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Currency</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cpaStatus" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>CPA Status</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cpaBBAN" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>CPA Number with check digit</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cpaIBAN" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>CPA Number in IBAN format</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cpaBranch" type="Q1:Branch" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>CPA Branch</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="currentAccountId" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Linked Current Account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="portfolioNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Linked Portfolio Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="balance" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Total Cash</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blockFTIn" type="tns:string1" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BLOCKFTIN</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blockFTOut" type="tns:string1" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BLOCKFTOUT</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blockInternalCash" type="tns:string1" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BLOCKINTERNALCASH</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blockDivAutoFTOut" type="tns:string1" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BLOCKDIVAUTOFTOUT</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="HoldingsType">
    <xsd:sequence>
      <xsd:element name="shareCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>SHARE CODE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="shareNameEnglish" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>SHARE NAME ENGLISH</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="shareNameArabic" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>SHARE NAME ARABIC</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="availableQuantity" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>AVAILABLE QUANTITY</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blockedQuantity" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>BLOCKED QUANTITY</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="totalQuantity" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>TOTAL QUANTITY</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lastPrice" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>LAST PRICE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lastPriceDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>LAST PRICE DATE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="marketValue" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>MARKET VALUE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="costPrice" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>COST PRICE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="costValue" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>COST VALUE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="unrealizedProfit" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>UNREALIZED PROFIT</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="currency" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>CURRENCY</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PortfolioValueType">
    <xsd:sequence>
      <xsd:element name="cash" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Cash Balance</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="marketValue" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Total Holdings Market Value</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="totalValue" type="Q1:AmountOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Total of  Cash and  Market value</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="currency" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Currency</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:simpleType name="string1">
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="1"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="string5">
    <xsd:restriction base="xsd:string">
      <xsd:maxLength value="5"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:element name="GetTreasuryCurrencyRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="rmPosition" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>RM POSITION</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="cif" type="Q1:CIFOptional" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetTreasuryCurrencyResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:GetTreasuryCurrencyResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetTreasuryCurrencyResponseType">
    <xsd:sequence>
      <xsd:element name="currencyList" maxOccurs="1" minOccurs="0" type="tns:currencyListType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="currencyListType">
    <xsd:sequence>
      <xsd:element name="currency" maxOccurs="unbounded" minOccurs="0" type="Q1:ISOCurrencyCodeOptional"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetClientPortfolioSummaryRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="rmPosition" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>RM POSITION</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="cif" type="Q1:CIFOptional" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetClientPortfolioSummaryResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:GetClientPortfolioSummaryResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetClientPortfolioSummaryResponseType">
    <xsd:sequence>
      <xsd:element name="portfolioSummary" maxOccurs="unbounded" minOccurs="0" type="tns:PortfolioSummaryType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PortfolioSummaryType">
    <xsd:sequence>
      <xsd:element name="cif" type="Q1:CIFOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>CIF</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="customerSegment" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Customer Segment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="sector" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Sector</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="salesFigure" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Sales Figure</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="riskRating" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Risk Rating</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="customerCreationDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Customer Creation Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetVolumeByProductRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="category" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>CATEGORY can have below values 1.
        							CASH_MARKET 2. LENDING 3. TRADE 4.
        							TREASURY</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="rmPosition" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>RM Position</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="groupBy" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>GROUP BY</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="cif" type="Q1:CIFOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>CIF</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="currency" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Currency</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetVolumeByProductResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:GetVolumeByProductResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetVolumeByProductResponseType">
    <xsd:sequence>
      <xsd:element name="record" maxOccurs="unbounded" minOccurs="0" type="tns:GetVolumeByProductRecordType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="GetVolumeByProductRecordType">
    <xsd:sequence>
      <xsd:element name="month" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>MONTH</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="year" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>YEAR- YYYY format</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="quarter" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>QUARTER: Q1,Q2,Q3,Q4</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="monthActual" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>MONTH ACTUAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="countActual" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>COUNT ACTUAL</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="productName" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>PRODUCT NAME</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="currency" maxOccurs="1" minOccurs="0" type="Q1:ISOCurrencyCodeOptional">
        <xsd:annotation>
          <xsd:documentation>CURRENCY</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="intType" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>INT TYPE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="termType" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>TERM TYPE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="purpose" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>PURPOSE</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="channelFlag" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>CHANNEL FLAG</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetCrossSellBenchmarkRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="rmPosition" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>RM Position</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="cif" type="Q1:CIFOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>CIF</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetCrossSellBenchmarkResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:GetCrossSellBenchmarkResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetCrossSellBenchmarkResponseType">
    <xsd:sequence>
      <xsd:element name="record" maxOccurs="unbounded" minOccurs="0" type="tns:GetCrossSellBenchmarkRecordType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="GetCrossSellBenchmarkRecordType">
    <xsd:sequence>
      <xsd:element name="actualRatio" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>ACTUAL RATIO</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="peerGroupRatio" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>PEER GROUP RATIO</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="groupCount" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>GROUP COUNT</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="category" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>CATEGORY</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="InquireCPALinkedAccountListRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="requestId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="shortCif" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InquireCPALinkedAccountListResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:InquireCPALinkedAccountListSuccessType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="InquireCPALinkedAccountListSuccessType">
    <xsd:sequence>
      <xsd:element name="inquireCPALinkedAccountListHeader" maxOccurs="1" minOccurs="0" type="tns:InquireCPALinkedAccountListHeaderType"/>
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="inquireCPALinkedAccountListItem" type="tns:InquireCPALinkedAccountListItemType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="InquireCPALinkedAccountListHeaderType">
    <xsd:sequence>
      <xsd:element name="requestId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="resultCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="resultDescription" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="resultNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="shortCif" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="InquireCPALinkedAccountListItemType">
    <xsd:sequence>
      <xsd:element name="cashPositionAccountNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="currentAccountNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="UpdateAccountLinkedToCPARequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="requestId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="channelId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="cashPositionAccountNumber" type="xsd:string" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="currentAccountNumber" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="UpdateAccountLinkedToCPAResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:UpdateAccountLinkedToCPASuccessType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="UpdateAccountLinkedToCPASuccessType">
    <xsd:sequence>
      <xsd:element name="requestId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="resultCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="resultDescription" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="UpdateCustomerCommunicationDataRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="requestId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="channelId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="shortCif" type="xsd:string" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="mobileNumber" type="Q1:MobileNumberOptional" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="email" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="UpdateCustomerCommunicationDataResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:UpdateCustomerCommunicationDataSuccessType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="UpdateCustomerCommunicationDataSuccessType">
    <xsd:sequence>
      <xsd:element name="requestId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="resultCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="resultDescription" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="RetrieveEmailAndMobileRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="requestId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="shortCif" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="RetrieveEmailAndMobileResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:RetrieveEmailAndMobileSuccessType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="RetrieveEmailAndMobileSuccessType">
    <xsd:sequence>
      <xsd:element name="requestId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="shortCif" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="email" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="mobileNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
</xsd:schema>