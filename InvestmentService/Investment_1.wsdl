<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions name="InvestmentService" targetNamespace="http://corp.alahli.com/middlewareservices/investment/1.0/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://corp.alahli.com/middlewareservices/investment/1.0/" xmlns:tns1="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:header="http://corp.alahli.com/middlewareservices/header/1.0/">
  <wsdl:types>
    <xsd:schema>
      <xsd:import namespace="http://corp.alahli.com/middlewareservices/investment/1.0/" schemaLocation="Investment.xsd"/>
      <xsd:import namespace="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/" schemaLocation="PortfolioService.xsd"/>
      <xsd:import namespace="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" schemaLocation="securityheader.xsd"/>
      <xsd:import namespace="http://corp.alahli.com/middlewareservices/header/1.0/" schemaLocation="header.xsd"/>
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="ServiceHeader">
    <wsdl:part name="header" element="header:ServiceHeader"/>
  </wsdl:message>
  <wsdl:message name="Security" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
    <wsdl:part name="header" element="wsse:Security"/>
  </wsdl:message>
  <wsdl:message name="AccountDetailsRequest">
    <wsdl:part name="body" element="tns:InvestmentAccountDetailsRequest"/>
  </wsdl:message>
  <wsdl:message name="AccountDetailsResponse">
    <wsdl:part name="body" element="tns:InvestmentAccountDetailsResponse"/>
  </wsdl:message>
  <wsdl:message name="BalanceProfileRequest">
    <wsdl:part name="body" element="tns:InvestmentBalanceProfileRequest"/>
  </wsdl:message>
  <wsdl:message name="BalanceProfileResponse">
    <wsdl:part name="body" element="tns:InvestmentBalanceProfileResponse"/>
  </wsdl:message>
  <wsdl:message name="CancelFundTradeRequest">
    <wsdl:part name="body" element="tns:CancelFundTradeRequest"/>
  </wsdl:message>
  <wsdl:message name="CancelFundTradeResponse">
    <wsdl:part name="body" element="tns:CancelFundTradeResponse"/>
  </wsdl:message>
  <wsdl:message name="FundPendingTradeRequest">
    <wsdl:part name="body" element="tns:FundPendingTradeRequest"/>
  </wsdl:message>
  <wsdl:message name="FundPendingTradeResponse">
    <wsdl:part name="body" element="tns:FundPendingTradeResponse"/>
  </wsdl:message>
  <wsdl:message name="InvestmentFundTransactionsRequest">
    <wsdl:part name="body" element="tns:InvestmentFundTransactionsRequest"/>
  </wsdl:message>
  <wsdl:message name="InvestmentFundTransactionsResponse">
    <wsdl:part name="body" element="tns:InvestmentFundTransactionsResponse"/>
  </wsdl:message>
  <wsdl:message name="InvestmentQuestionersRequest">
    <wsdl:part name="body" element="tns:InvestmentQuestionersRequest"/>
  </wsdl:message>
  <wsdl:message name="InvestmentQuestionersResponse">
    <wsdl:part name="body" element="tns:InvestmentQuestionersResponse"/>
  </wsdl:message>
  <wsdl:message name="RedeemInvestmentFundRequest">
    <wsdl:part name="body" element="tns:RedeemInvestmentFundRequest"/>
  </wsdl:message>
  <wsdl:message name="RedeemInvestmentFundResponse">
    <wsdl:part name="body" element="tns:RedeemInvestmentFundResponse"/>
  </wsdl:message>
  <wsdl:message name="SwitchInvestmentFundRequest">
    <wsdl:part name="body" element="tns:SwitchInvestmentFundRequest"/>
  </wsdl:message>
  <wsdl:message name="SwitchInvestmentFundResponse">
    <wsdl:part name="body" element="tns:SwitchInvestmentFundResponse"/>
  </wsdl:message>
  <wsdl:message name="InvestmentSubscriptionRequest">
    <wsdl:part name="body" element="tns:InvestmentSubscriptionRequest"/>
  </wsdl:message>
  <wsdl:message name="InvestmentSubscriptionResponse">
    <wsdl:part name="body" element="tns:InvestmentSubscriptionResponse"/>
  </wsdl:message>
  <wsdl:message name="ProgramSubscriptionRequest">
    <wsdl:part name="body" element="tns:ProgramSubscriptionRequest"/>
  </wsdl:message>
  <wsdl:message name="ProgramSubscriptionResponse">
    <wsdl:part name="body" element="tns:ProgramSubscriptionResponse"/>
  </wsdl:message>
  <wsdl:message name="UnsubscribeProgramRequest">
    <wsdl:part name="body" element="tns:UnsubscribeProgramRequest"/>
  </wsdl:message>
  <wsdl:message name="UnsubscribeProgramResponse">
    <wsdl:part name="body" element="tns:UnsubscribeProgramResponse"/>
  </wsdl:message>
  <wsdl:message name="InvestmentRecommendationRequest">
    <wsdl:part name="body" element="tns:InvestmentRecommendationRequest"/>
  </wsdl:message>
  <wsdl:message name="InvestmentRecommendationResponse">
    <wsdl:part name="body" element="tns:InvestmentRecommendationResponse"/>
  </wsdl:message>
  <wsdl:message name="FundsListRequest">
    <wsdl:part name="body" element="tns:FundsListRequest"/>
  </wsdl:message>
  <wsdl:message name="FundsListResponse">
    <wsdl:part name="body" element="tns:FundsListResponse"/>
  </wsdl:message>
  <wsdl:message name="InvestmentFundTradeDatesRequest">
    <wsdl:part name="body" element="tns:InvestmentFundTradeDatesRequest"/>
  </wsdl:message>
  <wsdl:message name="InvestmentFundTradeDatesResponse">
    <wsdl:part name="body" element="tns:InvestmentFundTradeDatesResponse"/>
  </wsdl:message>
  <wsdl:message name="InvestmentFundTermsAndConditionsRequest">
    <wsdl:part name="body" element="tns:InvestmentFundTermsAndConditionsRequest"/>
  </wsdl:message>
  <wsdl:message name="InvestmentFundTermsAndConditionsResponse">
    <wsdl:part name="body" element="tns:InvestmentFundTermsAndConditionsResponse"/>
  </wsdl:message>
  <wsdl:message name="OrderRequest">
    <wsdl:part name="body" element="tns:OrderRequest"/>
  </wsdl:message>
  <wsdl:message name="OrderResponse">
    <wsdl:part name="body" element="tns:OrderResponse"/>
  </wsdl:message>
  <wsdl:message name="PortfolioDetailsRequest">
    <wsdl:part name="body" element="tns:PortfolioDetailsRequest"/>
  </wsdl:message>
  <wsdl:message name="PortfolioDetailsResponse">
    <wsdl:part name="body" element="tns:PortfolioDetailsResponse"/>
  </wsdl:message>
  <wsdl:message name="RISAddSubscriptionRequest">
    <wsdl:part name="body" element="tns:RISAddSubscriptionRequest"/>
  </wsdl:message>
  <wsdl:message name="RISAddSubscriptionResponse">
    <wsdl:part name="body" element="tns:RISAddSubscriptionResponse"/>
  </wsdl:message>
  <wsdl:message name="RISCancelSubscriptionRequest">
    <wsdl:part name="body" element="tns:RISCancelSubscriptionRequest"/>
  </wsdl:message>
  <wsdl:message name="RISCancelSubscriptionResponse">
    <wsdl:part name="body" element="tns:RISCancelSubscriptionResponse"/>
  </wsdl:message>
  <wsdl:message name="TermsAndConditionsRequest">
    <wsdl:part name="body" element="tns:TermsAndConditionsRequest"/>
  </wsdl:message>
  <wsdl:message name="TermsAndConditionsResponse">
    <wsdl:part name="body" element="tns:TermsAndConditionsResponse"/>
  </wsdl:message>
  <wsdl:message name="RISUpdateSubscriptionRequest">
    <wsdl:part name="body" element="tns:RISUpdateSubscriptionRequest"/>
  </wsdl:message>
  <wsdl:message name="RISUpdateSubscriptionResponse">
    <wsdl:part name="body" element="tns:RISUpdateSubscriptionResponse"/>
  </wsdl:message>
  <wsdl:message name="RISStandingOrderEnquiryRequest">
    <wsdl:part name="body" element="tns:RISStandingOrderEnquiryRequest"/>
  </wsdl:message>
  <wsdl:message name="RISStandingOrderEnquiryResponse">
    <wsdl:part name="body" element="tns:RISStandingOrderEnquiryResponse"/>
  </wsdl:message>
  <wsdl:message name="FundInformationRequest">
    <wsdl:part name="body" element="tns:FundInformationRequest"/>
  </wsdl:message>
  <wsdl:message name="FundInformationResponse">
    <wsdl:part name="body" element="tns:FundInformationResponse"/>
  </wsdl:message>
  <wsdl:message name="GetSharePriceRequest">
    <wsdl:part name="parameters" element="tns:GetSharePriceRequest"/>
  </wsdl:message>
  <wsdl:message name="GetSharePriceResponse">
    <wsdl:part name="parameters" element="tns:GetSharePriceResponse"/>
  </wsdl:message>
  <wsdl:message name="GetExecutionDetailsRequest">
    <wsdl:part name="parameters" element="tns:GetExecutionDetailsRequest"/>
  </wsdl:message>
  <wsdl:message name="GetExecutionDetailsResponse">
    <wsdl:part name="parameters" element="tns:GetExecutionDetailsResponse"/>
  </wsdl:message>
  <wsdl:message name="GetPortfolioAccountRequest">
    <wsdl:part name="body" element="tns:GetPortfolioAccountRequest"/>
  </wsdl:message>
  <wsdl:message name="GetPortfolioAccountResponse">
    <wsdl:part name="body" element="tns:GetPortfolioAccountResponse"/>
  </wsdl:message>
  <wsdl:message name="InvestmentAccountProfileRequest">
    <wsdl:part name="body" element="tns:InvestmentAccountProfileRequest"/>
  </wsdl:message>
  <wsdl:message name="InvestmentAccountProfileResponse">
    <wsdl:part name="body" element="tns:InvestmentAccountProfileResponse"/>
  </wsdl:message>
  <wsdl:message name="InvestmentFundDetailsRequest">
    <wsdl:part name="body" element="tns:InvestmentFundDetailsRequest"/>
  </wsdl:message>
  <wsdl:message name="InvestmentFundDetailsResponse">
    <wsdl:part name="body" element="tns:InvestmentFundDetailsResponse"/>
  </wsdl:message>
  <wsdl:message name="InvestmentFundPriceRequest">
    <wsdl:part name="body" element="tns:InvestmentFundPriceRequest"/>
  </wsdl:message>
  <wsdl:message name="InvestmentFundPriceResponse">
    <wsdl:part name="body" element="tns:InvestmentFundPriceResponse"/>
  </wsdl:message>
  <wsdl:message name="CreateInvestmentAccountRequest">
    <wsdl:part name="body" element="tns:CreateInvestmentAccountRequest"/>
  </wsdl:message>
  <wsdl:message name="CreateInvestmentAccountResponse">
    <wsdl:part name="body" element="tns:CreateInvestmentAccountResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateNCBCCustomerRequest">
    <wsdl:part name="body" element="tns:UpdateNCBCCustomerRequest"/>
  </wsdl:message>
  <wsdl:message name="UpdateNCBCCustomerResponse">
    <wsdl:part name="body" element="tns:UpdateNCBCCustomerResponse"/>
  </wsdl:message>
  <wsdl:message name="GetShareNameRequest">
    <wsdl:part name="body" element="tns1:GetShareNameRequest"/>
  </wsdl:message>
  <wsdl:message name="GetShareNameResponse">
    <wsdl:part name="body" element="tns1:GetShareNameResponse"/>
  </wsdl:message>
  <wsdl:message name="TradableRightsSubscriptionRequest">
    <wsdl:part name="body" element="tns1:TradableRightsSubscriptionRequest"/>
  </wsdl:message>
  <wsdl:message name="TradableRightsSubscriptionResponse">
    <wsdl:part name="body" element="tns1:TradableRightsSubscriptionResponse"/>
  </wsdl:message>
  <wsdl:message name="TradableRightsEligibilityRequest">
    <wsdl:part name="body" element="tns1:TradableRightsEligibilityRequest"/>
  </wsdl:message>
  <wsdl:message name="TradableRightsEligibilityResponse">
    <wsdl:part name="body" element="tns1:TradableRightsEligibilityResponse"/>
  </wsdl:message>
  <wsdl:message name="GetShareDetailsRequest">
    <wsdl:part name="body" element="tns1:GetShareDetailsRequest"/>
  </wsdl:message>
  <wsdl:message name="GetShareDetailsResponse">
    <wsdl:part name="body" element="tns1:GetShareDetailsResponse"/>
  </wsdl:message>
  <wsdl:message name="GetSectorNameRequest">
    <wsdl:part name="body" element="tns1:GetSectorNameRequest"/>
  </wsdl:message>
  <wsdl:message name="GetSectorNameResponse">
    <wsdl:part name="body" element="tns1:GetSectorNameResponse"/>
  </wsdl:message>
  <wsdl:message name="PortfolioSearchRequest">
    <wsdl:part name="body" element="tns1:PortfolioSearchRequest"/>
  </wsdl:message>
  <wsdl:message name="PortfolioSearchResponse">
    <wsdl:part name="body" element="tns1:PortfolioSearchResponse"/>
  </wsdl:message>
  <wsdl:message name="PortfolioHoldingsRequest">
    <wsdl:part name="body" element="tns1:PortfolioHoldingsRequest"/>
  </wsdl:message>
  <wsdl:message name="PortfolioHoldingsResponse">
    <wsdl:part name="body" element="tns1:PortfolioHoldingsResponse"/>
  </wsdl:message>
  <wsdl:message name="GetBuyingPowerDetailsRequest">
    <wsdl:part name="body" element="tns1:GetBuyingPowerDetailsRequest"/>
  </wsdl:message>
  <wsdl:message name="GetBuyingPowerDetailsResponse">
    <wsdl:part name="body" element="tns1:GetBuyingPowerDetailsResponse"/>
  </wsdl:message>
  <wsdl:message name="GetMarketOverviewDetailsRequest">
    <wsdl:part name="body" element="tns1:GetMarketOverviewDetailsRequest"/>
  </wsdl:message>
  <wsdl:message name="GetMarketOverviewDetailsResponse">
    <wsdl:part name="body" element="tns1:GetMarketOverviewDetailsResponse"/>
  </wsdl:message>
  <wsdl:message name="GetLocalSharesStatementRequest">
    <wsdl:part name="body" element="tns1:GetLocalSharesStatementRequest"/>
  </wsdl:message>
  <wsdl:message name="GetLocalSharesStatementResponse">
    <wsdl:part name="body" element="tns1:GetLocalSharesStatementResponse"/>
  </wsdl:message>
  <wsdl:message name="CreatePortfolioAccountRequest">
    <wsdl:part name="body" element="tns1:CreatePortfolioAccountRequest"/>
  </wsdl:message>
  <wsdl:message name="CreatePortfolioAccountResponse">
    <wsdl:part name="body" element="tns1:CreatePortfolioAccountResponse"/>
  </wsdl:message>
  <wsdl:message name="InvestmentAccountTransactionsRequest">
    <wsdl:part name="body" element="tns:InvestmentAccountTransactionsRequest"/>
  </wsdl:message>
  <wsdl:message name="InvestmentAccountTransactionsResponse">
    <wsdl:part name="body" element="tns:InvestmentAccountTransactionsResponse"/>
  </wsdl:message>
  <wsdl:message name="DeleteOrderRequest">
    <wsdl:part name="body" element="tns:DeleteOrderRequest"/>
  </wsdl:message>
  <wsdl:message name="DeleteOrderResponse">
    <wsdl:part name="body" element="tns:DeleteOrderResponse"/>
  </wsdl:message>
  <wsdl:message name="ChangeOrderRequest">
    <wsdl:part name="body" element="tns:ChangeOrderRequest"/>
  </wsdl:message>
  <wsdl:message name="ChangeOrderResponse">
    <wsdl:part name="body" element="tns:ChangeOrderResponse"/>
  </wsdl:message>
  <wsdl:message name="CreateInvestmentStatementRequest">
    <wsdl:part name="body" element="tns:CreateInvestmentStatementRequest"/>
  </wsdl:message>
  <wsdl:message name="CreateInvestmentStatementResponse">
    <wsdl:part name="body" element="tns:CreateInvestmentStatementResponse"/>
  </wsdl:message>
  <wsdl:message name="GetTadawulUserRequest">
    <wsdl:part name="body" element="tns1:GetTadawulUserRequest"/>
  </wsdl:message>
  <wsdl:message name="GetTadawulUserResponse">
    <wsdl:part name="body" element="tns1:GetTadawulUserResponse"/>
  </wsdl:message>
  <wsdl:message name="ActivateTadawulUserRequest">
    <wsdl:part name="body" element="tns1:ActivateTadawulUserRequest"/>
  </wsdl:message>
  <wsdl:message name="ActivateTadawulUserResponse">
    <wsdl:part name="body" element="tns1:ActivateTadawulUserResponse"/>
  </wsdl:message>
  <wsdl:message name="GetPendingFundTransactionRequest">
    <wsdl:part name="body" element="tns:GetPendingFundTransactionRequest"/>
  </wsdl:message>
  <wsdl:message name="GetPendingFundTransactionResponse">
    <wsdl:part name="body" element="tns:GetPendingFundTransactionResponse"/>
  </wsdl:message>
  <wsdl:message name="ChargeCustomerRequest">
    <wsdl:part name="body" element="tns1:ChargeCustomerRequest"/>
  </wsdl:message>
  <wsdl:message name="ChargeCustomerResponse">
    <wsdl:part name="body" element="tns1:ChargeCustomerResponse"/>
  </wsdl:message>
  <wsdl:message name="GetOrderHistoryRequest">
    <wsdl:part name="body" element="tns:GetOrderHistoryRequest"/>
  </wsdl:message>
  <wsdl:message name="GetOrderHistoryResponse">
    <wsdl:part name="body" element="tns:GetOrderHistoryResponse"/>
  </wsdl:message>
  <wsdl:message name="GetInvestmentFundProfileRequest">
    <wsdl:part name="body" element="tns:GetInvestmentFundProfileRequest"/>
  </wsdl:message>
  <wsdl:message name="GetInvestmentFundProfileResponse">
    <wsdl:part name="body" element="tns:GetInvestmentFundProfileResponse"/>
  </wsdl:message>
  <wsdl:message name="ChangeInvestmentAddressRequest">
    <wsdl:part name="body" element="tns:ChangeInvestmentAddressRequest"/>
  </wsdl:message>
  <wsdl:message name="ChangeInvestmentAddressResponse">
    <wsdl:part name="body" element="tns:ChangeInvestmentAddressResponse"/>
  </wsdl:message>
  <wsdl:message name="CreateCustomerRequest">
    <wsdl:part name="body" element="tns:CreateCustomerRequest"/>
  </wsdl:message>
  <wsdl:message name="CreateCustomerResponse">
    <wsdl:part name="body" element="tns:CreateCustomerResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateCustomerRequest">
    <wsdl:part name="body" element="tns:UpdateCustomerRequest"/>
  </wsdl:message>
  <wsdl:message name="UpdateCustomerResponse">
    <wsdl:part name="body" element="tns:UpdateCustomerResponse"/>
  </wsdl:message>
  <wsdl:message name="OpenIntegratedAccountRequest">
    <wsdl:part name="body" element="tns:OpenIntegratedAccountRequest"/>
  </wsdl:message>
  <wsdl:message name="OpenIntegratedAccountResponse">
    <wsdl:part name="body" element="tns:OpenIntegratedAccountResponse"/>
  </wsdl:message>
  <wsdl:message name="CashAccountDetailsRequest">
    <wsdl:part name="body" element="tns1:CashAccountDetailsRequest"/>
  </wsdl:message>
  <wsdl:message name="CashAccountDetailsResponse">
    <wsdl:part name="body" element="tns1:CashAccountDetailsResponse"/>
  </wsdl:message>
  <wsdl:message name="CustomerPortfolioRegistrationRequest">
    <wsdl:part name="body" element="tns:CustomerPortfolioRegistrationRequest"/>
  </wsdl:message>
  <wsdl:message name="CustomerPortfolioRegistrationResponse">
    <wsdl:part name="body" element="tns:CustomerPortfolioRegistrationResponse"/>
  </wsdl:message>
  <wsdl:message name="InquireCashBlockRequest">
    <wsdl:part name="body" element="tns1:InquireCashBlockRequest"/>
  </wsdl:message>
  <wsdl:message name="InquireCashBlockResponse">
    <wsdl:part name="body" element="tns1:InquireCashBlockResponse"/>
  </wsdl:message>
  <wsdl:message name="InquireInvestmentProfileRequest">
    <wsdl:part name="body" element="tns1:InquireInvestmentProfileRequest"/>
  </wsdl:message>
  <wsdl:message name="InquireInvestmentProfileResponse">
    <wsdl:part name="body" element="tns1:InquireInvestmentProfileResponse"/>
  </wsdl:message>
  <wsdl:message name="CashUnblockRequest">
    <wsdl:part name="body" element="tns1:CashUnblockRequest"/>
  </wsdl:message>
  <wsdl:message name="CashUnblockResponse">
    <wsdl:part name="body" element="tns1:CashUnblockResponse"/>
  </wsdl:message>
  <wsdl:message name="CashBlockRequest">
    <wsdl:part name="body" element="tns1:CashBlockRequest"/>
  </wsdl:message>
  <wsdl:message name="CashBlockResponse">
    <wsdl:part name="body" element="tns1:CashBlockResponse"/>
  </wsdl:message>
  <wsdl:message name="PortfolioListRequest">
    <wsdl:part name="body" element="tns1:PortfolioListRequest"/>
  </wsdl:message>
  <wsdl:message name="PortfolioListResponse">
    <wsdl:part name="body" element="tns1:PortfolioListResponse"/>
  </wsdl:message>
  <wsdl:message name="GetPortfolioDetailsRequest">
    <wsdl:part name="body" element="tns1:GetPortfolioDetailsRequest"/>
  </wsdl:message>
  <wsdl:message name="GetPortfolioDetailsResponse">
    <wsdl:part name="body" element="tns1:GetPortfolioDetailsResponse"/>
  </wsdl:message>
  <wsdl:message name="IVRTermsAndConditionsRequest">
    <wsdl:part name="body" element="tns:IVRTermsAndConditionsRequest"/>
  </wsdl:message>
  <wsdl:message name="IVRTermsAndConditionsResponse">
    <wsdl:part name="body" element="tns:IVRTermsAndConditionsResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateOccupationListRequest">
    <wsdl:part name="body" element="tns:UpdateOccupationListRequest"/>
  </wsdl:message>
  <wsdl:message name="UpdateOccupationListResponse">
    <wsdl:part name="body" element="tns:UpdateOccupationListResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateSharesPledgingRequest">
    <wsdl:part name="body" element="tns:UpdateSharesPledgingRequest"/>
  </wsdl:message>
  <wsdl:message name="UpdateSharesPledgingResponse">
    <wsdl:part name="body" element="tns:UpdateSharesPledgingResponse"/>
  </wsdl:message>
  <wsdl:message name="InquireSharePledgeDetailsRequest">
    <wsdl:part name="body" element="tns:InquireSharePledgeDetailsRequest"/>
  </wsdl:message>
  <wsdl:message name="InquireSharePledgeDetailsResponse">
    <wsdl:part name="body" element="tns:InquireSharePledgeDetailsResponse"/>
  </wsdl:message>
  <wsdl:message name="GetTreasuryCurrencyRequest">
    <wsdl:part name="body" element="tns1:GetTreasuryCurrencyRequest"/>
  </wsdl:message>
  <wsdl:message name="GetTreasuryCurrencyResponse">
    <wsdl:part name="body" element="tns1:GetTreasuryCurrencyResponse"/>
  </wsdl:message>
  <wsdl:message name="GetClientPortfolioSummaryRequest">
    <wsdl:part name="body" element="tns1:GetClientPortfolioSummaryRequest"/>
  </wsdl:message>
  <wsdl:message name="GetClientPortfolioSummaryResponse">
    <wsdl:part name="body" element="tns1:GetClientPortfolioSummaryResponse"/>
  </wsdl:message>
  <wsdl:message name="GetVolumeByProductRequest">
    <wsdl:part name="body" element="tns1:GetVolumeByProductRequest"/>
  </wsdl:message>
  <wsdl:message name="GetVolumeByProductResponse">
    <wsdl:part name="body" element="tns1:GetVolumeByProductResponse"/>
  </wsdl:message>
  <wsdl:message name="GetCrossSellBenchmarkRequest">
    <wsdl:part name="body" element="tns1:GetCrossSellBenchmarkRequest"/>
  </wsdl:message>
  <wsdl:message name="GetCrossSellBenchmarkResponse">
    <wsdl:part name="body" element="tns1:GetCrossSellBenchmarkResponse"/>
  </wsdl:message>
  <wsdl:message name="InquireCPALinkedAccountListRequest">
    <wsdl:part name="body" element="tns1:InquireCPALinkedAccountListRequest"/>
  </wsdl:message>
  <wsdl:message name="InquireCPALinkedAccountListResponse">
    <wsdl:part name="body" element="tns1:InquireCPALinkedAccountListResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateAccountLinkedToCPARequest">
    <wsdl:part name="body" element="tns1:UpdateAccountLinkedToCPARequest"/>
  </wsdl:message>
  <wsdl:message name="UpdateAccountLinkedToCPAResponse">
    <wsdl:part name="body" element="tns1:UpdateAccountLinkedToCPAResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateCustomerCommunicationDataRequest">
    <wsdl:part name="body" element="tns1:UpdateCustomerCommunicationDataRequest"/>
  </wsdl:message>
  <wsdl:message name="UpdateCustomerCommunicationDataResponse">
    <wsdl:part name="body" element="tns1:UpdateCustomerCommunicationDataResponse"/>
  </wsdl:message>
  <wsdl:message name="NCBCRiskProfileRequest">
    <wsdl:part name="body" element="tns:NCBCRiskProfileRequest"/>
  </wsdl:message>
  <wsdl:message name="NCBCRiskProfileResponse">
    <wsdl:part name="body" element="tns:NCBCRiskProfileResponse"/>
  </wsdl:message>
  <wsdl:message name="ClassificationQuestionnaireRequest">
    <wsdl:part name="body" element="tns:ClassificationQuestionnaireRequest"/>
  </wsdl:message>
  <wsdl:message name="ClassificationQuestionnaireResponse">
    <wsdl:part name="body" element="tns:ClassificationQuestionnaireResponse"/>
  </wsdl:message>
  <wsdl:message name="RiskQuestionnaireRequest">
    <wsdl:part name="body" element="tns:RiskQuestionnaireRequest"/>
  </wsdl:message>
  <wsdl:message name="RiskQuestionnaireResponse">
    <wsdl:part name="body" element="tns:RiskQuestionnaireResponse"/>
  </wsdl:message>
  <wsdl:message name="CustomerClassificationRequest">
    <wsdl:part name="body" element="tns:CustomerClassificationRequest"/>
  </wsdl:message>
  <wsdl:message name="CustomerClassificationResponse">
    <wsdl:part name="body" element="tns:CustomerClassificationResponse"/>
  </wsdl:message>
  <wsdl:message name="CustomerRiskProfileRequest">
    <wsdl:part name="body" element="tns:CustomerRiskProfileRequest"/>
  </wsdl:message>
  <wsdl:message name="CustomerRiskProfileResponse">
    <wsdl:part name="body" element="tns:CustomerRiskProfileResponse"/>
  </wsdl:message>
  <wsdl:message name="GetCustomerClassificationAndRiskProfileRequest">
    <wsdl:part name="body" element="tns:GetCustomerClassificationAndRiskProfileRequest"/>
  </wsdl:message>
  <wsdl:message name="GetCustomerClassificationAndRiskProfileResponse">
    <wsdl:part name="body" element="tns:GetCustomerClassificationAndRiskProfileResponse"/>
  </wsdl:message>
  <wsdl:message name="RetrieveEmailAndMobileRequest">
    <wsdl:part name="body" element="tns1:RetrieveEmailAndMobileRequest"/>
  </wsdl:message>
  <wsdl:message name="RetrieveEmailAndMobileResponse">
    <wsdl:part name="body" element="tns1:RetrieveEmailAndMobileResponse"/>
  </wsdl:message>
  <wsdl:message name="CheckCustomerComplianceRequest">
    <wsdl:part name="body" element="tns:CheckCustomerComplianceRequest"/>
  </wsdl:message>
  <wsdl:message name="CheckCustomerComplianceResponse">
    <wsdl:part name="body" element="tns:CheckCustomerComplianceResponse"/>
  </wsdl:message>
  <wsdl:portType name="InvestmentService">
    <wsdl:operation name="GetInvestmentAccountDetails">
      <wsdl:input message="tns:AccountDetailsRequest"/>
      <wsdl:output message="tns:AccountDetailsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentBalanceProfile">
      <wsdl:input message="tns:BalanceProfileRequest"/>
      <wsdl:output message="tns:BalanceProfileResponse"/>
    </wsdl:operation>
    <wsdl:operation name="CancelInvestmentFundTrade">
      <wsdl:input message="tns:CancelFundTradeRequest"/>
      <wsdl:output message="tns:CancelFundTradeResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentFundPendingTrade">
      <wsdl:input message="tns:FundPendingTradeRequest"/>
      <wsdl:output message="tns:FundPendingTradeResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentFundTransactions">
      <wsdl:input message="tns:InvestmentFundTransactionsRequest"/>
      <wsdl:output message="tns:InvestmentFundTransactionsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentQuestioners">
      <wsdl:input message="tns:InvestmentQuestionersRequest"/>
      <wsdl:output message="tns:InvestmentQuestionersResponse"/>
    </wsdl:operation>
    <wsdl:operation name="RedeemInvestmentFund">
      <wsdl:input message="tns:RedeemInvestmentFundRequest"/>
      <wsdl:output message="tns:RedeemInvestmentFundResponse"/>
    </wsdl:operation>
    <wsdl:operation name="SwitchInvestmentFund">
      <wsdl:input message="tns:SwitchInvestmentFundRequest"/>
      <wsdl:output message="tns:SwitchInvestmentFundResponse"/>
    </wsdl:operation>
    <wsdl:operation name="SubscribeInvestment">
      <wsdl:input message="tns:InvestmentSubscriptionRequest"/>
      <wsdl:output message="tns:InvestmentSubscriptionResponse"/>
    </wsdl:operation>
    <wsdl:operation name="SubscribeProgram">
      <wsdl:input message="tns:ProgramSubscriptionRequest"/>
      <wsdl:output message="tns:ProgramSubscriptionResponse"/>
    </wsdl:operation>
    <wsdl:operation name="UnsubscribeProgram">
      <wsdl:input message="tns:UnsubscribeProgramRequest"/>
      <wsdl:output message="tns:UnsubscribeProgramResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentRecommendation">
      <wsdl:input message="tns:InvestmentRecommendationRequest"/>
      <wsdl:output message="tns:InvestmentRecommendationResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetFundsList">
      <wsdl:input message="tns:FundsListRequest"/>
      <wsdl:output message="tns:FundsListResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentFundTradeDates">
      <wsdl:input message="tns:InvestmentFundTradeDatesRequest"/>
      <wsdl:output message="tns:InvestmentFundTradeDatesResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentFundTermsAndConditions">
      <wsdl:input message="tns:InvestmentFundTermsAndConditionsRequest"/>
      <wsdl:output message="tns:InvestmentFundTermsAndConditionsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="Order">
      <wsdl:input message="tns:OrderRequest"/>
      <wsdl:output message="tns:OrderResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetPortfolioDetails">
      <wsdl:input message="tns:PortfolioDetailsRequest"/>
      <wsdl:output message="tns:PortfolioDetailsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="AddThimarSubscription">
      <wsdl:input message="tns:RISAddSubscriptionRequest"/>
      <wsdl:output message="tns:RISAddSubscriptionResponse"/>
    </wsdl:operation>
    <wsdl:operation name="CancelThimarSubscription">
      <wsdl:input message="tns:RISCancelSubscriptionRequest"/>
      <wsdl:output message="tns:RISCancelSubscriptionResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetThimarTC">
      <wsdl:input message="tns:TermsAndConditionsRequest"/>
      <wsdl:output message="tns:TermsAndConditionsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateThimarSubscription">
      <wsdl:input message="tns:RISUpdateSubscriptionRequest"/>
      <wsdl:output message="tns:RISUpdateSubscriptionResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetThimarSubscriptiondetails">
      <wsdl:input message="tns:RISStandingOrderEnquiryRequest"/>
      <wsdl:output message="tns:RISStandingOrderEnquiryResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetFundInformation">
      <wsdl:input message="tns:FundInformationRequest"/>
      <wsdl:output message="tns:FundInformationResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetSharePrice">
      <wsdl:input message="tns:GetSharePriceRequest"/>
      <wsdl:output message="tns:GetSharePriceResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetExecutionDetails">
      <wsdl:input message="tns:GetExecutionDetailsRequest"/>
      <wsdl:output message="tns:GetExecutionDetailsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetPortfolioAccount">
      <wsdl:input message="tns:GetPortfolioAccountRequest"/>
      <wsdl:output message="tns:GetPortfolioAccountResponse"/>
    </wsdl:operation>
    <wsdl:operation name="InvestmentAccountProfile">
      <wsdl:input message="tns:InvestmentAccountProfileRequest"/>
      <wsdl:output message="tns:InvestmentAccountProfileResponse"/>
    </wsdl:operation>
    <wsdl:operation name="InvestmentFundDetails">
      <wsdl:input message="tns:InvestmentFundDetailsRequest"/>
      <wsdl:output message="tns:InvestmentFundDetailsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="InvestmentFundPrice">
      <wsdl:input message="tns:InvestmentFundPriceRequest"/>
      <wsdl:output message="tns:InvestmentFundPriceResponse"/>
    </wsdl:operation>
    <wsdl:operation name="CreateInvestmentAccount">
      <wsdl:documentation>Investment fund account create</wsdl:documentation>
      <wsdl:input message="tns:CreateInvestmentAccountRequest"/>
      <wsdl:output message="tns:CreateInvestmentAccountResponse"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateNCBCCustomer">
      <wsdl:documentation>Create or update NCBC Customer</wsdl:documentation>
      <wsdl:input message="tns:UpdateNCBCCustomerRequest"/>
      <wsdl:output message="tns:UpdateNCBCCustomerResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentAccountTransactions">
      <wsdl:input message="tns:InvestmentAccountTransactionsRequest"/>
      <wsdl:output message="tns:InvestmentAccountTransactionsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="DeleteOrder">
      <wsdl:input message="tns:DeleteOrderRequest"/>
      <wsdl:output message="tns:DeleteOrderResponse"/>
    </wsdl:operation>
    <wsdl:operation name="ChangeOrder">
      <wsdl:input message="tns:ChangeOrderRequest"/>
      <wsdl:output message="tns:ChangeOrderResponse"/>
    </wsdl:operation>
    <wsdl:operation name="CreateInvestmentStatement">
      <wsdl:input message="tns:CreateInvestmentStatementRequest"/>
      <wsdl:output message="tns:CreateInvestmentStatementResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetPendingFundTransaction">
      <wsdl:input message="tns:GetPendingFundTransactionRequest"/>
      <wsdl:output message="tns:GetPendingFundTransactionResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetOrderHistory">
      <wsdl:input message="tns:GetOrderHistoryRequest"/>
      <wsdl:output message="tns:GetOrderHistoryResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentFundProfile">
      <wsdl:input message="tns:GetInvestmentFundProfileRequest"/>
      <wsdl:output message="tns:GetInvestmentFundProfileResponse"/>
    </wsdl:operation>
    <wsdl:operation name="ChangeInvestmentAddress">
      <wsdl:input message="tns:ChangeInvestmentAddressRequest"/>
      <wsdl:output message="tns:ChangeInvestmentAddressResponse"/>
    </wsdl:operation>
    <wsdl:operation name="CreateCustomer">
      <wsdl:input message="tns:CreateCustomerRequest"/>
      <wsdl:output message="tns:CreateCustomerResponse"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateCustomer">
      <wsdl:input message="tns:UpdateCustomerRequest"/>
      <wsdl:output message="tns:UpdateCustomerResponse"/>
    </wsdl:operation>
    <wsdl:operation name="OpenIntegratedAccount">
      <wsdl:input message="tns:OpenIntegratedAccountRequest"/>
      <wsdl:output message="tns:OpenIntegratedAccountResponse"/>
    </wsdl:operation>
    <wsdl:operation name="IVRTermsAndConditions">
      <wsdl:input message="tns:IVRTermsAndConditionsRequest"/>
      <wsdl:output message="tns:IVRTermsAndConditionsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateOccupationList">
      <wsdl:input message="tns:UpdateOccupationListRequest"/>
      <wsdl:output message="tns:UpdateOccupationListResponse"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateSharesPledging">
      <wsdl:input message="tns:UpdateSharesPledgingRequest"/>
      <wsdl:output message="tns:UpdateSharesPledgingResponse"/>
    </wsdl:operation>
    <wsdl:operation name="InquireSharePledgeDetails">
      <wsdl:input message="tns:InquireSharePledgeDetailsRequest"/>
      <wsdl:output message="tns:InquireSharePledgeDetailsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetNCBCRiskProfile">
      <wsdl:input message="tns:NCBCRiskProfileRequest"/>
      <wsdl:output message="tns:NCBCRiskProfileResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetClassificationQuestionnaire">
      <wsdl:input message="tns:ClassificationQuestionnaireRequest"/>
      <wsdl:output message="tns:ClassificationQuestionnaireResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetRiskQuestionnaire">
      <wsdl:input message="tns:RiskQuestionnaireRequest"/>
      <wsdl:output message="tns:RiskQuestionnaireResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetCustomerClassification">
      <wsdl:input message="tns:CustomerClassificationRequest"/>
      <wsdl:output message="tns:CustomerClassificationResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetCustomerRiskProfile">
      <wsdl:input message="tns:CustomerRiskProfileRequest"/>
      <wsdl:output message="tns:CustomerRiskProfileResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetCustomerClassificationAndRiskProfile">
      <wsdl:documentation>Calculate the Risk Score Based on the Classification/Risk answer</wsdl:documentation>
      <wsdl:input message="tns:GetCustomerClassificationAndRiskProfileRequest"/>
      <wsdl:output message="tns:GetCustomerClassificationAndRiskProfileResponse"/>
    </wsdl:operation>
    <wsdl:operation name="CheckCustomerCompliance">
      <wsdl:documentation>Check for thr BlackListed Customer before OnBoarding to SNBC</wsdl:documentation>
      <wsdl:input message="tns:CheckCustomerComplianceRequest"/>
      <wsdl:output message="tns:CheckCustomerComplianceResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="PortfolioService">
    <wsdl:operation name="GetShareName">
      <xsd:annotation>
        <xsd:documentation>Purpose of this service is to Provide with the
					share name in English and Arabic.</xsd:documentation>
      </xsd:annotation>
      <wsdl:input message="tns:GetShareNameRequest"/>
      <wsdl:output message="tns:GetShareNameResponse"/>
    </wsdl:operation>
    <wsdl:operation name="SubscribeTradableRights">
      <xsd:annotation>
        <xsd:documentation>Purpose of this service is to subscribe to Tradable Rights</xsd:documentation>
      </xsd:annotation>
      <wsdl:input message="tns:TradableRightsSubscriptionRequest"/>
      <wsdl:output message="tns:TradableRightsSubscriptionResponse"/>
    </wsdl:operation>
    <wsdl:operation name="CheckTradableRightsEligibility">
      <xsd:annotation>
        <xsd:documentation>Purpose of this service is to Provide the Tradable rights eligibility</xsd:documentation>
      </xsd:annotation>
      <wsdl:input message="tns:TradableRightsEligibilityRequest"/>
      <wsdl:output message="tns:TradableRightsEligibilityResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetShareDetails">
      <xsd:annotation>
        <xsd:documentation>Purpose of this service is to Provide with the
					stock information details .</xsd:documentation>
      </xsd:annotation>
      <wsdl:input message="tns:GetShareDetailsRequest"/>
      <wsdl:output message="tns:GetShareDetailsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetSectorName">
      <xsd:annotation>
        <xsd:documentation>Purpose of this service is to To provide the
					Sector Name details.</xsd:documentation>
      </xsd:annotation>
      <wsdl:input message="tns:GetSectorNameRequest"/>
      <wsdl:output message="tns:GetSectorNameResponse"/>
    </wsdl:operation>
    <wsdl:operation name="PortfolioSearch">
      <xsd:annotation>
        <xsd:documentation>To search portfolio as per the requirement.This
					request needs either portfolioNumber or id or
					accountNumber to be
					exist</xsd:documentation>
      </xsd:annotation>
      <wsdl:input message="tns:PortfolioSearchRequest"/>
      <wsdl:output message="tns:PortfolioSearchResponse"/>
    </wsdl:operation>
    <wsdl:operation name="PortfolioHoldings">
      <xsd:annotation>
        <xsd:documentation>Purpose of this service is to Returns the current
					holdings in a portfolio.</xsd:documentation>
      </xsd:annotation>
      <wsdl:input message="tns:PortfolioHoldingsRequest"/>
      <wsdl:output message="tns:PortfolioHoldingsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetBuyingPowerDetails">
      <xsd:annotation>
        <xsd:documentation>Purpose of this service is To provide the
					customers shares Buying Power</xsd:documentation>
      </xsd:annotation>
      <wsdl:input message="tns:GetBuyingPowerDetailsRequest"/>
      <wsdl:output message="tns:GetBuyingPowerDetailsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetMarketOverviewDetails">
      <xsd:annotation>
        <xsd:documentation>Purpose of this service is to return overall
					market indicators</xsd:documentation>
      </xsd:annotation>
      <wsdl:input message="tns:GetMarketOverviewDetailsRequest"/>
      <wsdl:output message="tns:GetMarketOverviewDetailsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetLocalSharesStatement">
      <xsd:annotation>
        <xsd:documentation>The purpose of this service is to return the
					local
					shares statement.</xsd:documentation>
      </xsd:annotation>
      <wsdl:input message="tns:GetLocalSharesStatementRequest"/>
      <wsdl:output message="tns:GetLocalSharesStatementResponse"/>
    </wsdl:operation>
    <wsdl:operation name="CreatePortfolioAccount">
      <wsdl:input message="tns:CreatePortfolioAccountRequest"/>
      <wsdl:output message="tns:CreatePortfolioAccountResponse"/>
    </wsdl:operation>
    <wsdl:operation name="ChargeCustomer">
      <wsdl:input message="tns:ChargeCustomerRequest"/>
      <wsdl:output message="tns:ChargeCustomerResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetTadawulUser">
      <wsdl:input message="tns:GetTadawulUserRequest"/>
      <wsdl:output message="tns:GetTadawulUserResponse"/>
    </wsdl:operation>
    <wsdl:operation name="ActivateTadawulUser">
      <wsdl:input message="tns:ActivateTadawulUserRequest"/>
      <wsdl:output message="tns:ActivateTadawulUserResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetCashAccountDetails">
      <wsdl:input message="tns:CashAccountDetailsRequest"/>
      <wsdl:output message="tns:CashAccountDetailsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="CustomerPortfolioRegistration">
      <wsdl:input message="tns:CustomerPortfolioRegistrationRequest"/>
      <wsdl:output message="tns:CustomerPortfolioRegistrationResponse"/>
    </wsdl:operation>
    <wsdl:operation name="InquireCashBlock">
      <wsdl:input message="tns:InquireCashBlockRequest"/>
      <wsdl:output message="tns:InquireCashBlockResponse"/>
    </wsdl:operation>
    <wsdl:operation name="InquireInvestmentProfile">
      <wsdl:input message="tns:InquireInvestmentProfileRequest"/>
      <wsdl:output message="tns:InquireInvestmentProfileResponse"/>
    </wsdl:operation>
    <wsdl:operation name="CashUnblock">
      <wsdl:input message="tns:CashUnblockRequest"/>
      <wsdl:output message="tns:CashUnblockResponse"/>
    </wsdl:operation>
    <wsdl:operation name="CashBlock">
      <wsdl:input message="tns:CashBlockRequest"/>
      <wsdl:output message="tns:CashBlockResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetPortfolioList">
      <wsdl:input message="tns:PortfolioListRequest"/>
      <wsdl:output message="tns:PortfolioListResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetPortfolioDetails">
      <wsdl:input message="tns:GetPortfolioDetailsRequest"/>
      <wsdl:output message="tns:GetPortfolioDetailsResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetTreasuryCurrency">
      <wsdl:documentation>>Fetches treasury currency based on rmPosition</wsdl:documentation>
      <wsdl:input message="tns:GetTreasuryCurrencyRequest"/>
      <wsdl:output message="tns:GetTreasuryCurrencyResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetClientPortfolioSummary">
      <wsdl:documentation>>Fetches Client Portfolio Summary</wsdl:documentation>
      <wsdl:input message="tns:GetClientPortfolioSummaryRequest"/>
      <wsdl:output message="tns:GetClientPortfolioSummaryResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetVolumeByProduct">
      <wsdl:documentation>Fetches volumes based categories cash market, lending, trade, treasury   to be shown monthly or quarterly</wsdl:documentation>
      <wsdl:input message="tns:GetVolumeByProductRequest"/>
      <wsdl:output message="tns:GetVolumeByProductResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetCrossSellBenchmark">
      <wsdl:documentation>: A user can search one CIF in his portfolio to view ratios for 3 categories (Trade, CM, Treasury)
				 and how it measures up against a group of similar companies plus how many companies are in the group</wsdl:documentation>
      <wsdl:input message="tns:GetCrossSellBenchmarkRequest"/>
      <wsdl:output message="tns:GetCrossSellBenchmarkResponse"/>
    </wsdl:operation>
    <wsdl:operation name="InquireCPALinkedAccountList">
      <wsdl:input message="tns:InquireCPALinkedAccountListRequest"/>
      <wsdl:output message="tns:InquireCPALinkedAccountListResponse"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateAccountLinkedToCPA">
      <wsdl:input message="tns:UpdateAccountLinkedToCPARequest"/>
      <wsdl:output message="tns:UpdateAccountLinkedToCPAResponse"/>
    </wsdl:operation>
    <wsdl:operation name="UpdateCustomerCommunicationData">
      <wsdl:input message="tns:UpdateCustomerCommunicationDataRequest"/>
      <wsdl:output message="tns:UpdateCustomerCommunicationDataResponse"/>
    </wsdl:operation>
    <wsdl:operation name="RetrieveEmailAndMobile">
      <wsdl:input message="tns:RetrieveEmailAndMobileRequest"/>
      <wsdl:output message="tns:RetrieveEmailAndMobileResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="InvestmentService" type="tns:InvestmentService">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="GetInvestmentAccountDetails">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/GetAccountDetails"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentBalanceProfile">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/GetBalanceProfile"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelInvestmentFundTrade">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/GetFundCancelTrade"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentFundPendingTrade">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/GetFundPendingTrade"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentFundTransactions">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/GetFundTransactions"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentQuestioners">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/GetQuestioners"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RedeemInvestmentFund">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/RedeemInvestmentFund"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SwitchInvestmentFund">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/SwitchInvestmentFund"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SubscribeInvestment">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/SubscribeInvestment"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SubscribeProgram">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/SubscribeProgram"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UnsubscribeProgram">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/UnsubscribeProgram"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentRecommendation">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/GetInvestmentRecommendation"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetFundsList">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/GetFundsList"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentFundTradeDates">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/GetInvestmentFundTradeDates"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentFundTermsAndConditions">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/ipo/1.0/GetInvestmentFundTermsAndConditions"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Order">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/Order"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetPortfolioDetails">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetPortfolioDetails"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddThimarSubscription">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/AddThimarSubscription"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelThimarSubscription">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/CancelThimarSubscription"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetThimarTC">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetThimarTC"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateThimarSubscription">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/UpdateThimarSubscription"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetThimarSubscriptiondetails">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetThimarSubscriptiondetails"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetFundInformation">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetFundInformation"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSharePrice">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetSharePrice"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetExecutionDetails">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetExecutionDetails"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetPortfolioAccount">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetPortfolioAccount"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InvestmentAccountProfile">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/InvestmentAccountProfile"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InvestmentFundDetails">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/InvestmentFundDetails"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InvestmentFundPrice">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/InvestmentFundPrice"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateInvestmentAccount">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/CreateInvestmentAccount"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateNCBCCustomer">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/UpdateNCBCCustomer"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentAccountTransactions">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/InvestmentAccountTransactions"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteOrder">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/DeleteOrder"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ChangeOrder">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/ChangeOrder"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateInvestmentStatement">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/CreateInvestmentStatement"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetPendingFundTransaction">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetPendingFundTransaction"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetOrderHistory">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetOrderHistory"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetInvestmentFundProfile">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetInvestmentFundProfile"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ChangeInvestmentAddress">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/ChangeInvestmentAddress"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateCustomer">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/CreateCustomer"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateCustomer">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/UpdateCustomer"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OpenIntegratedAccount">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/OpenIntegratedAccount"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IVRTermsAndConditions">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/IVRTermsAndConditions"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateOccupationList">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/UpdateOccupationList"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateSharesPledging">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/UpdateSharesPledging"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InquireSharePledgeDetails">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/InquireSharePledgeDetails"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetNCBCRiskProfile">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetNCBCRiskProfile"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetClassificationQuestionnaire">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetClassificationQuestionnaire"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetRiskQuestionnaire">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetRiskQuestionnaire"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCustomerClassification">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetCustomerClassification"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCustomerRiskProfile">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetCustomerRiskProfile"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCustomerClassificationAndRiskProfile">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetCustomerClassificationAndRiskProfile"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckCustomerCompliance">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/CheckCustomerCompliance"/>
      <wsdl:input>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="PortfolioService" type="tns:PortfolioService">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="GetShareName">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/GetShareName"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SubscribeTradableRights">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/SubscribeTradableRights"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckTradableRightsEligibility">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/CheckTradableRightsEligibility"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetShareDetails">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/GetShareDetails"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSectorName">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/GetSectorName"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PortfolioSearch">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/PortfolioSearch"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PortfolioHoldings">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/PortfolioHoldings"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetBuyingPowerDetails">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/GetBuyingPowerDetails"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMarketOverviewDetails">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/GetMarketOverviewDetails"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLocalSharesStatement">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/GetLocalSharesStatement"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreatePortfolioAccount">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/CreatePortfolioAccount"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ChargeCustomer">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/ChargeCustomer"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTadawulUser">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/GetTadawulUser"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ActivateTadawulUser">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/ActivateTadawulUser"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCashAccountDetails">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/GetCashAccountDetails"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CustomerPortfolioRegistration">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/CustomerPortfolioRegistration"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InquireCashBlock">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/InquireCashBlock"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InquireInvestmentProfile">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/InquireInvestmentProfile"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CashUnblock">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/CashUnblock"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CashBlock">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/CashBlock"/>
      <wsdl:input>
        <soap:body use="literal"/>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetPortfolioList">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetPortfolioList"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetPortfolioDetails">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetPortfolioDetails"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTreasuryCurrency">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetTreasuryCurrency"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetClientPortfolioSummary">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetClientPortfolioSummary"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetVolumeByProduct">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetVolumeByProduct"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCrossSellBenchmark">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/1.0/GetCrossSellBenchmark"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InquireCPALinkedAccountList">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/InquireCPALinkedAccountList"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateAccountLinkedToCPA">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/UpdateAccountLinkedToCPA"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateCustomerCommunicationData">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/UpdateCustomerCommunicationData"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RetrieveEmailAndMobile">
      <soap:operation soapAction="http://corp.alahli.com/middlewareservices/investment/portfolioservice/1.0/RetrieveEmailAndMobile"/>
      <wsdl:input>
        <soap:header message="tns:Security" part="header" use="literal"/>
        <soap:header message="tns:ServiceHeader" part="header" use="literal"/>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="InvestmentService">
    <wsdl:port binding="tns:InvestmentService" name="InvestmentService">
      <soap:address location="http://**********:8080/cordys/com.alahli.gateway.NCBGateway.wcp?organization=o=Business Services,cn=cordys,cn=defaultInst,o=alahli.com"/>
    </wsdl:port>
    <wsdl:port name="PortfolioService" binding="tns:PortfolioService">
      <soap:address location="http://**********:8080/cordys/com.alahli.gateway.NCBGateway.wcp?organization=o=Business Services,cn=cordys,cn=defaultInst,o=alahli.com"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>