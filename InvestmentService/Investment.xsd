<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema targetNamespace="http://corp.alahli.com/middlewareservices/investment/1.0/" elementFormDefault="qualified" xmlns:tns="http://corp.alahli.com/middlewareservices/investment/1.0/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:fault="http://corp.alahli.com/middlewareservices/fault/1.0/" xmlns:Q1="http://corp.alahli.com/schemas/middleware/temporaltypes/2012/10/29/">
  <xsd:import namespace="http://corp.alahli.com/middlewareservices/fault/1.0/" schemaLocation="fault.xsd"/>
  <xsd:import namespace="http://corp.alahli.com/schemas/middleware/temporaltypes/2012/10/29/" schemaLocation="schemas.xsd"/>
  <xsd:element name="InvestmentAccountProfileRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="cif" type="Q1:CIF" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Customer CIF Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentAccountProfileResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="record" type="tns:InvestmentAccountProfileType" maxOccurs="unbounded" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="InvestmentAccountProfileType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="accountId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>AccountId</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="balance" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Balance Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="status" type="xsd:int" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Status</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountType" type="Q1:AccountType" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Account Type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="branch" type="Q1:Branch" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Branch</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:all>
  </xsd:complexType>
  <xsd:element name="InvestmentFundDetailsRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="accountId" type="xsd:string" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Account Id</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fund" type="Q1:Fund" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Fund</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentFundDetailsResponse">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="success" type="tns:InvestmentFundDetailsType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="InvestmentFundDetailsType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="balance" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Balance Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="currency" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Currency</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="balanceSAR" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Balance in SAR Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="unitPrice" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Unit Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="units" type="xsd:double" maxOccurs="1" minOccurs="0" default="0.0">
        <xsd:annotation>
          <xsd:documentation>No of Units</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="valueDate" type="Q1:DateOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="openDate" type="Q1:DateOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Open Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blockAmount" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Block Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="exchangeRate" type="xsd:double" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Exchange Rate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="status" type="xsd:int" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Status</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="statusDescription" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Status Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountType" type="Q1:AccountType" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Account Type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:all>
  </xsd:complexType>
  <xsd:element name="InvestmentFundPriceRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="fund" type="Q1:Fund" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Fund</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="date" type="Q1:MWDate" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CreateInvestmentAccountRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="MSGHDR" type="tns:MSGHDR_TYPE"/>
        <xsd:element name="MSGBDY" type="tns:MSGBDY_REQ_TYPE"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CreateInvestmentAccountResponse">
    <xsd:complexType>
      <xsd:choice>
        <xsd:element name="success" type="tns:CreateInvestmentAccountResponseType"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:choice>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="MSGHDR_TYPE">
    <xsd:sequence>
      <xsd:element name="MsgDateTime" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SessionId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ChannelId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="FunctionId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SubFunctionId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="UserId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Lang" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="TerminalId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Reserved1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="MSGBDY_REQ_TYPE">
    <xsd:sequence>
      <xsd:element name="CTRL" type="tns:CTRL_REQ_TYPE" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="WFLOG" type="tns:WFLOG_REQ_TYPE" maxOccurs="unbounded" minOccurs="0"/>
      <xsd:element name="PTY" type="tns:PTY_REQ_TYPE" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PTYADD" type="tns:PTYADD_REQ_TYPE" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PTYACCTADDR" type="tns:PTYACCTADDR_REQ_TYPE" maxOccurs="unbounded" minOccurs="0"/>
      <xsd:element name="PTYIDS" type="tns:PTYIDS_REQ_TYPE" maxOccurs="unbounded" minOccurs="0"/>
      <xsd:element name="PTYCONT" type="tns:PTYCONT_REQ_TYPE" maxOccurs="unbounded" minOccurs="0"/>
      <xsd:element name="PTYSYSLNK" type="tns:PTYSYSLNK_REQ_TYPE" maxOccurs="unbounded" minOccurs="0"/>
      <xsd:element name="PTYRLSHP" type="tns:PTYRLSHP_REQ_TYPE" maxOccurs="unbounded" minOccurs="0"/>
      <xsd:element name="PTYDATA" type="tns:PTYDATA_REQ_TYPE" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ACCT" type="tns:ACCT_REQ_TYPE" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ACCTDTL" type="tns:ACCTDTL_REQ_TYPE" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ACCTRLSHP" type="tns:ACCTRLSHP_REQ_TYPE" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ACCTDTL_REQ_TYPE">
    <xsd:sequence>
      <xsd:element name="AcctHolderNo" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AddIntrstInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AtmPrintedName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AvgMonthBalanceCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CancelDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CancelDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CancelUsr" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CashExecInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CcExecInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="ComProdExecInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CorrespondAddrCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CrtDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CrtUsr" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="DbCrCountry1Cd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="DbCrCountry2Cd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="DbCrCountry3Cd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="IdsNo" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="IntlTransferExecInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvInsExecInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvObjCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvRiskTolrCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="JointHolderCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="LastMaintDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="LastMaintUsr" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="LocalTransferExecInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MailOptCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MaxIntlTrnsCrAmtCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MaxIntlTrnsCrNoCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MaxIntlTrnsDbAmtCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MaxIntlTrnsDbNoCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MaxMonthTrnsAmtCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MaxMonthTrnsNoCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="OpenReasonCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="OpenReasonOther" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="OtherExecDesc" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="OtherExecInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PersChqExecInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PrfrdClndrCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PrfrdLangCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="SignOptionCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="StatementFreq" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="TreasProdExecInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CTRL_REQ_TYPE">
    <xsd:sequence>
      <xsd:element name="CheckBancsAcctFlg" type="xsd:string" minOccurs="1" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="WFLOG_REQ_TYPE">
    <xsd:sequence>
      <xsd:element name="WfUserId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="WfFunctionId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="WfUserIdApprover" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="WfTerminalAddress" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="WfSessionId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="WfApproverResponse" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="WfLogTimeStamp" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="WfAuthLevel" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PTY_REQ_TYPE">
    <xsd:sequence>
      <xsd:element name="BusinessDivisionCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CurrCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CustNo" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="EcnId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FamilyName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FirstName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FullName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="LanguageCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="LastName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MainBranchNo" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MiddleName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="NationalityCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartyId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartySubTypeCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PrimeRm" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="TitleCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PTYADD_REQ_TYPE">
    <xsd:sequence>
      <xsd:element name="AddInvObjCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AddProfession" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FinPlanFlg" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FpqAnswer1" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FpqAnswer2" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FpqAnswer3" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FpqAnswer4" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FpqAnswer5" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FpqAnswer6" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FpqAnswer7" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FpqAnswer8" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="HearCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvAdvisorCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvHorizonCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvKnowledgeCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvManagerCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvPayAddFeesFlg" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvPrefOnOffShoreFlg" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvPrefServChnlCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvProdHighRatioCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvProdLowRatioCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvProdMidRatioCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvRisPrefCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvTendToOtherBankFlg" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvBankPrefLocOffShoreFl" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvLoyaltyNoOfBanks" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MailOptCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="NetWorthCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PersFinAssetCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PrfrInvstAdvcFlg" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RisFlg" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="TotalInvAssets" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="TotalPoints" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvCustRiskScoreCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvCustTotRiskScore" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="InvKycInValidFlg" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="OnBoardDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PTYACCTADDR_REQ_TYPE">
    <xsd:sequence>
      <xsd:element name="AcctNo" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AddresExpiryDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AddresExpiryDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="Address1" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="Address2" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="Address3" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="Address4" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AddressEffectiveDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AddressEffectiveDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AddressStopDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AddressStopDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AddressTypeCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="ApartmentNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="City" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CountryCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="DefaultAddressFlg" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="District" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="HouseNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="LangCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="LocationDirections" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartyAddressId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PoBox" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PostalCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="State" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="Status" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="Street" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="TimeZone" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="ADDRLNG" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PTYIDS_REQ_TYPE">
    <xsd:sequence>
      <xsd:element name="DefaultFlg" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="IdExpiryDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="IdExpiryDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="IdIssueDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="IdIssueDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="IdIssuePlace" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="IdLanguageCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="IdNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="IdTypeCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="IssueCountryCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="LastAmndDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="LastAmndDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartyIdsId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PrintedAddress1" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PrintedAddress2" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PrintedAddress3" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PrintedFamilyName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PrintedFullName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="Status" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="ValidatedFlg" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PTYCONT_REQ_TYPE">
    <xsd:sequence>
      <xsd:element name="AcctId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="ContactEndDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="ContactEndDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="ContactId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="ContactStartDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="ContactStartDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="ContactTypeCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="DecisionMakerFlg" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="DefaultContact" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="Email" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="EmpWebSite" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="Fax1Ext" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="Fax2Ext" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="Fax3Ext" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FaxNo1" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FaxNo2" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FaxNo3" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="HomePageUrl" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="JobTitle" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="LangCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MobileNo1" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MobileNo2" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MobileNo3" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PagerNo" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartyId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartyIdRelated" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="Phone1Ext" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="Phone2Ext" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="Phone3Ext" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PhoneNo1" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PhoneNo2" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PhoneNo3" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PreferredContactTime" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="Rank" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="StatusCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="TelexNo" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PTYSYSLNK_REQ_TYPE">
    <xsd:sequence>
      <xsd:element name="SystemCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="ChanelIndFlg" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="SysCustUsrNo" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="StatusCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="StartDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="StartDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="EndDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="EndDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PTYRLSHP_REQ_TYPE">
    <xsd:sequence>
      <xsd:element name="Ambition" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AmendDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AmendDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CurrCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="EndDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="EndDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="OtherBankAcctTypeCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="OtherBankBranchCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="OthrBankAcctNo" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="OthrBankCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartyIdRelated" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartyRelationId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartyRelationCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="Percentage" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyAddress1" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyAddress2" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyCifNo" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyCity" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyCountryCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyPoBox" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyPostalCode" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="SpecialCaseCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyFirstName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyFamilyName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyMiddleName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyLastName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyIdTypeCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyIdNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyIdIssuePlace" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyIdIssueDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyIdIssueDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyIdExpiryDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyIdExpiryDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyPhoneNo1" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyFaxNo1" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="StartDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="StartDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="StatusCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PTYDATA_REQ_TYPE">
    <xsd:sequence>
      <xsd:element name="PTYIND" type="tns:PTYINDType" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PTYBUS" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ACCT_REQ_TYPE">
    <xsd:sequence>
      <xsd:element name="AcctAtmInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctBalance" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctBkStatusInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctCfCreatDeplInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctCfInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctCfStatusInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctClassCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctCrGlNo" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctCtgryCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctDesc" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctDrGlNo" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctIbInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctIsCreatDeplInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctIsInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctIsStatusInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctIvrInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctJointCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctLsCreatDeplInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctLsInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctLsStatusInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctNo" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctOpenDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctOpenDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctStatusCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctTfCreatDeplInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctTfInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctTfStatusInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctTrCreatDeplInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctTrInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctTrStatusInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctTypeCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AtmChannelInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="BrChannelInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CreateBranchCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CreateDivisionCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CrtDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CrtUsr" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CurrCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CurrentBalanceDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CurrentUnitPrice" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="FxRate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="IbChannelInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="IvrChannelEnqInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="IvrChannelFullInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="IvrChannelInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="LastMaintDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="LastMaintUsr" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MatuarityDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MaturityDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MobChannelInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="OrgChannelIndFlg" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="OrgSystemCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RmId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="TadawolChannelInd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="UnitBalance" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ACCTRLSHP_REQ_TYPE">
    <xsd:sequence>
      <xsd:element name="AcctIdRelated" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctNoRelated" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctNoRelatedCurrCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="AcctTypeCdRelated" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="DesignationFlg" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="EcnId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="JointPercent" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartyId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartyIdRelated" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="EcnIdRelated" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="CifNoRelated" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MainAcctId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MainAcctNo" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="MainAcctTypeCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartyFirstName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartyFamilyName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartyMiddleName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartyLastName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartyName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="PartySubTypeCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyFirstName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyFamilyName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyMiddleName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyLastName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartyName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelatedPartySubTypeCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelationCancelDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelationCancelDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelationExpDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelationExpDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelationModDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelationModDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelationStartDate" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelationStartDateH" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelationStatus" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="RelationTypeCd" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="InvestmentFundPriceResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="record" type="tns:InvestmentFundPriceType" maxOccurs="unbounded" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="InvestmentFundPriceType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="number" type="xsd:int" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="fund" type="Q1:Fund" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Fund</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="currency" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Currency</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="price" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="date" type="Q1:DateOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:all>
  </xsd:complexType>
  <xsd:element name="InvestmentAccountDetailsRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="accountId" type="xsd:string" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>AccID</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentAccountDetailsResponse">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="accountDetails" type="tns:InvestmentAccountDetailsType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentBalanceProfileRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="accountId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentBalanceProfileResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="fund" type="tns:FundBalanceProfileType" maxOccurs="unbounded" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentFundTermsAndConditionsRequest">
    <xsd:complexType>
      <xsd:all maxOccurs="1" minOccurs="0">
        <xsd:element name="fundNumber" type="xsd:int" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="customerLanguage" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentFundTermsAndConditionsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="termsAndConditions" type="tns:FundTermsAndConditionsType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CancelFundTradeRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="order" type="xsd:int" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="accountId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CancelFundTradeResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:FundCancelTradeType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="FundPendingTradeRequest">
    <xsd:complexType>
      <xsd:all maxOccurs="1" minOccurs="0">
        <xsd:element name="accountId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="transactionType" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:minLength value="0"/>
              <xsd:maxLength value="1"/>
              <xsd:enumeration value="A"/>
              <xsd:enumeration value="B"/>
              <xsd:enumeration value="C"/>
              <xsd:enumeration value="U"/>
              <xsd:enumeration value="P"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="transactionDate" type="Q1:Date" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="FundPendingTradeResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="pendingFundTrade" type="tns:FundPendingTradeInfoType" maxOccurs="unbounded" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentFundTransactionsRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="accountId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="fundCode" type="Q1:Fund" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="quantity" type="xsd:int" maxOccurs="1" minOccurs="1"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentFundTransactionsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="record" type="tns:FundTransactionsType" maxOccurs="unbounded" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentQuestionersRequest">
    <xsd:complexType>
      <xsd:all maxOccurs="1" minOccurs="0">
        <xsd:element name="shortCIF" type="Q1:ShortCIF" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="includeThimarDeclarationFund" type="Q1:trueOrFalseType" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentQuestionersResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="custInfo" type="tns:custInfoType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="question" type="tns:InvestmentQuestionType" maxOccurs="unbounded" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="InvestmentAccountDetailsType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="firstName" type="Q1:FirstName" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="lastName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="address1" type="Q1:Max70Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="address2" type="Q1:Max70Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="address3" type="Q1:Max70Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="status" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="statusDescription" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ivr" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="broker" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="branch" type="Q1:Branch" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="homePhone" type="Q1:PhoneNumberOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="workPhone" type="Q1:PhoneNumberOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="postCode" type="Q1:Max16Text" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="nationality" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="joint" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankAccountId" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="currency" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cif" type="Q1:CIF" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="isNumber" type="xsd:int" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="FundBalanceProfileType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="code" type="xsd:int" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="balance" type="Q1:Amount" default="0" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="currency" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="balanceSAR" type="Q1:Amount" default="0" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="unitPrice" type="Q1:Amount" default="0" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="units" type="xsd:double" maxOccurs="1" minOccurs="1" default="0.0"/>
      <xsd:element name="valueDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="openDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="blockAmount" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="exchangeRate" type="xsd:double" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="status" type="xsd:int" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="statusDescription" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="accountType" type="Q1:AccountType" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="nameInEnglish" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="nameInArabic" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="subStatus" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="remStatus" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="swiStatus" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:element name="RedeemInvestmentFundRequest">
    <xsd:complexType>
      <xsd:all maxOccurs="1" minOccurs="0">
        <xsd:element name="fromAccountId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="fundCode" type="Q1:Fund" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="amount" type="Q1:Amount" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="toAccountId" type="Q1:AccountId" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="source" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="reason" type="xsd:string" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="type" type="xsd:string" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="balance" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="shortCIF" type="Q1:ShortCIF" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="referenceNumber" type="xsd:int" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="payMethod" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="RedeemInvestmentFundResponse">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="fundRedemptionResult" type="tns:FundRedemptionType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="SwitchInvestmentFundRequest">
    <xsd:complexType>
      <xsd:all maxOccurs="1" minOccurs="0">
        <xsd:element name="fromAccount" type="xsd:string"/>
        <xsd:element name="fromFund" type="Q1:Fund"/>
        <xsd:element name="toAccount" type="xsd:string"/>
        <xsd:element name="toFund" type="Q1:Fund"/>
        <xsd:element name="fromAmount" type="Q1:Amount"/>
        <xsd:element name="toAmount" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="shortCIF" type="Q1:ShortCIF"/>
        <xsd:element name="referenceId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="source" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="balance" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="type" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="F"/>
              <xsd:enumeration value="P"/>
              <xsd:enumeration value="C"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="initial" type="Q1:YorNType" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="SwitchInvestmentFundResponse">
    <xsd:complexType>
      <xsd:sequence maxOccurs="1" minOccurs="0">
        <xsd:element name="success" type="tns:SwitchInvestmentFundResultType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentSubscriptionRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="accountId" type="Q1:AccountId" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="toAccountId" type="xsd:string" maxOccurs="1" minOccurs="1" default="0"/>
        <xsd:element name="fund" type="Q1:Fund" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="amount" type="Q1:Amount" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="description" type="xsd:string" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="source" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="initialIndicator" type="Q1:YorNType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="shortCIF" type="Q1:ShortCIF" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="reason" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="override" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="sourceOfMoney" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="newAcc" type="Q1:AccountId" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="signTermsCond" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="segmentLimit" type="xsd:double" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="baseAmount" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="dueAmount" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="treasuryRate" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="rateType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="refNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="mobileNo" type="Q1:MobileNumber" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="bookingRef" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentSubscriptionResponse">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="success" type="tns:SubscribeInvestmentResultType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="ProgramSubscriptionRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="MSGHDR" type="tns:messageHeaderType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="MSGBDY" type="tns:messageBodyType" maxOccurs="1" minOccurs="1"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="ProgramSubscriptionResponse">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="success" type="tns:SubscribeProgramResultType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="UnsubscribeProgramRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="MSGHDR" type="tns:messageHeaderType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="MSGBDY" type="tns:messageBodyType" maxOccurs="1" minOccurs="1"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="UnsubscribeProgramResponse">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="success" type="tns:SubscribeProgramResultType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentRecommendationRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="score" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentRecommendationResponse">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="recommendation" type="tns:InvestmentRecommendationType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="FundsListRequest">
    <xsd:complexType>
      <!--Note: FundList doesn't expect any parameters. I have put a request 
				to NCB to confirm it. Incase if parameters are required, we can uncomment 
				them. Author: Muneeb. <xsd:all> <xsd:element name="shortCIF" type="Q1:ShortCIF"> 
				</xsd:element> <xsd:element name="fromAccID" type="Q1:AccountId"> </xsd:element> 
				<xsd:element name="toAccID" type="Q1:AccountId"> </xsd:element> <xsd:element 
				name="fromAmount" type="Q1:Amount"> </xsd:element> <xsd:element name="toAmount" 
				type="Q1:Amount"> </xsd:element> <xsd:element name="desc" type="xsd:string"> 
				</xsd:element> <xsd:element name="accID" type="Q1:AccountId"> </xsd:element> 
				<xsd:element name="code" type="xsd:string"> </xsd:element> </xsd:all>-->
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="FundsListResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="fund" type="tns:FundInfoType" maxOccurs="unbounded" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentFundTradeDatesRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="fromFund" type="Q1:Fund" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="toFund" type="Q1:Fund" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentFundTradeDatesResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="tradeDates" type="tns:FundTradeDatesType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="FundCancelTradeType">
    <xsd:all>
      <xsd:element name="order" type="xsd:int" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="reversal" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="journal" type="Q1:JournalId" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="referenceId" type="xsd:string" maxOccurs="1" minOccurs="0" default="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="FundPendingTradeInfoType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="number" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="code" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ED"/>
            <xsd:enumeration value="TR"/>
            <xsd:enumeration value="PW"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="descriptionInEnglish" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="descriptionInArabic" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="order" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountFrom" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fundFrom" type="Q1:Fund" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountTo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fundTo" type="Q1:Fund" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="transactionDate" type="Q1:DateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="transactionTime" type="xsd:time" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="valueDate" type="Q1:DateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="amount" type="Q1:Amount" maxOccurs="1" minOccurs="0" default="0"/>
      <xsd:element name="source" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="sourceInEnglish" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="sourceInArabic" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="branch" type="Q1:Branch" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="user" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankAccount" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="redCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="payMode" type="xsd:int" maxOccurs="1" minOccurs="0" default="0"/>
      <xsd:element name="status" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="U"/>
            <xsd:enumeration value="C"/>
            <xsd:enumeration value="P"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="cancelDate" type="Q1:DateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cancelTime" type="xsd:time" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cancelUser" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="signTermCond" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="sourceOfMoney" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="processDate" type="Q1:DateOptional" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="FundTransactionsType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="number" type="xsd:int" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="code" maxOccurs="1" minOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ED"/>
            <xsd:enumeration value="TO"/>
            <xsd:enumeration value="PW"/>
            <xsd:enumeration value="TI"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="descriptionInEnglish" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="descriptionInArabic" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="date" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="unitPrice" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="units" type="xsd:double" default="0.0" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="amount" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="acuUnits" type="xsd:double" default="0.0" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="setDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="order" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fromFund" type="Q1:Fund" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="source" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="payType" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="balance" type="Q1:Amount" maxOccurs="1" minOccurs="0" default="0.0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="FundRedemptionType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="tradeDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="collectDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="journal" type="Q1:JournalId" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="time" type="Q1:MWTimeOptional"/>
      <xsd:element name="currency" type="Q1:ISOCurrencyCode"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="SwitchInvestmentFundResultType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="tradeDate" type="Q1:Date"/>
      <xsd:element name="valueDate" type="Q1:DateOptional"/>
      <xsd:element name="journal" type="Q1:JournalId"/>
      <xsd:element name="time" type="Q1:MWTime"/>
      <xsd:element name="fromCurrency" type="Q1:ISOCurrencyCode"/>
      <xsd:element name="toCurrency" type="Q1:ISOCurrencyCode"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="InvestmentQuestionType">
    <xsd:sequence>
      <xsd:element name="text" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="answer" type="tns:InvestmentQuestionAnswerType" maxOccurs="unbounded" minOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="InvestmentQuestionAnswerType">
    <xsd:all>
      <xsd:element name="letter" maxOccurs="1" minOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="a"/>
            <xsd:enumeration value="b"/>
            <xsd:enumeration value="c"/>
            <xsd:enumeration value="d"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="qValue" maxOccurs="1" minOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:int">
            <xsd:enumeration value="1"/>
            <xsd:enumeration value="2"/>
            <xsd:enumeration value="3"/>
            <xsd:enumeration value="4"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="text" type="xsd:string" maxOccurs="1" minOccurs="1"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="InvestmentRecommendationType">
    <xsd:all>
      <xsd:element name="riskInEnglish" maxOccurs="1" minOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="High"/>
            <xsd:enumeration value="Low"/>
            <xsd:enumeration value="Low Medium"/>
            <xsd:enumeration value="Medium"/>
            <xsd:enumeration value="Medium High"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="fundList" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="code" maxOccurs="1" minOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="01"/>
            <xsd:enumeration value="02"/>
            <xsd:enumeration value="03"/>
            <xsd:enumeration value="04"/>
            <xsd:enumeration value="05"/>
            <xsd:enumeration value="06"/>
            <xsd:enumeration value="07"/>
            <xsd:enumeration value="08"/>
            <xsd:enumeration value="09"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="SubscribeInvestmentResultType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="tradeDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="valueDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="journalId" type="Q1:JournalId" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="referenceNumber" type="xsd:int" maxOccurs="1" minOccurs="1" default="0"/>
      <xsd:element name="balance" type="Q1:Amount" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="time" type="xsd:int" maxOccurs="1" minOccurs="1" default="0"/>
      <xsd:element name="currency" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="1"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="FundInfoType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="id" type="xsd:int"/>
      <xsd:element name="currency" type="xsd:string"/>
      <xsd:element name="status" type="xsd:string"/>
      <xsd:element name="subStatus" type="xsd:string"/>
      <xsd:element name="redStatus" type="xsd:string"/>
      <xsd:element name="swiStatus" type="xsd:string"/>
      <xsd:element name="glAccountId" type="Q1:AccountId"/>
      <xsd:element name="shortCode" type="xsd:string"/>
      <xsd:element name="type">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="S"/>
            <xsd:enumeration value="L"/>
            <xsd:enumeration value="M"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="subValueToday" type="Q1:MWDateOptional"/>
      <xsd:element name="redValueToday" type="Q1:MWDateOptional"/>
      <xsd:element name="swiValueToday" type="Q1:MWDateOptional"/>
      <xsd:element name="nextSubValue" type="Q1:MWDateOptional"/>
      <xsd:element name="nextRedValue" type="Q1:MWDateOptional"/>
      <xsd:element name="nextSwiValue" type="Q1:MWDateOptional"/>
      <xsd:element name="refreshDate" type="xsd:string"/>
      <xsd:element name="price" type="Q1:Amount"/>
      <xsd:element name="date" type="Q1:MWDateOptional"/>
      <xsd:element name="subCutOffTime" type="xsd:string"/>
      <xsd:element name="sucCutOffTime" type="xsd:string"/>
      <xsd:element name="trfCutOffTime" type="xsd:string"/>
      <xsd:element name="redCutOffTime" type="xsd:string"/>
      <xsd:element name="name" type="xsd:string"/>
      <xsd:element name="percentageFlag" type="Q1:YorNType"/>
      <xsd:element name="percentage" type="xsd:double" default="0.0"/>
      <xsd:element name="amountLimit" type="Q1:Amount"/>
      <xsd:element name="assestUnderManagement" type="xsd:string"/>
      <xsd:element name="minimumThimar" type="Q1:Amount"/>
      <xsd:element name="nameEnglish" type="xsd:string"/>
      <xsd:element name="nameArabic" type="xsd:string"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="FundTermsAndConditionsType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="fundCode" type="Q1:Fund" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="name" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="language" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="size" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="version" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="mimeType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="binaryData" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="textData" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="FundTradeDatesType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="valueDate" type="Q1:MWDateOptional"/>
      <xsd:element name="colDate" type="Q1:MWDateOptional"/>
      <xsd:element name="trfDate" type="Q1:MWDateOptional"/>
      <xsd:element name="trtDate" type="Q1:MWDateOptional"/>
      <xsd:element name="subCutOffTime" type="Q1:MWTimeOptional"/>
      <xsd:element name="redCutOffTime" type="Q1:MWTimeOptional"/>
      <xsd:element name="trfCutOffTime" type="Q1:MWTimeOptional"/>
      <xsd:element name="time" type="Q1:MWTime"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="messageHeaderType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="messageDateTime" type="Q1:DateFormat" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="sessionId" type="xsd:int" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="channelId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="functionId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="userId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="language" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="terminalId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="reserved1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="responseMessageHeaderType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="date" type="Q1:DateFormat" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="sessionId" type="xsd:int" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="channelId" type="xsd:int" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="functionId" type="xsd:int" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="userId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="language" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="terminalId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="reserved1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="responseCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="responseDescription" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="responseField" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="messageBodyType">
    <xsd:all>
      <xsd:element name="CTRL" type="tns:messageControlType"/>
      <xsd:element name="PTY" type="tns:messagePartyType"/>
      <xsd:element name="NTFCTN" type="tns:NTFCTNType" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="messageControlType">
    <xsd:all>
      <xsd:element name="OperationMode" maxOccurs="1" minOccurs="1">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="R"/>
            <xsd:enumeration value="S"/>
            <xsd:enumeration value="E"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="UserPreferedCalendar" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="ControlFlg1" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ControlFlg2" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ControlFlg3" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ConvertATMType" type="Q1:YorNType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ForceFlag" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="messagePartyType">
    <xsd:sequence maxOccurs="1" minOccurs="0">
      <xsd:element name="EcnId" type="xsd:string"/>
      <xsd:element name="PartyId" type="xsd:string"/>
      <xsd:element name="PTYSYSLNK" type="tns:partySysLinkType" maxOccurs="unbounded" minOccurs="0"/>
      <xsd:element name="PTYPGM" type="tns:partyProgramType" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="partySysLinkType">
    <xsd:all>
      <xsd:element name="SysLnkPartyRelationId" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SysLnkPartyID" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SystemCd" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ChanelIndFlg" type="Q1:YorNType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SysCustUsrNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="StatusCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="StartDate" type="Q1:DateFormat" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="StartDateH" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="EndDate" type="Q1:DateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="EndDateH" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="PartyOthSysCustId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="partyProgramType">
    <xsd:all>
      <xsd:element name="PartyProgramId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ProgramCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ActiveProgramFlg" type="Q1:YorNType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AutomaticRenewFlg" type="Q1:YorNType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SubscriptionDate" type="Q1:DateFormat" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="SubscriptionReqDate" type="Q1:DateFormat" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="UnsubscribeDate" type="Q1:DateFormat" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="UnsubscribeReqDate" type="Q1:DateFormat" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="RequestId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AcctId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AcctNo" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AcctTypeCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AcctIdPrev" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AcctNoPrev" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="AcctTypeCdPrev" type="Q1:AccountType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="RenewalPeriodCd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ForcedSubUnsubInd" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="NTFCTNType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="RequestId" type="xsd:int" maxOccurs="1" minOccurs="0" default="0"/>
      <xsd:element name="RequestNo" type="xsd:int" maxOccurs="1" minOccurs="0" default="0"/>
      <xsd:element name="RequestType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="RequestorUserNotes" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ResponseStatus" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ResponseUserId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ResponseUserRoleId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ResponseUserNotes" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ItemKey" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ActivityLabel" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="SubscribeProgramResultType">
    <xsd:all>
      <xsd:element name="messageHeader" type="tns:responseMessageHeaderType"/>
      <xsd:element name="messageBody" type="tns:messagePartyType"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:element name="OrderRequest">
    <xsd:complexType>
      <xsd:all minOccurs="0" maxOccurs="1">
        <xsd:element name="customer" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Customer CIF Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="portfolioNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Portfolio account number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="samaShareCode" type="xsd:integer" default="0" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="action" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>0- All 1- Sell 2- Buy</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:enumeration value="1"/>
              <xsd:enumeration value="2"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="type" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>1=Market and 2=Limit</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:int">
              <xsd:enumeration value="1"/>
              <xsd:enumeration value="2"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="expiresOn" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="desiredQuantity" type="xsd:integer" default="0" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="minimumQuantity" type="xsd:integer" default="0" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="discloseQuantity" type="xsd:integer" default="0" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="fill" type="xsd:string" default="0" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="desiredPrice" type="xsd:decimal" default="0.0" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="customerType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="mobileNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="tifType" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="FOK"/>
              <xsd:enumeration value="GTD"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="ipoFlag" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="isPersonalFinance" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="accountNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="sequenceId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="isBaNCSUpdateRequired" type="xsd:boolean" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>The default value is true. In case of Bancs 14
							, false needs to be sent.</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="OrderResponse">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="success" type="tns:orderType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="orderType">
    <xsd:all>
      <xsd:element name="request" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="orderNumber" type="xsd:string" maxOccurs="1" minOccurs="1"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:element name="PortfolioDetailsRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="number">
          <xsd:simpleType>
            <xsd:annotation>
              <xsd:documentation>Portfolio number</xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:integer">
              <xsd:minInclusive value="0"/>
              <xsd:totalDigits value="10"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="type" default="All">
          <xsd:simpleType>
            <xsd:annotation>
              <xsd:documentation>1. All (Default) 2. MarketValue 3.
								LastClose</xsd:documentation>
            </xsd:annotation>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="All"/>
              <xsd:enumeration value="MarketValue"/>
              <xsd:enumeration value="LastClose"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="PortfolioDetailsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="share" type="tns:portfolioDetailsType" maxOccurs="unbounded" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="portfolioDetailsType">
    <xsd:all minOccurs="0" maxOccurs="1">
      <xsd:element name="shareCode" type="xsd:integer">
        <xsd:annotation>
          <xsd:documentation>Share code.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="english" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>english name of the shares.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="arabic" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Arabic name of the shares.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="quantity" type="xsd:integer">
        <xsd:annotation>
          <xsd:documentation>Quantity of the share.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="pledge" type="xsd:string"/>
      <xsd:element name="ownQuantity" type="xsd:integer"/>
      <xsd:element name="costPrice" type="Q1:Amount"/>
      <xsd:element name="costValue" type="Q1:Amount"/>
      <xsd:element name="marketValue">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="lastTradePrice" default="0.0" type="Q1:Amount">
              <xsd:annotation>
                <xsd:documentation>Markert last price</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
            <xsd:element name="value" default="0" type="Q1:Amount">
              <xsd:annotation>
                <xsd:documentation>Market value of the shares</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
            <xsd:element name="unrealizedProfit" default="0" type="Q1:Amount">
              <xsd:annotation>
                <xsd:documentation>Market value Unrealized Profit</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="lastCloseValue" type="tns:lastCloseValueType"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="lastCloseValueType">
    <xsd:sequence>
      <xsd:element name="lastTradePrice" default="0.0" type="Q1:Amount">
        <xsd:annotation>
          <xsd:documentation>Last Close price value</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="value" default="0" type="Q1:Amount">
        <xsd:annotation>
          <xsd:documentation>Last Close value</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="unrealizeProfit" default="0" type="Q1:Amount">
        <xsd:annotation>
          <xsd:documentation>Last close Unrealized Profit</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="RISAddSubscriptionRequest">
    <xsd:complexType>
      <xsd:all maxOccurs="1" minOccurs="0">
        <xsd:element name="fromAccountId" type="Q1:AccountId"/>
        <xsd:element name="transferAmount" type="Q1:Amount"/>
        <xsd:element name="frequency" type="xsd:string"/>
        <xsd:element name="frequencyCode" type="xsd:string"/>
        <xsd:element name="toAccountId" type="Q1:AccountId"/>
        <xsd:element name="mfAccountNumber" type="xsd:string"/>
        <xsd:element name="startDate" type="Q1:MWDate1"/>
        <xsd:element name="fromCurrencyCode" type="Q1:ISOCurrencyCode"/>
        <xsd:element name="securityCode" type="xsd:string"/>
        <xsd:element name="toCurrencyCode" type="Q1:ISOCurrencyCode"/>
        <xsd:element name="priorityCode" type="xsd:string"/>
        <xsd:element name="toAccountIdComments" type="xsd:string"/>
        <xsd:element name="channelCode" type="xsd:string"/>
        <xsd:element name="acceptFundFlag" type="Q1:YorNType"/>
        <xsd:element name="acceptRiskFlag" type="Q1:YorNType"/>
        <xsd:element name="thimarAppFee" type="xsd:int"/>
        <xsd:element name="systemIdCode" type="xsd:string"/>
        <xsd:element name="endDate" type="Q1:MWDate1"/>
        <xsd:element name="commission" type="xsd:double"/>
        <xsd:element name="calendar" type="xsd:string"/>
        <xsd:element name="autoChaseDays" type="xsd:string"/>
        <xsd:element name="paymentType" type="xsd:string"/>
        <xsd:element name="fundCode" type="xsd:string"/>
        <xsd:element name="payFeeOrFine" type="xsd:string"/>
        <xsd:element name="narrative" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="RISAddSubscriptionResponse">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="success" type="tns:RISAddSubscriptionType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="RISAddSubscriptionType">
    <xsd:all>
      <xsd:element name="referenceNumber" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="totalAmount" default="0.0" type="Q1:Amount" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="error" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:element name="RISCancelSubscriptionRequest">
    <xsd:complexType>
      <xsd:all maxOccurs="1" minOccurs="0">
        <xsd:element name="fromAccountId" type="Q1:AccountId"/>
        <xsd:element name="transferAmount" type="Q1:Amount"/>
        <xsd:element name="frequency" type="xsd:string"/>
        <xsd:element name="frequencyCode" type="xsd:string"/>
        <xsd:element name="orderDate" type="Q1:MWDate"/>
        <xsd:element name="toAccountId" type="Q1:AccountId"/>
        <xsd:element name="fromAccountIdComments" type="xsd:string"/>
        <xsd:element name="startDate" type="Q1:MWDate"/>
        <xsd:element name="fromCurrencyCode" type="Q1:ISOCurrencyCode"/>
        <xsd:element name="toCurrencyCode" type="Q1:ISOCurrencyCode"/>
        <xsd:element name="toAccountIdComments" type="xsd:string"/>
        <xsd:element name="channelCode" type="xsd:string"/>
        <xsd:element name="acceptFundFlag" type="Q1:YorNType"/>
        <xsd:element name="acceptRiskFlag" type="Q1:YorNType"/>
        <xsd:element name="thimarAppFee" type="Q1:Amount"/>
        <xsd:element name="systemIdentifierCode" type="xsd:string"/>
        <xsd:element name="endDate" type="Q1:MWDate"/>
        <xsd:element name="recordForFNSUse" type="xsd:string"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="RISCancelSubscriptionResponse">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="success" type="tns:RISCancelSubscriptionType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="TermsAndConditionsRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="fundCode" type="xsd:int" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="customerLanguage" type="xsd:int" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="dataSource" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="TermsAndConditionsResponse">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="success" type="tns:TermsandConditionsType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="RISUpdateSubscriptionRequest">
    <xsd:complexType>
      <xsd:all maxOccurs="1" minOccurs="0">
        <xsd:element name="fromAccountId" type="Q1:AccountId"/>
        <xsd:element name="transferAmount" type="Q1:Amount"/>
        <xsd:element name="frequency" type="xsd:string"/>
        <xsd:element name="frequencyCode" type="xsd:string"/>
        <xsd:element name="orderDate" type="Q1:MWDate1"/>
        <xsd:element name="toAccountId" type="Q1:AccountId"/>
        <xsd:element name="fromAccountIdComments" type="xsd:string"/>
        <xsd:element name="startDate" type="Q1:MWDate1"/>
        <xsd:element name="fromCurrencyCode" type="Q1:ISOCurrencyCode"/>
        <xsd:element name="securityCode" type="xsd:string"/>
        <xsd:element name="toCurrencyCode" type="Q1:ISOCurrencyCode"/>
        <xsd:element name="priorityCode" type="xsd:string"/>
        <xsd:element name="toAccountIdComments" type="xsd:string"/>
        <xsd:element name="channelCode" type="xsd:string"/>
        <xsd:element name="acceptFundFlag" type="Q1:YorNType"/>
        <xsd:element name="acceptRiskFlag" type="Q1:YorNType"/>
        <xsd:element name="thimarAppFee" type="Q1:Amount"/>
        <xsd:element name="systemIdCode" type="xsd:string"/>
        <xsd:element name="endDate" type="Q1:MWDate1"/>
        <xsd:element name="commission1" type="xsd:double"/>
        <xsd:element name="record" type="xsd:string"/>
        <xsd:element name="referenceNumber" type="xsd:string"/>
        <xsd:element name="calendar" type="xsd:string"/>
        <xsd:element name="autoChaseDays" type="xsd:string"/>
        <xsd:element name="paymentType" type="xsd:string"/>
        <xsd:element name="paymentPurpose" type="xsd:string"/>
        <xsd:element name="payFeeOrFine" type="xsd:string"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="RISUpdateSubscriptionResponse">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="success" type="tns:RISUpdateSubscriptionType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="RISStandingOrderEnquiryRequest" type="tns:RISStandingOrderEnquiryType"/>
  <xsd:element name="RISStandingOrderEnquiryResponse">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="success" type="tns:RISStandingOrderEnquiryType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="TermsandConditionsType">
    <xsd:all>
      <xsd:element name="fundCode" default="0" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="name" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="customerLanguge" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="size" type="xsd:int" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="version" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="mimeType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="dataBin" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="datatxt" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="RISStandingOrderEnquiryType">
    <xsd:all maxOccurs="1" minOccurs="0">
      <xsd:element name="accountNumber" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="fromAccountCurrencyCode" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="systemId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="shortEnquiryOpt" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pageNumber" default="0" type="Q1:PageNumber" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="paymentType" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="selectEnquireOrAmendOption" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="enquireOrAmendOptionCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pageControl" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="functionOpt1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ownerIndCode1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="standingOrdTypeCode1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="orderDate1" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="startOrNextDt1" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="endDate1" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="postingFreqCode1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="autoChaseDays1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="amtToXfer1" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="toAcctCurCode1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="recordForFNSUse1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="priorityTo1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pymtPurpose1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="priorityFrom1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="sOAcctNum1" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="systemIdentifier1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankID1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fill1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pmtType1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pmtPercentage1" default="0.0" type="xsd:double" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="refNum1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cal1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="channelcode1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="acceptfundflag1" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="acceptRiskflag1" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="thimarappfee1" default="0.0" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fromAcctNumComments1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="functionOpt2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ownerIndCode2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="standingOrdTypeCode2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="orderDate2" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="startOrNextDt2" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="endDate2" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="postingFreqCode2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="autoChaseDays2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="amtToXfer2" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="toAcctCurCode2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="recordForFNSUse2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="priorityTo2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pymtPurpose2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="priorityFrom2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="sOAcctNum2" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="systemIdentifier2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankID2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fill2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pmtType2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pmtPercentage2" default="0.0" type="xsd:double" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="refNum2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cal2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="channelcode2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="acceptfundflag2" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="acceptRiskflag2" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="thimarappfee2" default="0.0" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fromAcctNumComments2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="functionOpt3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ownerIndCode3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="standingOrdTypeCode3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="orderDate3" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="startOrNextDt3" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="endDate3" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="postingFreqCode3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="autoChaseDays3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="amtToXfer3" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="toAcctCurCode3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="recordForFNSUse3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="priorityTo3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pymtPurpose3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="priorityForm3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="sOAcctNum3" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="systemIdentifier3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankID3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fill3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pmtType3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pmtPercentage3" default="0.0" type="xsd:double" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="refNum3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cal3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="channelcode3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="acceptfundflag3" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="acceptRiskflag3" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="thimarappfee3" default="0.0" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fromAcctNumComments3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="functionOpt4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ownerIndCode4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="standingOrdTypeCode4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="orderDate4" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="startOrNextDt4" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="endDate4" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="postingFreqCode4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="autoChaseDays4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="amtToXfer4" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="toAcctCurCode4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="recordForFNSUse4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="priorityTo4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pymtPurpose4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="priorityFrom4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="sOAcctNum4" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="systemIdentifier4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankID4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fill4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pmtType4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pmtPercentage4" default="0.0" type="xsd:double" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="refNum4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cal4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="channelcode4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="acceptfundflag4" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="acceptRiskflag4" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="thimarappfee4" default="0.0" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fromAcctNumComments4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="functionOpt5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ownerIndCode5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="standingOrdTypeCode5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="orderDate5" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="startOrNextDt5" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="endDate5" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="postingFreqCode5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="autoChaseDays5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="amtToXfer5" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="toAcctCurCode5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="recordForFNSUse5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="priorityTo5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pymtPurpose5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="priorityFrom5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="sOAcctNum5" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="systemIdentifier5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankID5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fill5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pmtType5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pmtPercentage5" default="0.0" type="xsd:double" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="refNum5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cal5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="channelcode5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="acceptfundflag5" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="acceptRiskflag5" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="thimarappfee5" default="0.0" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fromAcctNumComments5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="functionOpt6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ownerIndCode6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="standingOrdTypeCode6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="orderDate6" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="startOrNextDt6" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="endDate6" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="postingFreqCode6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="autoChaseDays6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="amtToXfer6" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="toAcctCurCode6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="recordForFNSUse6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="priorityTo6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pymtPurpose6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="priorityFrom6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="sOAcctNum6" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="systemIdentifier6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankID6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fill6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pmtType6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pmtPercentage6" default="0.0" type="xsd:double" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="refNum6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cal6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="channelcode6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="acceptfundflag6" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="acceptRiskflag6" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="thimarappfee6" default="0.0" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fromAcctNumComments6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="functionOpt7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ownerIndCode7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="standingOrdTypeCode7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="orderDate7" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="startOrNextDt7" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="endDate7" type="Q1:MWDateOptional1" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="postingFreqCode7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="autoChaseDays7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="amtToXfer7" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="toAcctCurCode7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="recordForFNSUse7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="priorityTo7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pymtPurpose7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="priorityFrom7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="sOAcctNum7" type="Q1:AccountIdOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="systemIndentifier7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankID7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fill7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pmtType7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pmtPercentage7" default="0.0" type="xsd:double" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="refNum7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cal7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="channelcode7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="acceptfundflag7" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="acceptRiskflag7" type="Q1:YorNTypeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Thimarappfee7" default="0.0" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fromAcctNumComments7" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="moreRecordsToFollow" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="RISCancelSubscriptionType">
    <xsd:all>
      <xsd:element name="referenceNumber" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="error" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="RISUpdateSubscriptionType">
    <xsd:all>
      <xsd:element name="referenceNumber" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="error" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:simpleType name="MWDate1">
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]){8}|([0-9]){7}"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MWDate1Optional">
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="([0-9]){8}|([0-9]){7}()"/>
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:element name="FundInformationRequest">
    <xsd:complexType>
      <xsd:all>
        <xsd:element name="fundId" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>FundID</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:all>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="FundInformationResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="record" type="tns:FundInformationType" maxOccurs="unbounded" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="FundInformationType">
    <xsd:all>
      <xsd:element name="recordNo" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="fundId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="currencyCode" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="nameEnglish" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="nameArabic" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="glAccountId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="minimumBalance" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="minimumSubscription" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="minimumISubscription" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="minSwitch" type="Q1:Amount" maxOccurs="1" default="0.0" minOccurs="0"/>
      <xsd:element name="minRedemption" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="valuationCycle" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fluctuation" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fluctuationAmount" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="type" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="lastRate" type="Q1:Amount" maxOccurs="1" default="0.0" minOccurs="0"/>
      <xsd:element name="previousPct" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ytdPct" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="lastValueDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="year1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="year2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="year3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="year5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="shortCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cpf" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="redirect" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="subStatus" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="redStatus" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="swiStatus" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="status" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="subValueToday" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="redValueToday" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="swiValueToday" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="subValueNext" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="redValueNext" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="swiValueNext" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="refreshDate" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="price" type="Q1:Amount" maxOccurs="1" default="0.0" minOccurs="0"/>
      <xsd:element name="date" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="exChangeRate" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cutOffTimeSub" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cutOffTimeSuc" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cutOffTimeTrf" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="cutOffTimeRed" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="percentageFlag" type="Q1:YorNType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="percentage" type="Q1:Amount" default="0.0" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="amountLimit" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="assetUnderManagment" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="minThimar" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="custInfoType">
    <xsd:all>
      <xsd:element name="fpqPoints" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="invAccLimit" type="xsd:int" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="customerInvAccs" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="partyId" type="xsd:string" maxOccurs="1" minOccurs="1"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:element name="GetSharePriceRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="shareCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="sectorCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="isPersonalFinance" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetSharePriceResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:getSharePriceType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="getSharePriceType">
    <xsd:sequence>
      <xsd:element name="share" type="tns:getSharePriceShareType" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="getSharePriceShareType">
    <xsd:sequence>
      <xsd:element name="shareCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="shareNameEnglish" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="shareNameArabic" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="sectorCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="sectorEnglish" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="sectorArabic" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="openingPrice" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="closingPrice" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="currentPrice" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="lastPrice" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="lastVolume" type="xsd:integer" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="highPrice" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="lowPrice" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="shareQuantity" type="xsd:integer" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bidPrice" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bidQuantity" type="xsd:integer" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bestPrice" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bestQuantity" type="xsd:integer" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="minimumPrice" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="maximumPrice" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="personalFinancePrice" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetExecutionDetailsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="orderNumber" type="xsd:string" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="isOrderDetails" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="isPersonalFinance" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="accountNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="sequenceId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetExecutionDetailsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:getExecutionDetailsType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="getExecutionDetailsType">
    <xsd:sequence>
      <xsd:element name="orderDetails" type="tns:orderDetailsType" minOccurs="0" nillable="true"/>
      <xsd:element name="executionDetails" type="tns:executionDetailsType" nillable="true" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="orderDetailsType">
    <xsd:sequence>
      <xsd:element name="orderDetail" type="tns:orderDetailType" minOccurs="0" maxOccurs="unbounded" nillable="true"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="executionDetailsType">
    <xsd:sequence>
      <xsd:element name="executionDetail" type="tns:executionDetailType" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="orderDetailType">
    <xsd:sequence>
      <xsd:element name="brokerageAmount" type="Q1:Amount" maxOccurs="1" minOccurs="0" nillable="true"/>
      <xsd:element name="actualBuyingPrice" type="Q1:Amount" maxOccurs="1" minOccurs="0" nillable="true"/>
      <xsd:element name="orderDate" type="Q1:MWDate" maxOccurs="1" minOccurs="0" nillable="true"/>
      <xsd:element name="orderStatus" type="xsd:string" maxOccurs="1" minOccurs="0" nillable="true"/>
      <xsd:element name="orderDescription" type="xsd:string" maxOccurs="1" minOccurs="0" nillable="true"/>
      <xsd:element name="buyingValue" type="Q1:Amount" maxOccurs="1" minOccurs="0" nillable="true"/>
      <xsd:element name="buyingFlag" type="xsd:string" maxOccurs="1" minOccurs="0" nillable="true"/>
      <xsd:element name="isBuying" maxOccurs="1" minOccurs="0" nillable="true">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="1"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="isSelling" maxOccurs="1" minOccurs="0" nillable="true">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="1"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="orderNotes" type="xsd:string" maxOccurs="1" minOccurs="0" nillable="true"/>
      <xsd:element name="vat" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="executionDetailType">
    <xsd:sequence>
      <xsd:element name="tradedQuantity" type="xsd:string" nillable="true" minOccurs="0"/>
      <xsd:element name="tradedPrice" type="Q1:Amount" minOccurs="0" nillable="true"/>
      <xsd:element name="tradeDate" nillable="true" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:date">
            <xsd:pattern value="(([0-2]\d|[3][0-1])\/([0]\d|[1][0-2])\/[2][0]\d{2})$|^(([0-2]\d|[3][0-1])\/([0]\d|[1][0-2])\/[2][0]\d{2}\s([0-1]\d|[2][0-3])\:[0-5]\d\:[0-5]\d)"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="costValue" type="Q1:Amount" minOccurs="0" nillable="true"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetPortfolioAccountRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="encId" type="xsd:string" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Short CIF value</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="nationalId" type="xsd:string" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>National Id number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetPortfolioAccountResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="GetPortfolioAccountResponse" type="tns:getPortfolioAccountType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="getPortfolioAccountType">
    <xsd:sequence>
      <xsd:element name="investmentAccountNumber" type="xsd:int" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Investment Account Number. This is the Customer
						Identifier in Enable Systems (ETS/GBS). Logically similar to CIF
						in BaNCS but a different number.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="portfolioAccountDetails" type="tns:portfolioAccountDetailsType" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="portfolioAccountDetailsType">
    <xsd:sequence>
      <xsd:element name="cashAccountNumber" type="Q1:AccountIdOptional" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Customer account number in ETS system. Used for
						trading purpose.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="city" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>City name. Generally the CSRs write city names
						in this field, sometimes in Arabic, sometimes in English.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="nationalId" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>National id number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="postBox" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Post box number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="portfolioCategory" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Category of portfolio. Ex: The code 040 for
						Portfolio is a standard category (standard trading / normal
						porfolio).</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="portfolioStatus" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Status of the portfolio.
						Possible values are W
						for Pending, O for Opened, C for Closed</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="portfolioRequestId" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ETS request reference number. It is known as the
						internal portfolio number.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="csdNumber" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Central Securities Depository number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="portfolioDescriptionArabic" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Name of the portfolio. Default value is the
						customer name.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="portfolioDescriptionEnglish" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Name of the portfolio. Default value is the
						customer name.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="notes" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Description about the portfolio</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="postalCode" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Postal code number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="countryCode" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Country code number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="statusDate" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Last status updated date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cashAccountCategoryNumber" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Cash Account Category number value. Ex: The code
						200 is a standard category for cash account (standard customer /
						citizen customer) .</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="currencyCode" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Currency code value</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="currentAccountNumber" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Customers Bancs account. This is the deposit
						account.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CreateInvestmentAccountResponseType">
    <xsd:sequence>
      <xsd:element name="messageHeader" type="tns:MSGHDR_TYPE" minOccurs="1" maxOccurs="1"/>
      <xsd:element name="messageBody" type="tns:MSGBDY_TYPE" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="MSGBDY_TYPE">
    <xsd:sequence>
      <xsd:element name="systemErrorDesc" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="partyId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="ecnNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="customerNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="accountNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="UpdateNCBCCustomerRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="ecnId" type="Q1:ShortCIF" minOccurs="1" maxOccurs="1"/>
        <xsd:element name="currentAccountNumber" type="Q1:AccountIdOptional" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="customerInformation" type="tns:customerInformationType" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="applicationInformation" type="tns:applicationInformationType" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="contactInformation" type="tns:contactInformationType" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="vatRegistrationInformation" type="tns:vatRegistrationInformationType" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="UpdateNCBCCustomerResponse">
    <xsd:complexType>
      <xsd:choice>
        <xsd:element name="success" type="tns:UpdateNCBCCustomerResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:choice>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="customerInformationType">
    <xsd:all>
      <xsd:element name="firstName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="firstNameArabic" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="middleName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="middleNameArabic" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="familyName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="familyNameArabic" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="lastName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="lastNameArabic" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="title" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="gender" type="Q1:Gender" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="dateOfBirth" type="Q1:MWDateOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="placeOfBirth" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="nationality" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="maritalStatus" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="id" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="idType" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="idIssueDate" type="Q1:MWDateOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="idIssuePlace" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="idExpiryDate" type="Q1:MWDateOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="additionalID" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="additionalIDType" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="additionalIDIssueDate" type="Q1:MWDateOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="additionalIDIssuePlace" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="additionalIDExpiryDate" type="Q1:MWDateOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="cif" type="Q1:CIFOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="category" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="class" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="type" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="status" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="subtype" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="programCd1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="authorizedCapitalStock" type="xsd:decimal" default="0.0" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="paidupCapitalStock" type="xsd:decimal" default="0.0" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="pepFlag" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="1"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="highRiskFlag" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="1"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="remarks" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:maxLength value="50"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="ncbcGovtJudicialFlag" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ncbcPepRelatedFlag" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="clientRiskClassification" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="clientQuestionnaireClassification" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="unifiedNationalNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountNumber1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountType1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankName1" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountNumber2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountType2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankName2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountNumber3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountType3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankName3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountNumber4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountType4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankName4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountNumber5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountType5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankName5" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountNumber6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="accountType6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankName6" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="flagNonNCBClient" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Possible Values : 	Y|N</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountInformationList" type="tns:AccountInformationListType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="relationList" type="tns:RelationListType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="localSharePortfolioFlag" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Possible Values : 	Y|N</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="RelationListType">
    <xsd:sequence>
      <xsd:element name="relationListDetails" type="tns:RelationListDetailsType" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AccountInformationListType">
    <xsd:sequence>
      <xsd:element name="accountInfoList" type="tns:AccountInfoDetailsType" minOccurs="0" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AccountInfoDetailsType">
    <xsd:sequence>
      <xsd:element name="accountNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="accountType" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="bankName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AuthorityToPerformCheckboxeListType">
    <xsd:sequence>
      <xsd:element name="authorityToPerformCheckboxe" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="RelationListDetailsType">
    <xsd:sequence>
      <xsd:element name="authorityToPerformCheckboxeList" type="tns:AuthorityToPerformCheckboxeListType" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="function" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Possible Values : 	A|U|D</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="relationshipType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Possible Values : 1|2|3|4|5  . 
							1)      Court appointed receiver 
							2)      Guardian			 
							3)      Power of attorney / Attorney in-fact				 
							4)      Guardian of an incapacitated Person	 
							5)      Guardian of an interdicted Person</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="relationPartyCIFNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="relatedPartyName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="relatedPartyIDType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="relatedPartyIDNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="applicationInformationType">
    <xsd:all>
      <xsd:element name="incKycNeeded" type="Q1:YorNTypeOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="invValidationDate" type="Q1:MWDateOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="language" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="legalEntity" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="onBoardReason" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="onBoardingDate" type="Q1:MWDateOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="registrationStatus" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="samaApprovalDate" type="Q1:MWDateOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="samaApprovalNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="subLegalEntity" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="branch" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="totalPoints" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="primeRM" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="mfRiskLevel" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="contactInformationType">
    <xsd:all>
      <xsd:element name="basicNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="unitNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="street" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="city" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="district" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="poBox" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="postalCode" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="phoneNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="mobileNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="additionalMobileNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="faxNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="email" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="homeCountry" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="homeCountryUnitNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="homeCountryStreet" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="homeCountryCity" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="homeCountryDistrict" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="homeCountryPostalCode" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="employerBasicNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="employerUnitNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="employerStreet" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="employerCity" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="employerDistrict" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="employerPoBox" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="employerPostalCode" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="employerPhoneNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="employerFaxNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="employerFaxExtension" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="employerEmail" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="regionCode" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="vatRegistrationInformationType">
    <xsd:all>
      <xsd:element name="vatRegistrationNumber" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="vatCountryRegistration" type="xsd:string" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>1=KSA
						2= Other GCC Member Country
						3= Outside GCC</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="vatRelatedPartyPosition" type="xsd:string" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>1 = Employee of NCBC
						2 = Related Entities (NCB, REDCO, and Esnad etc Country)
						3 = Director of NCBC
						4 = Other</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="placeOfSupply" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:complexType name="UpdateNCBCCustomerResponseType">
    <xsd:all>
      <xsd:element name="investmentAccountNumber" type="xsd:int" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="cashAccountNumber" type="Q1:AccountIdOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="iban" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="currentAccountNumber" type="Q1:AccountIdOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="cashAccountCategory" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:element name="InvestmentAccountTransactionsRequest">
    <xsd:annotation>
      <xsd:documentation>Investment AccountTransactions</xsd:documentation>
    </xsd:annotation>
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountId">
          <xsd:annotation>
            <xsd:documentation>Account Number</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:maxLength value="16"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="quantity" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>Number of transactions (Default 20)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fromAmount" type="Q1:Amount">
          <xsd:annotation>
            <xsd:documentation>Amount from</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="toAmount" type="Q1:Amount">
          <xsd:annotation>
            <xsd:documentation>Amount to</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fromDate" type="Q1:MWDateOptional">
          <xsd:annotation>
            <xsd:documentation>Date from in the format YYYYMMDD</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="toDate" type="Q1:MWDateOptional">
          <xsd:annotation>
            <xsd:documentation>Date to in the format YYYYMMDD</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fromTime" type="Q1:MWTime">
          <xsd:annotation>
            <xsd:documentation>Time from in the format HHMMSS (military)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="toTime" type="Q1:MWTime">
          <xsd:annotation>
            <xsd:documentation>Time To in the format HHMMSS (military)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fromCheque" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>Cheque number to range</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="toCheque" type="xsd:string"/>
        <xsd:element name="tranType">
          <xsd:annotation>
            <xsd:documentation>Transaction Type to be retrieved:
							DB- if Debit
							Transactions CR-if
							Credit Transactions HO-if Hold
							Transactions
							RL-if Release
							Transactions HR-if Hold/Release
							Transactions PC-if
							Promo Code List
							TC-if Transaction Categories List
							TD-if Transaction
							Extra Details</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="DB"/>
              <xsd:enumeration value="CR"/>
              <xsd:enumeration value="HO"/>
              <xsd:enumeration value="RL"/>
              <xsd:enumeration value="HR"/>
              <xsd:enumeration value="PC"/>
              <xsd:enumeration value="TC"/>
              <xsd:enumeration value="TD"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="customerLang" default="N">
          <xsd:annotation>
            <xsd:documentation>Retrieves transactions description
							base on the
							Customer Account
							Language if the value is Y,
							otherwise default N,
							base on
							language request</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="N"/>
              <xsd:enumeration value="Y"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="tranProcDate" default="P">
          <xsd:annotation>
            <xsd:documentation>Retrieves transaction based on
							processed date T
							if Transaction Date
							(Default) P if Posted Date</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="P"/>
              <xsd:enumeration value="T"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="nextRecNo" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>Next Record to Retrieve from Previous Inquiry</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="journal" type="xsd:string">
          <xsd:annotation>
            <xsd:documentation>Journal to retrieve from (Only for GL Account)</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="tranOption" default="A">
          <xsd:annotation>
            <xsd:documentation>Transaction Type: A-if All (Default,
							Exclude M,
							P, H , T) M-if Only
							Messages F-if Only Financial P-if
							Only Stop
							H-if Only Hold , Release
							T-if Change Account Status</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="A"/>
              <xsd:enumeration value="M"/>
              <xsd:enumeration value="F"/>
              <xsd:enumeration value="P"/>
              <xsd:enumeration value="H"/>
              <xsd:enumeration value="T"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="sortOption" default="DSC">
          <xsd:annotation>
            <xsd:documentation>Show transactions order ordering</xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="ASC"/>
              <xsd:enumeration value="DSC"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InvestmentAccountTransactionsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="investmentAccountTransactions" type="tns:investmentAccountTransactionsType" maxOccurs="1" minOccurs="0"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="investmentAccountTransactionsType">
    <xsd:sequence>
      <xsd:element name="transaction" type="tns:InvestmentTransactionType" maxOccurs="unbounded" minOccurs="0"/>
      <xsd:element name="totalTransactions" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="InvestmentTransactionType">
    <xsd:sequence>
      <xsd:element name="recordId" type="Q1:RecordId" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="date" type="xsd:date" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="postDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="postTime" type="Q1:MWTimeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="Loc" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="branch" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="teller" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="termNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="amount" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="balance" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="balanceTran" type="Q1:Amount" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="code" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="description" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="description2" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="description3" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="description4" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="category" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="journal" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="type" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="typeDesc" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="chequeNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ATMTranNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="ATMTermNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="nextRecNo" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="DeleteOrderRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="customer" type="xsd:string" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Customer</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="orderNumber" type="xsd:string" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Order Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customerType" type="xsd:string" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Customer Type</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="DeleteOrderResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:DeleteOrderResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="DeleteOrderResponseType">
    <xsd:sequence>
      <xsd:element name="orderNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ChangeOrderRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="ChangeOrder" type="tns:ChangeOrdersType" maxOccurs="1" minOccurs="1"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="ChangeOrdersType">
    <xsd:sequence>
      <xsd:element name="customer" type="xsd:string" maxOccurs="1" minOccurs="1">
        <xsd:annotation>
          <xsd:documentation>Customer</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="orderNumber" type="xsd:string" maxOccurs="1" minOccurs="1">
        <xsd:annotation>
          <xsd:documentation>Order Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="portfolioNumber" type="xsd:string" maxOccurs="1" minOccurs="1">
        <xsd:annotation>
          <xsd:documentation>Portfolio Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="expiryDate" type="Q1:DateTimeOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Date of Expiry</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="type" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="desiredQuantity" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Desired Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="minimumQuantity" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Minimum Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="discloseQuantity" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Disclose Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="fill" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="desiredPrice" type="Q1:Amount" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Desired Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="customerType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Customer Type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="tifType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ChangeOrderResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:ChangeOrdersResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="ChangeOrdersResponseType">
    <xsd:all>
      <xsd:element name="request" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      <xsd:element name="orderNumber" type="xsd:string" maxOccurs="1" minOccurs="1"/>
    </xsd:all>
  </xsd:complexType>
  <xsd:element name="CreateInvestmentStatementRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountId" type="Q1:AccountId" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Account ID</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fund" type="Q1:Fund" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Fund</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="dateFrom" type="Q1:DateTimeOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Date From</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="dateTo" type="Q1:DateTimeOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Date To</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="sourceOfFund" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Source Of Fund</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CreateInvestmentStatementResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:CreateInvestmentStatementResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="CreateInvestmentStatementResponseType">
    <xsd:sequence>
      <xsd:element name="journal" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Journal</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetPendingFundTransactionRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountId" type="Q1:AccountId" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Account ID</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fund" type="Q1:Fund" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Fund</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetPendingFundTransactionResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:GetPendingFundTransactionResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetPendingFundTransactionResponseType">
    <xsd:sequence>
      <xsd:element name="recordDetails" type="tns:recType" maxOccurs="unbounded" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Record</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="recType">
    <xsd:sequence>
      <xsd:element name="number" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="code" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="descriptionEnglish" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Description in English</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="descriptionArabic" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Description in Arabic</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="date" type="Q1:DateTimeOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="unitPrice" type="Q1:Amount" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Price of each unit</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="units" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Units</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetOrderHistoryRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="portfolioNumber" type="xsd:string" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Portfolio Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="action" type="xsd:string" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Action</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="fromDate" type="Q1:DateTimeOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>From Date</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="toDate" type="Q1:DateTimeOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>To Date</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="status" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="limit" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="orderNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Order Number</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetOrderHistoryResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:GetOrderHistoryResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetOrderHistoryResponseType">
    <xsd:sequence>
      <xsd:element name="orderDetails" type="tns:orderDetailsResponseType" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="orderDetailsResponseType">
    <xsd:sequence>
      <xsd:element name="branch" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Branch</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="action" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Action</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="code" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="orderNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="TransactionId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Transaction ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="esisId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="time" type="Q1:DateTimeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="portfolioNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Portfolio Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="accountNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Account Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="pending" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="filled" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="price" type="Q1:Amount" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="status" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="expiryDate" type="Q1:DateTimeOptional" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Expiry Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="notes" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="orderType" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Type of Order</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="tifType" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetInvestmentFundProfileRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountId" type="Q1:AccountId" maxOccurs="1" minOccurs="1">
          <xsd:annotation>
            <xsd:documentation>Account ID for Fund List</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetInvestmentFundProfileResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="tns:GetInvestmentFundProfileResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetInvestmentFundProfileResponseType">
    <xsd:sequence>
      <xsd:element name="funds" type="tns:fundType" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Funds for given Account number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="fundType">
    <xsd:sequence>
      <xsd:element name="fund" type="xsd:string" maxOccurs="unbounded" minOccurs="1">
        <xsd:annotation>
          <xsd:documentation>Each Fund number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ChangeInvestmentAddressRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="address1" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="address2" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="city" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="email" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="workFax" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="homeFax" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="workPhone" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="mobile" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="pager" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="homePhone" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="poBox" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="postCode" type="xsd:string" minOccurs="0" maxOccurs="1"/>
        <xsd:element name="source" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="ChangeInvestmentAddressResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CreateCustomerRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="customerInformation" type="tns:customerInfoType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="portfolioInformation" type="tns:portfolioInformationType" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CreateCustomerResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="customerInformation" type="tns:customerInfoResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="UpdateCustomerRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="customerInformation" type="tns:customerInfoType" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="UpdateCustomerResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="customerInformation" type="tns:customerInfoResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="customerInfoType">
    <xsd:sequence>
      <xsd:element name="employeeId" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="cif" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="salutation" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="firstName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="secondName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="thirdName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="lastName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="address1" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="address2" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="city" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="postalCode" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="poBox" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="email" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="homePhone" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="workPhone" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="mobilePhone" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="fax" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="country" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="currency" type="Q1:ISOCurrencyCodeOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="nationality" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="gender" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="investmentHorizon" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="language" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="agencyCode" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="branchCode" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="dateOfBirth" type="Q1:MWDateOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="employerName" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="idType" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="idValue" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="idExpiry" type="Q1:MWDateOptional" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="riskLevel" type="xsd:string" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="status" type="xsd:string" minOccurs="0" maxOccurs="1"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="accountInfoType">
    <xsd:sequence>
      <xsd:element name="portfolioCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="portfolioName" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="portfolioCurrency" type="Q1:ISOCurrencyCodeOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="bankAccountNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="customerInfoResponseType">
    <xsd:sequence>
      <xsd:element name="cif" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="portfolioCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="OpenIntegratedAccountRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="cif" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="debittedAccount" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="amount" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="answers" type="tns:answersType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="totalPoints" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="riskCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="branch" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="fundCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="fundCurrency" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="baseAmount" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="treasuryRate" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="OpenIntegratedAccountResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="accountInformation" type="tns:accountInformationResponseType" maxOccurs="1" minOccurs="0"/>
        <xsd:element ref="fault:fault" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="answersType">
    <xsd:sequence>
      <xsd:element name="answer" type="xsd:string" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="accountInformationResponseType">
    <xsd:sequence>
      <xsd:element name="accountNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="fundCode" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="tradeDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="valueDate" type="Q1:MWDateOptional" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="journal" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="balance" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="referenceNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="time" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      <xsd:element name="currency" type="xsd:string" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="portfolioInformationType">
    <xsd:sequence>
      <xsd:element name="account" type="tns:accountInfoType" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PTYINDType">
    <xsd:sequence>
      <xsd:element name="DateOfBirth" type="Q1:DateFormat" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="CustomerPortfolioRegistrationRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="nationalId" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>nationalId</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="shortCif" type="Q1:ShortCIFOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>shortCif</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="operatorId" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>operatorId</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="customerRegistrationtype" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>customerRegistrationtype</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="mode" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>mode</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="comments" type="xsd:string" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>comments</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CustomerPortfolioRegistrationResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="xsd:string"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="IVRTermsAndConditionsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="ecnId" type="Q1:ShortCIF" maxOccurs="1" minOccurs="1"/>
        <xsd:element name="language" type="xsd:string" maxOccurs="1" minOccurs="1"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="IVRTermsAndConditionsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:IVRTermsAndConditionsResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="IVRTermsAndConditionsResponseType">
    <xsd:sequence>
      <xsd:element name="ecnId" maxOccurs="1" minOccurs="0" type="Q1:ShortCIFOptional"/>
      <xsd:element name="TAndCKey" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="language" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="pdfFileName" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="TAndCActivationDate" maxOccurs="1" minOccurs="0" type="xsd:date"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="UpdateOccupationListRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="occupationList" type="tns:OccupationListType" maxOccurs="unbounded" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="UpdateOccupationListResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:UpdateOccupationListResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="OccupationListType">
    <xsd:sequence>
      <xsd:element name="occupationCode" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="employmentStatus" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="descriptionEnglish" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="descriptionArabic" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="pepFlag" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="highRiskFlag" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="UpdateOccupationListResponseType">
    <xsd:sequence>
      <xsd:element name="occupationList" type="tns:OccupationListType" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="UpdateSharesPledgingRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="collateralId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="pledgeFlag" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="pledgeId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="headerData" type="tns:SharesPledgingHeaderDataType" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="pledgingDetails" type="tns:pledgingDetailsRequestType" maxOccurs="unbounded" minOccurs="0"/>
        <xsd:element name="pledgeRequestorCIF" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="UpdateSharesPledgingResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:SharesPledgingResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="SharesPledgingHeaderDataType">
    <xsd:sequence>
      <xsd:element name="bankId" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="branchId" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="channel" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="messageDateTime" maxOccurs="1" minOccurs="0" type="xsd:dateTime"/>
      <xsd:element name="messageId" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="requestDateTime" maxOccurs="1" minOccurs="0" type="xsd:dateTime"/>
      <xsd:element name="requestId" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="userId" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="pledgingDetailsRequestType">
    <xsd:sequence>
      <xsd:element name="csdNumber" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="pledgeType" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="shareBlockList" maxOccurs="unbounded" minOccurs="0" type="tns:shareBlockListRequestType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="shareBlockListRequestType">
    <xsd:sequence>
      <xsd:element name="stockCode" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="stockQuantity" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SharesPledgingResponseType">
    <xsd:sequence>
      <xsd:element name="collateralId" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="pledgeId" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="messageDateTime" maxOccurs="1" minOccurs="0" type="xsd:dateTime"/>
      <xsd:element name="messageId" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="operationNumber" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="requestDateTime" maxOccurs="1" minOccurs="0" type="xsd:dateTime"/>
      <xsd:element name="requestId" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="resultCode" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="resultDescription" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="statusCode" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="InquireSharePledgeDetailsRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="collateralId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="pledgeId" type="xsd:string" maxOccurs="1" minOccurs="0"/>
        <xsd:element name="headerData" type="tns:SharesPledgingHeaderDataType" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="InquireSharePledgeDetailsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:InquireSharePledgeDetailsResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="InquireSharePledgeDetailsResponseType">
    <xsd:sequence>
      <xsd:element name="collateralId" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="messageDateTime" maxOccurs="1" minOccurs="0" type="xsd:dateTime"/>
      <xsd:element name="messageId" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="operationNumber" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="requestDateTime" maxOccurs="1" minOccurs="0" type="xsd:dateTime"/>
      <xsd:element name="requestId" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="resultCode" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="resultDescription" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="statusCode" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="inquiryList" maxOccurs="unbounded" minOccurs="0" type="tns:inquiryListType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="inquiryListType">
    <xsd:sequence>
      <xsd:element name="inquiryPledgeList" maxOccurs="unbounded" minOccurs="0" type="tns:inquiryPledgeListType"/>
      <xsd:element name="pledgeFlag" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="pledgeId" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="requestStatus" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="inquiryPledgeListType">
    <xsd:sequence>
      <xsd:element name="csdNumber" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="pledgeStatus" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="pledgeType" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="stockCode" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="stockQuantity" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="NCBCRiskProfileRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="nin" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="NCBCRiskProfileResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:NCBCRiskProfileResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="NCBCRiskProfileResponseType">
    <xsd:sequence>
      <xsd:element name="nin" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="clientRiskClassification" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="clientQuestionnaireClassification" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ClassificationQuestionnaireRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="customerType" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="J"/>
              <xsd:enumeration value="P"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="ClassificationQuestionnaireResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:ClassificationQuestionnaireResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="ClassificationQuestionnaireResponseType">
    <xsd:sequence>
      <xsd:element name="customerType" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="J"/>
            <xsd:enumeration value="P"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="questionList" maxOccurs="unbounded" minOccurs="0" type="tns:QuestionListType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="QuestionListType">
    <xsd:sequence>
      <xsd:element name="questionCode" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="questionDescriptionEnglish" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="questionDescriptionArabic" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="answerType" maxOccurs="1" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Answer Type can have below values:
                                                     -RB
                                                     -FT
                                                     -AT
                                                     -DT
                                                     -NO
                                                     -NT
-MS</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="answerList" maxOccurs="unbounded" minOccurs="0" type="tns:AnswerListType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AnswerListType">
    <xsd:sequence>
      <xsd:element name="answerCode" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <!--<xsd:element name="answerWeight" maxOccurs="1" minOccurs="0" type="xsd:string" />-->
      <xsd:element name="answerDescriptionEnglish" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="answerDescriptionArabic" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="RiskQuestionnaireRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="customerType" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="J"/>
              <xsd:enumeration value="P"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="RiskQuestionnaireResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:RiskQuestionnaireResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="RiskQuestionnaireResponseType">
    <xsd:sequence>
      <xsd:element name="customerType" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="J"/>
            <xsd:enumeration value="P"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="questionList" maxOccurs="unbounded" minOccurs="0" type="tns:QuestionListType"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="CustomerClassificationRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="customerType" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="J"/>
              <xsd:enumeration value="P"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="totalAnswerWeight" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CustomerClassificationResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:CustomerClassificationResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="CustomerClassificationResponseType">
    <xsd:sequence>
      <xsd:element name="customerType" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="J"/>
            <xsd:enumeration value="P"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="customerClassificationCode" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="RET"/>
            <xsd:enumeration value="QLF"/>
            <xsd:enumeration value="INC"/>
            <xsd:enumeration value="ICP"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="CustomerRiskProfileRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="customerType" maxOccurs="1" minOccurs="0">
          <xsd:simpleType>
            <xsd:restriction base="xsd:string">
              <xsd:enumeration value="J"/>
              <xsd:enumeration value="P"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="totalAnswerWeight" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CustomerRiskProfileResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:CustomerRiskProfileResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="CustomerRiskProfileResponseType">
    <xsd:sequence>
      <xsd:element name="customerType" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="J"/>
            <xsd:enumeration value="P"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="customerRiskProfile" maxOccurs="1" minOccurs="0">
        <xsd:simpleType>
          <xsd:restriction base="xsd:string">
            <xsd:enumeration value="09"/>
            <xsd:enumeration value="10"/>
            <xsd:enumeration value="11"/>
          </xsd:restriction>
        </xsd:simpleType>
      </xsd:element>
      <xsd:element name="riskProfileDescEnglish" maxOccurs="1" minOccurs="0" type="xsd:string"/>
      <xsd:element name="riskProfileDescArabic" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetCustomerClassificationAndRiskProfileRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="ecnId" type="Q1:ShortCIFOptional" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Short Cif</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="classificationAnswers" type="tns:ClassificationAnswersType" maxOccurs="1" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Classification Answers</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="riskAnswers" type="tns:RiskAnswersType" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>Risk Answers</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="ClassificationAnswersType">
    <xsd:sequence>
      <xsd:element name="answer" type="tns:ClassificationAnswersListType" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ClassificationAnswersListType">
    <xsd:sequence>
      <xsd:element name="questionCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Question Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="answerCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Answer Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="RiskAnswersType">
    <xsd:sequence>
      <xsd:element name="answer" type="tns:RiskAnswersListType" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="RiskAnswersListType">
    <xsd:sequence>
      <xsd:element name="questionCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Question Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="answerCode" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Answer Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GetCustomerClassificationAndRiskProfileResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:GetCustomerClassificationAndRiskProfileResponseType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="GetCustomerClassificationAndRiskProfileResponseType">
    <xsd:sequence>
      <xsd:element name="customerClassificationCode" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Customer Classification Code : can have below
						values
						-RET
						-QLF
						-INC
						-ICP</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="classificationDescriptionEnglish" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Classification Description inEnglish</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="classificationDescriptionArabic" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Classification Description in Arabic</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="riskProfileCode" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Risk Profile Code: can have below values
						-09
						-10
						-11</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="riskScore" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Risk Score</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="riskProfileDescriptionEnglish" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Risk Profile Description in English</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="riskProfileDescriptionArabic" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Risk Profile Description in Arabic</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="CheckCustomerComplianceRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="idNumber" type="xsd:string" maxOccurs="1" minOccurs="0"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="CheckCustomerComplianceResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="success" maxOccurs="1" minOccurs="0" type="tns:CheckCustomerComplianceType"/>
        <xsd:element maxOccurs="1" minOccurs="0" ref="fault:fault"/>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="CheckCustomerComplianceType">
    <xsd:sequence>
      <xsd:element name="idNumber" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>customer id</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="blackListFlag" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Black List Flag</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cashBlockFlag" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Cash Block Flag</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="operativeBlockFlag" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Operative Block Flag</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="heirBlockFlag" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Heir Block Flag</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="pepFlag" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Pep Flag</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="highRiskFlag" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>High Risk Flag</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
</xsd:schema>